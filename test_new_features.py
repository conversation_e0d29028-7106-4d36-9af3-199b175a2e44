#!/usr/bin/env python3
"""
测试新功能
"""
import sys
import os
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from raw_data_manager import RawDataManager
from case_collector import CaseCollector
from models import CaseSummary


def test_raw_data_manager():
    """测试原始数据管理器"""
    print("=" * 60)
    print("测试原始数据管理器")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_dir = config.RAW_DATA_DIR
    config.RAW_DATA_DIR = temp_dir
    config.SAVE_RAW_DATA = True
    config.PACK_RAW_DATA = True
    
    try:
        # 创建管理器实例
        manager = RawDataManager()
        
        print(f"✅ 创建原始数据管理器成功")
        print(f"   会话ID: {manager.session_id}")
        print(f"   数据目录: {manager.raw_data_dir}")
        print(f"   打包模式: {manager.pack_data}")
        
        # 测试保存各种数据
        test_cases_data = {
            "code": 200,
            "message": "success",
            "data": {
                "data": [{"id": 123, "subject": "测试工单"}],
                "total": 1
            }
        }
        
        manager.save_cases_list_response(1, test_cases_data, "5")
        print("✅ 保存工单列表响应数据")
        
        test_detail_data = {
            "summary": {"id": 123, "subject": "测试工单"},
            "comments": [],
            "attachments": []
        }
        
        manager.save_case_detail_response(123, test_detail_data)
        print("✅ 保存工单详情响应数据")
        
        manager.save_html_page(123, "<html><body>测试页面</body></html>", "detail")
        print("✅ 保存HTML页面")
        
        manager.save_api_request_info(
            "https://test.com/api", 
            "POST", 
            {"Content-Type": "application/json"}, 
            {"test": "data"},
            200
        )
        print("✅ 保存API请求信息")
        
        # 完成会话
        manager.finalize_session()
        print("✅ 完成会话并打包数据")
        
        # 检查生成的文件
        zip_files = [f for f in os.listdir(temp_dir) if f.endswith('.zip')]
        if zip_files:
            print(f"✅ 生成ZIP文件: {zip_files[0]}")
        else:
            print("❌ 未生成ZIP文件")
        
        # 获取摘要
        summary = manager.get_session_summary()
        print("📊 会话摘要:")
        for key, value in summary.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)
        config.RAW_DATA_DIR = original_dir
        config.SAVE_RAW_DATA = False


def test_collection_modes():
    """测试采集模式配置"""
    print("\n" + "=" * 60)
    print("测试采集模式配置")
    print("=" * 60)
    
    # 保存原始配置
    original_configs = {
        'SPECIFIC_CASE_IDS': config.SPECIFIC_CASE_IDS,
        'LATEST_N_CASES': config.LATEST_N_CASES,
        'INCREMENTAL_N_CASES': config.INCREMENTAL_N_CASES,
        'COLLECT_BOTH_STATUS': config.COLLECT_BOTH_STATUS,
        'CASE_STATUS_OPEN': config.CASE_STATUS_OPEN,
        'CASE_STATUS_CLOSED': config.CASE_STATUS_CLOSED
    }
    
    try:
        # 测试指定工单ID模式
        config.SPECIFIC_CASE_IDS = [5766, 7514]
        print(f"✅ 指定工单ID模式: {config.SPECIFIC_CASE_IDS}")
        
        # 测试最新N条模式
        config.LATEST_N_CASES = 10
        print(f"✅ 最新N条模式: {config.LATEST_N_CASES}")
        
        # 测试增量N条模式
        config.INCREMENTAL_N_CASES = 5
        print(f"✅ 增量N条模式: {config.INCREMENTAL_N_CASES}")
        
        # 测试工单状态配置
        config.COLLECT_BOTH_STATUS = True
        print(f"✅ 采集两种状态: {config.COLLECT_BOTH_STATUS}")
        print(f"   Open状态: {config.CASE_STATUS_OPEN}")
        print(f"   Closed状态: {config.CASE_STATUS_CLOSED}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        # 恢复原始配置
        for key, value in original_configs.items():
            setattr(config, key, value)


def test_case_filtering():
    """测试工单过滤逻辑"""
    print("\n" + "=" * 60)
    print("测试工单过滤逻辑")
    print("=" * 60)
    
    try:
        collector = CaseCollector()
        
        # 创建测试工单
        test_case = CaseSummary(
            id=123,
            case_number="TEST001",
            subject="测试工单",
            product_line="TEST",
            product_name="测试产品",
            project_name="测试项目",
            cnum="TEST",
            product_code="T001",
            case_level=0,
            issue_type=1,
            issue_description="测试描述",
            attach=None,
            priority=4,
            version="v1.0",
            notify=None,
            status=5,
            uid=1,
            deadline=None,
            is_admin_create=0,
            created_at="2025-01-01 00:00:00",
            updated_at="2025-01-01 00:00:00",
            company_name="测试公司",
            username="测试用户"
        )
        
        # 测试过滤逻辑
        should_skip = collector._should_skip_case_by_filters(test_case)
        print(f"✅ 工单过滤测试: should_skip = {should_skip}")
        
        # 测试停止条件
        config.LATEST_N_CASES = 5
        should_stop = collector._should_stop_collection(5)
        print(f"✅ 停止条件测试: should_stop = {should_stop}")
        
        config.LATEST_N_CASES = 0
        should_stop = collector._should_stop_collection(5)
        print(f"✅ 无限制条件测试: should_stop = {should_stop}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_directory_optimization():
    """测试目录创建优化"""
    print("\n" + "=" * 60)
    print("测试目录创建优化")
    print("=" * 60)
    
    try:
        collector = CaseCollector()
        
        # 创建测试工单详情（无图片无附件）
        test_case = CaseSummary(
            id=123,
            case_number="TEST001",
            subject="测试工单",
            product_line="TEST",
            product_name="测试产品",
            project_name="测试项目",
            cnum="TEST",
            product_code="T001",
            case_level=0,
            issue_type=1,
            issue_description="这是一个没有图片的测试描述",
            attach=None,
            priority=4,
            version="v1.0",
            notify=None,
            status=5,
            uid=1,
            deadline=None,
            is_admin_create=0,
            created_at="2025-01-01 00:00:00",
            updated_at="2025-01-01 00:00:00",
            company_name="测试公司",
            username="测试用户"
        )
        
        from models import CaseDetail
        case_detail = CaseDetail(summary=test_case)
        case_detail.comments = []
        case_detail.attachments = []
        
        # 测试是否有图片内容
        has_images = collector._has_images_in_content(case_detail)
        print(f"✅ 无图片内容检测: has_images = {has_images}")
        
        # 测试有图片的情况
        case_detail.summary.issue_description = "这是包含图片的描述 <img src='test.jpg'>"
        has_images = collector._has_images_in_content(case_detail)
        print(f"✅ 有图片内容检测: has_images = {has_images}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_configuration_display():
    """测试配置显示"""
    print("\n" + "=" * 60)
    print("测试配置显示")
    print("=" * 60)
    
    try:
        # 模拟不同的配置组合
        test_configs = [
            {
                'SPECIFIC_CASE_IDS': [5766, 7514],
                'LATEST_N_CASES': 0,
                'INCREMENTAL_N_CASES': 0,
                'COLLECT_BOTH_STATUS': True,
                'SAVE_RAW_DATA': True,
                'PACK_RAW_DATA': True
            },
            {
                'SPECIFIC_CASE_IDS': [],
                'LATEST_N_CASES': 10,
                'INCREMENTAL_N_CASES': 0,
                'COLLECT_BOTH_STATUS': False,
                'SAVE_RAW_DATA': False,
                'PACK_RAW_DATA': False
            },
            {
                'SPECIFIC_CASE_IDS': [],
                'LATEST_N_CASES': 0,
                'INCREMENTAL_N_CASES': 5,
                'COLLECT_BOTH_STATUS': True,
                'SAVE_RAW_DATA': True,
                'PACK_RAW_DATA': False
            }
        ]
        
        for i, test_config in enumerate(test_configs, 1):
            print(f"\n配置组合 {i}:")
            for key, value in test_config.items():
                print(f"   {key}: {value}")
            
            # 模拟配置显示逻辑
            if test_config['SPECIFIC_CASE_IDS']:
                print(f"   采集模式: 指定工单ID - {test_config['SPECIFIC_CASE_IDS']}")
            elif test_config['LATEST_N_CASES'] > 0:
                print(f"   采集模式: 最新 {test_config['LATEST_N_CASES']} 条工单")
            elif test_config['INCREMENTAL_N_CASES'] > 0:
                print(f"   采集模式: 增量 {test_config['INCREMENTAL_N_CASES']} 条工单")
            else:
                print("   采集模式: 全量采集")
            
            if test_config['COLLECT_BOTH_STATUS']:
                print(f"   工单状态: Open({config.CASE_STATUS_OPEN}) + Closed({config.CASE_STATUS_CLOSED})")
            else:
                print(f"   工单状态: 仅Closed({config.CASE_STATUS_CLOSED})")
            
            if test_config['SAVE_RAW_DATA']:
                pack_mode = "打包模式" if test_config['PACK_RAW_DATA'] else "分别保存模式"
                print(f"   原始数据: 启用 - {pack_mode}")
            else:
                print("   原始数据: 禁用")
        
        print("\n✅ 配置显示测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("新功能测试程序")
    print("=" * 60)
    
    tests = [
        ("原始数据管理器", test_raw_data_manager),
        ("采集模式配置", test_collection_modes),
        ("工单过滤逻辑", test_case_filtering),
        ("目录创建优化", test_directory_optimization),
        ("配置显示", test_configuration_display)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！新功能工作正常")
    else:
        print("⚠️  部分测试失败，需要检查相关功能")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
