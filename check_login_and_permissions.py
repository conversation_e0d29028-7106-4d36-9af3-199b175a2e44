#!/usr/bin/env python3
"""
检查登录状态和权限问题
验证是否能正确访问工单5766的附件
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from case_collector import CaseCollector
from utils import logger


def check_login_status():
    """检查登录状态"""
    print("=" * 80)
    print("检查登录状态")
    print("=" * 80)
    
    try:
        # 创建工单采集器
        with CaseCollector() as collector:
            print(f"✅ 工单采集器创建成功")
            
            # 检查是否已登录
            print(f"\n🔍 检查登录状态...")
            
            # 访问主页
            collector.driver.get("https://akrocare.akrostar-tech.com/")
            import time
            time.sleep(3)
            
            page_source = collector.driver.page_source
            
            # 检查登录状态的指标
            login_indicators = [
                ("登录页面", "login" in page_source.lower()),
                ("用户名输入", "username" in page_source.lower()),
                ("密码输入", "password" in page_source.lower()),
                ("登录按钮", "sign in" in page_source.lower() or "login" in page_source.lower()),
                ("已登录用户", "logout" in page_source.lower() or "sign out" in page_source.lower()),
                ("工单列表", "case" in page_source.lower()),
            ]
            
            print(f"登录状态检查:")
            for indicator, found in login_indicators:
                status = "✅ 是" if found else "❌ 否"
                print(f"   {indicator}: {status}")
            
            # 尝试访问工单列表页面
            print(f"\n🔍 尝试访问工单列表...")
            collector.driver.get("https://akrocare.akrostar-tech.com/case")
            time.sleep(3)
            
            list_page_source = collector.driver.page_source
            
            # 检查工单列表页面
            list_indicators = [
                ("工单表格", "<table" in list_page_source),
                ("工单ID", "5766" in list_page_source),
                ("分页", "pagination" in list_page_source.lower()),
                ("搜索框", "search" in list_page_source.lower()),
            ]
            
            print(f"工单列表页面检查:")
            for indicator, found in list_indicators:
                status = "✅ 是" if found else "❌ 否"
                print(f"   {indicator}: {status}")
            
            # 直接尝试访问工单5766
            print(f"\n🔍 直接访问工单5766...")
            detail_url = config.CASE_DETAIL_URL_TEMPLATE.format(5766)
            print(f"访问URL: {detail_url}")
            
            collector.driver.get(detail_url)
            time.sleep(5)
            
            detail_page_source = collector.driver.page_source
            
            # 检查工单详情页面
            detail_indicators = [
                ("页面标题包含5766", "5766" in collector.driver.title),
                ("页面内容包含5766", "5766" in detail_page_source),
                ("错误页面", "error" in detail_page_source.lower() or "404" in detail_page_source),
                ("权限拒绝", "access denied" in detail_page_source.lower() or "permission" in detail_page_source.lower()),
                ("需要登录", "login" in detail_page_source.lower()),
                ("工单内容", "case" in detail_page_source.lower()),
                ("附件区域", "attachment" in detail_page_source.lower()),
            ]
            
            print(f"工单5766详情页面检查:")
            for indicator, found in detail_indicators:
                status = "✅ 是" if found else "❌ 否"
                print(f"   {indicator}: {status}")
            
            # 保存页面内容用于分析
            temp_dir = tempfile.mkdtemp()
            
            main_page_file = os.path.join(temp_dir, "main_page.html")
            with open(main_page_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            list_page_file = os.path.join(temp_dir, "list_page.html")
            with open(list_page_file, 'w', encoding='utf-8') as f:
                f.write(list_page_source)
            
            detail_page_file = os.path.join(temp_dir, "detail_page.html")
            with open(detail_page_file, 'w', encoding='utf-8') as f:
                f.write(detail_page_source)
            
            print(f"\n📁 页面内容已保存到: {temp_dir}")
            print(f"   - main_page.html: 主页内容")
            print(f"   - list_page.html: 工单列表页面")
            print(f"   - detail_page.html: 工单5766详情页面")
            
            # 分析页面内容
            print(f"\n📊 页面内容分析:")
            print(f"   主页大小: {len(page_source)} 字符")
            print(f"   列表页大小: {len(list_page_source)} 字符")
            print(f"   详情页大小: {len(detail_page_source)} 字符")
            
            # 查找关键信息
            if "5766" in detail_page_source:
                print(f"   ✅ 详情页包含工单5766信息")
            else:
                print(f"   ❌ 详情页不包含工单5766信息")
            
            # 检查是否有重定向到登录页面
            current_url = collector.driver.current_url
            print(f"   当前URL: {current_url}")
            
            if "login" in current_url.lower():
                print(f"   ⚠️  页面被重定向到登录页面")
                return False, "需要登录"
            elif "error" in current_url.lower() or "404" in current_url.lower():
                print(f"   ⚠️  页面显示错误")
                return False, "页面错误"
            else:
                print(f"   ✅ 页面访问正常")
                return True, temp_dir
    
    except Exception as e:
        print(f"❌ 检查登录状态时发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False, str(e)


def test_different_case_ids():
    """测试不同的工单ID"""
    print("\n" + "=" * 80)
    print("测试不同的工单ID")
    print("=" * 80)
    
    try:
        # 创建工单采集器
        with CaseCollector() as collector:
            # 测试几个不同的工单ID
            test_case_ids = [5766, 5765, 5767, 1000, 2000]
            
            for case_id in test_case_ids:
                print(f"\n🔍 测试工单 {case_id}...")
                
                detail_url = config.CASE_DETAIL_URL_TEMPLATE.format(case_id)
                collector.driver.get(detail_url)
                
                import time
                time.sleep(3)
                
                page_source = collector.driver.page_source
                current_url = collector.driver.current_url
                
                # 检查页面状态
                indicators = [
                    ("包含工单ID", str(case_id) in page_source),
                    ("包含附件", "attachment" in page_source.lower()),
                    ("包含DDRPHY", "DDRPHY" in page_source),
                    ("页面大小", len(page_source)),
                    ("当前URL", current_url),
                ]
                
                print(f"   工单 {case_id} 检查结果:")
                for indicator, value in indicators:
                    if isinstance(value, bool):
                        status = "✅ 是" if value else "❌ 否"
                        print(f"     {indicator}: {status}")
                    else:
                        print(f"     {indicator}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试不同工单ID时发生异常: {e}")
        return False


def main():
    """主函数"""
    print("🔍 登录状态和权限检查")
    
    tests = [
        ("检查登录状态", check_login_status),
        ("测试不同工单ID", test_different_case_ids),
    ]
    
    results = []
    temp_dir = None
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_name == "检查登录状态":
                result, info = test_func()
                if result and isinstance(info, str) and os.path.exists(info):
                    temp_dir = info
            else:
                result = test_func()
            
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 完成")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("检查结果总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试完成")
    
    if temp_dir:
        print(f"\n📁 页面内容保存在: {temp_dir}")
        print(f"可以查看这些文件来分析具体问题")
    
    print(f"\n📋 可能的原因:")
    print(f"1. 工单5766可能不存在或已被删除")
    print(f"2. 需要特殊权限才能访问工单5766")
    print(f"3. 工单5766没有附件")
    print(f"4. 附件需要特殊的访问方式")
    print(f"5. 登录状态有问题")
    
    return passed > 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
