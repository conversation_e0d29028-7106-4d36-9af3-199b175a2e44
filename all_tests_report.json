{"summary": {"start_time": "2025-05-29T23:05:05.933301", "end_time": "2025-05-29T23:05:08.309715", "total_duration": "0:00:02.376414", "total_tests": 5, "passed_tests": 4, "failed_tests": 1, "required_failures": 0, "success_rate": "80.0%"}, "test_results": [{"script": "test_data_generator.py", "description": "测试数据生成器", "success": true, "duration": "0.25s", "return_code": 0, "stdout": "============================================================\n测试数据生成器\n============================================================\n\n✅ 测试数据生成完成!\n📁 数据目录: test_data_complete\n📁 附件目录: test_data_complete\\attachments\n📊 基础工单: 5 个\n📊 增量工单: 5 个\n📎 附件文件: 8 个\n\n📋 生成的文件:\n  - attachment_manifest.json (2791 bytes)\n  - base_dataset.json (10485 bytes)\n  - incremental_dataset.json (10611 bytes)\n  - test_data_summary.json (1089 bytes)\n\n📎 附件文件:\n  - data_1002_1.csv (141 bytes, 文档)\n  - data_1003_2.csv (141 bytes, 文档)\n  - data_1008_2.csv (141 bytes, 文档)\n  - document_1001.txt (201 bytes, 文档)\n  - document_1002.txt (201 bytes, 文档)\n  - document_1003.txt (201 bytes, 文档)\n  - document_1004.txt (201 bytes, 文档)\n  - document_1005.txt (201 bytes, 文档)\n  - document_1006.txt (201 bytes, 文档)\n  - document_1007.txt (201 bytes, 文档)\n  - document_1008.txt (201 bytes, 文档)\n  - image_1002.png (666 bytes, 图片)\n  - image_1003.png (669 bytes, 图片)\n  - image_1005.png (674 bytes, 图片)\n  - image_1007.png (660 bytes, 图片)\n  - image_1008.png (659 bytes, 图片)\n", "stderr": "2025-05-29 23:05:06,116 - INFO - 开始生成完整测试数据集...\n2025-05-29 23:05:06,117 - INFO - 生成基础数据集...\n2025-05-29 23:05:06,117 - INFO - 生成基础工单: 1001 - basic\n2025-05-29 23:05:06,158 - INFO - 生成基础工单: 1002 - with_images\n2025-05-29 23:05:06,160 - INFO - 生成基础工单: 1003 - complex\n2025-05-29 23:05:06,160 - INFO - 生成基础工单: 1004 - basic\n2025-05-29 23:05:06,162 - INFO - 生成基础工单: 1005 - with_images\n2025-05-29 23:05:06,163 - INFO - 数据已保存到: test_data_complete\\base_dataset.json\n2025-05-29 23:05:06,163 - INFO - 基础数据集已保存: test_data_complete\\base_dataset.json\n2025-05-29 23:05:06,163 - INFO - 生成增量数据集...\n2025-05-29 23:05:06,164 - INFO - 生成更新工单: 1002 - updated\n2025-05-29 23:05:06,164 - INFO - 生成新增工单: 1006 - basic\n2025-05-29 23:05:06,166 - INFO - 生成新增工单: 1007 - with_images\n2025-05-29 23:05:06,168 - INFO - 生成新增工单: 1008 - complex\n2025-05-29 23:05:06,169 - INFO - 数据已保存到: test_data_complete\\incremental_dataset.json\n2025-05-29 23:05:06,169 - INFO - 增量数据集已保存: test_data_complete\\incremental_dataset.json\n2025-05-29 23:05:06,170 - INFO - 数据已保存到: test_data_complete\\attachment_manifest.json\n2025-05-29 23:05:06,170 - INFO - 附件清单已保存: test_data_complete\\attachment_manifest.json\n2025-05-29 23:05:06,170 - INFO - 数据已保存到: test_data_complete\\test_data_summary.json\n2025-05-29 23:05:06,171 - INFO - 测试数据摘要已保存: test_data_complete\\test_data_summary.json\n"}, {"script": "test_data_modes.py", "description": "数据模式基础测试", "success": true, "duration": "0.38s", "return_code": 0, "stdout": "全量模式和增量模式功能测试\n============================================================\n\n🧪 运行测试: 全量模式\n============================================================\n测试全量模式\n============================================================\n测试全量模式 - 目录输出\n✅ 目录模式成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506\n   生成文件: ['attachments', 'metadata.json', 'patch_content.json']\n   模式: full\n   工单数量: 3\n   新增工单: 3\n\n测试全量模式 - ZIP输出\n✅ ZIP模式成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506.zip\n   ZIP内容: ['metadata.json', 'patch_content.json']\n✅ 全量模式 测试通过\n\n🧪 运行测试: 增量模式\n\n============================================================\n测试增量模式\n============================================================\n1. 创建基础数据\n✅ 基础数据创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\full_20250529_230506.zip\n\n2. 创建增量数据\n✅ 增量Patch创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\patches\\patch_20250529_230506.zip\n   模式: incremental\n   基础版本: 20250529_230506\n   新增工单: [1003]\n   更新工单: [1002]\n✅ 增量模式 测试通过\n\n🧪 运行测试: Patch应用器\n\n============================================================\n测试Patch应用器\n============================================================\n1. 测试Patch解析\n✅ Patch解析成功\n   Patch ID: test_patch_001\n   新增工单: 2\n\n2. 测试空目录应用\n✅ 空目录应用成功\n   结果工单数: 2\n\n3. 测试现有数据合并\n\nPatch应用摘要:\n  test_patch.zip: 新增 2 个，更新 0 个\n\n✅ 数据合并成功\n   合并后工单数: 4\n✅ Patch应用器 测试通过\n\n============================================================\n测试结果总结\n============================================================\n全量模式: ✅ 通过\n增量模式: ✅ 通过\nPatch应用器: ✅ 通过\n\n总计: 3/3 测试通过\n🎉 所有测试通过！全量模式和增量模式功能正常\n\n功能说明:\n1. ✅ 全量模式支持目录和ZIP两种输出\n2. ✅ 增量模式支持变更检测和Patch生成\n3. ✅ Patch应用器支持解析、应用和合并\n4. ✅ 数据格式兼容性良好\n5. ✅ 元数据管理完善\n", "stderr": "2025-05-29 23:05:06,375 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,375 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,376 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,379 - INFO - 全量模式数据目录已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506\n2025-05-29 23:05:06,386 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,387 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,387 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,403 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506.zip\n2025-05-29 23:05:06,404 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphzs15jg9\\full_20250529_230506.zip\n2025-05-29 23:05:06,414 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,415 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,416 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,432 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\full_20250529_230506.zip\n2025-05-29 23:05:06,433 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\full_20250529_230506.zip\n2025-05-29 23:05:06,433 - INFO - 开始创建增量模式Patch...\n2025-05-29 23:05:06,442 - INFO - 更新工单: 1002 - 基础工单2-更新\n2025-05-29 23:05:06,442 - INFO - 新增工单: 1003 - 新增工单3\n2025-05-29 23:05:06,443 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\patches\\patch_20250529_230506\\metadata.json\n2025-05-29 23:05:06,444 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\patches\\patch_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,460 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\patches\\patch_20250529_230506.zip\n2025-05-29 23:05:06,461 - INFO - 增量Patch已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm8n9vswb\\patches\\patch_20250529_230506.zip\n2025-05-29 23:05:06,472 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,472 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,473 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,489 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\full_20250529_230506.zip\n2025-05-29 23:05:06,490 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\full_20250529_230506.zip\n2025-05-29 23:05:06,491 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\test_patch\\metadata.json\n2025-05-29 23:05:06,492 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\test_patch\\patch_content.json\n2025-05-29 23:05:06,507 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\test_patch.zip\n2025-05-29 23:05:06,517 - INFO - 开始应用Patch到空目录...\n2025-05-29 23:05:06,517 - INFO - 处理Patch: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\test_patch.zip\n2025-05-29 23:05:06,517 - INFO - 添加新增工单: 2003\n2025-05-29 23:05:06,517 - INFO - 添加新增工单: 2004\n2025-05-29 23:05:06,518 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\empty_result.json\n2025-05-29 23:05:06,518 - INFO - 新增工单数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\empty_result.json\n2025-05-29 23:05:06,527 - INFO - 开始应用Patch到现有数据...\n2025-05-29 23:05:06,539 - INFO - 加载基础数据: 2 个工单\n2025-05-29 23:05:06,539 - INFO - 应用Patch: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\test_patch.zip\n2025-05-29 23:05:06,540 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\merge_result.json\n2025-05-29 23:05:06,540 - INFO - 合并数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp_p4u8_yp\\merge_result.json\n"}, {"script": "comprehensive_test_suite.py", "description": "综合测试套件", "success": true, "duration": "0.68s", "return_code": 0, "stdout": "================================================================================\n综合测试套件 - 全量模式和增量模式完整测试\n================================================================================\n\n忽略的更新工单:\n  - 工单 1002: 更新后的工单1002\n\n\nPatch应用摘要:\n  patch_20250529_230506.zip: 新增 3 个，更新 1 个\n\n\n================================================================================\n测试结果总结\n================================================================================\n总测试数: 6\n通过测试: 6\n失败测试: 0\n成功率: 100.0%\n结论: 所有测试通过\n\n详细结果:\n✅ 数据生成测试\n   生成5个基础工单，5个增量工单，16个附件\n✅ 附件处理测试\n   处理9个附件（3个图片），ZIP包含9个文件\n✅ 图片内容检测测试\n   工单描述图片: 3, 评论图片: 3\n✅ 全量模式测试\n   目录模式: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506, ZIP模式: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n✅ 增量模式测试\n   基础版本: 20250529_230506, 新增: 3, 更新: 1\n✅ Patch应用测试\n   解析1个Patch, 空目录提取3个工单, 合并后8个工单\n\n📄 详细测试报告已保存: comprehensive_test_report.json\n", "stderr": "2025-05-29 23:05:06,764 - INFO - ================================================================================\n2025-05-29 23:05:06,764 - INFO - 开始运行综合测试套件\n2025-05-29 23:05:06,764 - INFO - ================================================================================\n2025-05-29 23:05:06,764 - INFO - 开始测试数据生成...\n2025-05-29 23:05:06,764 - INFO - 开始生成完整测试数据集...\n2025-05-29 23:05:06,765 - INFO - 生成基础数据集...\n2025-05-29 23:05:06,765 - INFO - 生成基础工单: 1001 - basic\n2025-05-29 23:05:06,806 - INFO - 生成基础工单: 1002 - with_images\n2025-05-29 23:05:06,808 - INFO - 生成基础工单: 1003 - complex\n2025-05-29 23:05:06,809 - INFO - 生成基础工单: 1004 - basic\n2025-05-29 23:05:06,810 - INFO - 生成基础工单: 1005 - with_images\n2025-05-29 23:05:06,811 - INFO - 数据已保存到: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\base_dataset.json\n2025-05-29 23:05:06,811 - INFO - 基础数据集已保存: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\base_dataset.json\n2025-05-29 23:05:06,811 - INFO - 生成增量数据集...\n2025-05-29 23:05:06,812 - INFO - 生成更新工单: 1002 - updated\n2025-05-29 23:05:06,812 - INFO - 生成新增工单: 1006 - basic\n2025-05-29 23:05:06,814 - INFO - 生成新增工单: 1007 - with_images\n2025-05-29 23:05:06,816 - INFO - 生成新增工单: 1008 - complex\n2025-05-29 23:05:06,817 - INFO - 数据已保存到: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\incremental_dataset.json\n2025-05-29 23:05:06,817 - INFO - 增量数据集已保存: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\incremental_dataset.json\n2025-05-29 23:05:06,818 - INFO - 数据已保存到: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachment_manifest.json\n2025-05-29 23:05:06,818 - INFO - 附件清单已保存: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachment_manifest.json\n2025-05-29 23:05:06,818 - INFO - 数据已保存到: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\test_data_summary.json\n2025-05-29 23:05:06,818 - INFO - 测试数据摘要已保存: C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\test_data_summary.json\n2025-05-29 23:05:06,819 - INFO - 数据生成测试: ✅ 通过\n2025-05-29 23:05:06,819 - INFO -   详情: 生成5个基础工单，5个增量工单，16个附件\n2025-05-29 23:05:06,837 - INFO - 开始测试附件处理...\n2025-05-29 23:05:06,838 - INFO - 测试环境设置完成: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\n2025-05-29 23:05:06,860 - INFO - 测试数据包含9个附件，其中3个图片\n2025-05-29 23:05:06,860 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,861 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,862 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,939 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:06,942 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:06,953 - INFO - ZIP包中包含9个附件文件\n2025-05-29 23:05:06,953 - INFO - 附件分布在5个工单目录中\n2025-05-29 23:05:06,953 - INFO - 附件处理测试: ✅ 通过\n2025-05-29 23:05:06,953 - INFO -   详情: 处理9个附件（3个图片），ZIP包含9个文件\n2025-05-29 23:05:06,954 - INFO - 开始测试图片内容检测...\n2025-05-29 23:05:06,954 - INFO - 发现3个工单描述包含图片\n2025-05-29 23:05:06,954 - INFO - 发现3条评论包含图片\n2025-05-29 23:05:06,954 - INFO - 图片内容检测测试: ✅ 通过\n2025-05-29 23:05:06,954 - INFO -   详情: 工单描述图片: 3, 评论图片: 3\n2025-05-29 23:05:06,954 - INFO - 开始测试全量模式...\n2025-05-29 23:05:06,954 - INFO - 测试环境设置完成: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_oyyu75mr\n2025-05-29 23:05:06,954 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,955 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,956 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:06,966 - INFO - 全量模式数据目录已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\n2025-05-29 23:05:06,967 - INFO - 全量模式包含9个附件文件\n2025-05-29 23:05:06,967 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:06,968 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:06,969 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:07,001 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,003 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,012 - INFO - 全量模式测试: ✅ 通过\n2025-05-29 23:05:07,013 - INFO -   详情: 目录模式: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506, ZIP模式: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,013 - INFO - 开始测试增量模式...\n2025-05-29 23:05:07,013 - INFO - 测试环境设置完成: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_rb13mc6u\n2025-05-29 23:05:07,013 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:07,014 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:07,016 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:07,050 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,053 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,053 - INFO - 开始创建增量模式Patch...\n2025-05-29 23:05:07,063 - INFO - 更新工单: 1002 - 更新后的工单1002\n2025-05-29 23:05:07,063 - INFO - 新增工单: 1006 - 基础测试工单1006\n2025-05-29 23:05:07,063 - INFO - 新增工单: 1007 - 包含图片的工单1007\n2025-05-29 23:05:07,063 - INFO - 新增工单: 1008 - 复杂工单1008\n2025-05-29 23:05:07,064 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\patches\\patch_20250529_230506\\metadata.json\n2025-05-29 23:05:07,066 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\patches\\patch_20250529_230506\\patch_content.json\n2025-05-29 23:05:07,131 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\patches\\patch_20250529_230506.zip\n2025-05-29 23:05:07,134 - INFO - 增量Patch已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\patches\\patch_20250529_230506.zip\n2025-05-29 23:05:07,143 - INFO - 检测到3个新增工单: [1006, 1007, 1008]\n2025-05-29 23:05:07,143 - INFO - 检测到1个更新工单: [1002]\n2025-05-29 23:05:07,143 - INFO - 增量模式测试: ✅ 通过\n2025-05-29 23:05:07,143 - INFO -   详情: 基础版本: 20250529_230506, 新增: 3, 更新: 1\n2025-05-29 23:05:07,144 - INFO - 测试环境设置完成: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\n2025-05-29 23:05:07,144 - INFO - 开始创建全量模式数据包...\n2025-05-29 23:05:07,145 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\metadata.json\n2025-05-29 23:05:07,146 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506\\patch_content.json\n2025-05-29 23:05:07,183 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,185 - INFO - 全量模式数据包已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\full_20250529_230506.zip\n2025-05-29 23:05:07,185 - INFO - 开始测试Patch应用...\n2025-05-29 23:05:07,186 - INFO - 解析Patch: patch_20250529_230506\n2025-05-29 23:05:07,186 - INFO -   模式: incremental\n2025-05-29 23:05:07,186 - INFO -   新增工单: 3\n2025-05-29 23:05:07,186 - INFO -   更新工单: 1\n2025-05-29 23:05:07,186 - INFO - 开始应用Patch到空目录...\n2025-05-29 23:05:07,186 - INFO - 处理Patch: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\patches\\patch_20250529_230506.zip\n2025-05-29 23:05:07,186 - INFO - 添加新增工单: 1006\n2025-05-29 23:05:07,186 - INFO - 添加新增工单: 1007\n2025-05-29 23:05:07,187 - INFO - 添加新增工单: 1008\n2025-05-29 23:05:07,187 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\empty_result.json\n2025-05-29 23:05:07,187 - INFO - 新增工单数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\empty_result.json\n2025-05-29 23:05:07,196 - INFO - 空目录应用提取了3个新增工单\n2025-05-29 23:05:07,196 - INFO - 开始应用Patch到现有数据...\n2025-05-29 23:05:07,205 - INFO - 加载基础数据: 5 个工单\n2025-05-29 23:05:07,205 - INFO - 应用Patch: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_1fies3bs\\patches\\patch_20250529_230506.zip\n2025-05-29 23:05:07,207 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\merge_result.json\n2025-05-29 23:05:07,207 - INFO - 合并数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\merge_result.json\n2025-05-29 23:05:07,216 - INFO - 数据合并后共有8个工单\n2025-05-29 23:05:07,216 - INFO - 开始创建合并Patch...\n2025-05-29 23:05:07,218 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\repacked.zip_temp\\metadata.json\n2025-05-29 23:05:07,220 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\repacked.zip_temp\\patch_content.json\n2025-05-29 23:05:07,236 - INFO - ZIP包创建成功: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\repacked.zip\n2025-05-29 23:05:07,236 - INFO - 合并Patch已创建: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_lywcyf31\\repacked.zip\n2025-05-29 23:05:07,237 - INFO - Patch应用测试: ✅ 通过\n2025-05-29 23:05:07,237 - INFO -   详情: 解析1个Patch, 空目录提取3个工单, 合并后8个工单\n2025-05-29 23:05:07,239 - INFO - 数据已保存到: comprehensive_test_report.json\n"}, {"script": "test_collector.py", "description": "采集器基础测试", "success": false, "duration": "0.65s", "return_code": 1, "stdout": "\n从demo-content.html提取到 4 条评论:\n  评论1: AkroStar Reply: - 2025-03-05 10:14:35\n    内容: Hi Ya<PERSON>，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！...\n  评论2: AkroStar Reply: - 2024-12-10 18:42:23\n    内容: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...\n  评论3: wf w: - 2024-12-10 16:16:43\n    内容: HI,Aden:有任何进展，可以先同步给我们吗?...\n  评论4: AkroStar Reply: - 2024-12-05 20:39:03\n    内容: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！...\n", "stderr": "test_attachment_extraction (__main__.TestCaseCollector)\n测试附件提取 ... 2025-05-29 23:05:07,720 - INFO - 已禁用代理\n2025-05-29 23:05:07,721 - INFO - 工单 7514 提取到 1 个附件\nok\ntest_attachment_extraction_with_real_data (__main__.TestCaseCollector)\n使用真实数据测试附件提取 ... 2025-05-29 23:05:07,765 - INFO - 已禁用代理\n2025-05-29 23:05:07,766 - INFO - 工单 5766 没有附件\nok\ntest_case_detail_to_dict (__main__.TestCaseCollector)\n测试工单详情转换为字典 ... ok\ntest_case_summary_creation (__main__.TestCaseCollector)\n测试工单概要创建 ... ok\ntest_case_summary_with_real_data (__main__.TestCaseCollector)\n使用真实数据测试工单概要 ... ok\ntest_collector_initialization (__main__.TestCaseCollector)\n测试采集器初始化 ... 2025-05-29 23:05:07,777 - INFO - 已禁用代理\n2025-05-29 23:05:07,778 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe\n2025-05-29 23:05:07,778 - INFO - WebDriver初始化成功\nok\ntest_comment_extraction (__main__.TestCaseCollector)\n测试评论提取 ... 2025-05-29 23:05:07,780 - INFO - 已禁用代理\n2025-05-29 23:05:07,780 - INFO - 工单 7514 提取到 2 条评论\nok\ntest_comment_extraction_with_real_data (__main__.TestCaseCollector)\n使用真实数据测试评论提取 ... 2025-05-29 23:05:07,798 - INFO - 已禁用代理\n2025-05-29 23:05:07,802 - INFO - 工单 5766 提取到 4 条评论\nok\ntest_complete_case_detail_with_real_data (__main__.TestCaseCollector)\n使用真实数据测试完整工单详情 ... ok\ntest_csrf_token_extraction (__main__.TestCaseCollector)\n测试CSRF token提取功能 ... 2025-05-29 23:05:07,812 - INFO - 已禁用代理\nok\ntest_custom_driver_path (__main__.TestCaseCollector)\n测试自定义ChromeDriver路径 ... 2025-05-29 23:05:07,814 - INFO - 已禁用代理\n2025-05-29 23:05:07,814 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\\\Users\\\\<USER>\\\\AppData\\\\Local\\\\Temp\\\\tmp4wofeou7\\\\last_update.json'\n2025-05-29 23:05:07,814 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver\n2025-05-29 23:05:07,815 - INFO - WebDriver初始化成功\nok\ntest_extract_comments_fallback_method (__main__.TestCaseCollector)\n测试评论提取的降级方法 ... 2025-05-29 23:05:07,816 - INFO - 已禁用代理\n2025-05-29 23:05:07,816 - INFO - 工单 123 提取到 1 条评论\nok\ntest_extract_comments_from_demo_html (__main__.TestCaseCollector)\n测试从demo-content.html提取评论 ... 2025-05-29 23:05:07,828 - INFO - 已禁用代理\n2025-05-29 23:05:07,831 - INFO - 工单 5766 提取到 4 条评论\nok\ntest_get_cases_via_requests_structure (__main__.TestCaseCollector)\n测试requests方法的基本结构 ... 2025-05-29 23:05:07,832 - INFO - 已禁用代理\n2025-05-29 23:05:07,833 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'\n2025-05-29 23:05:07,872 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))\n2025-05-29 23:05:07,872 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases\nFAIL\ntest_get_cases_via_selenium (__main__.TestCaseCollector)\n测试通过Selenium获取工单数据 ... 2025-05-29 23:05:07,874 - INFO - 已禁用代理\nok\ntest_json_operations (__main__.TestCaseCollector)\n测试JSON操作 ... 2025-05-29 23:05:07,875 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpcdx8m0jp\\test.json\nok\ntest_safe_filename (__main__.TestCaseCollector)\n测试安全文件名生成 ... ok\ntest_session_headers_update (__main__.TestCaseCollector)\n测试session headers更新功能 ... 2025-05-29 23:05:07,885 - INFO - 已禁用代理\n2025-05-29 23:05:07,885 - INFO - Session headers和cookies已更新\nok\ntest_versioned_filename (__main__.TestCaseCollector)\n测试版本化文件名生成 ... ok\n\n======================================================================\nFAIL: test_get_cases_via_requests_structure (__main__.TestCaseCollector)\n测试requests方法的基本结构\n----------------------------------------------------------------------\nTraceback (most recent call last):\n  File \"test_collector.py\", line 256, in test_get_cases_via_requests_structure\n    self.assertIn('code', result)\nAssertionError: 'code' not found in {}\n\n----------------------------------------------------------------------\nRan 19 tests in 0.167s\n\nFAILED (failures=1)\n"}, {"script": "test_new_features.py", "description": "新功能测试", "success": true, "duration": "0.40s", "return_code": 0, "stdout": "新功能测试程序\n============================================================\n\n🧪 运行测试: 原始数据管理器\n============================================================\n测试原始数据管理器\n============================================================\n✅ 创建原始数据管理器成功\n   会话ID: 20250529_230508\n   数据目录: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\n   打包模式: True\n✅ 保存工单列表响应数据\n✅ 保存工单详情响应数据\n✅ 保存HTML页面\n✅ 保存API请求信息\n✅ 完成会话并打包数据\n✅ 生成ZIP文件: raw_data_20250529_230508.zip\n📊 会话摘要:\n   session_id: 20250529_230508\n   raw_data_enabled: True\n   pack_mode: True\n   raw_data_dir: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\n   packed_file: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\\raw_data_20250529_230508.zip\n✅ 原始数据管理器 测试通过\n\n🧪 运行测试: 采集模式配置\n\n============================================================\n测试采集模式配置\n============================================================\n✅ 指定工单ID模式: [5766, 7514]\n✅ 最新N条模式: 10\n✅ 增量N条模式: 5\n✅ 采集两种状态: True\n   Open状态: 4\n   Closed状态: 5\n✅ 采集模式配置 测试通过\n\n🧪 运行测试: 工单过滤逻辑\n\n============================================================\n测试工单过滤逻辑\n============================================================\n✅ 工单过滤测试: should_skip = False\n✅ 停止条件测试: should_stop = True\n✅ 无限制条件测试: should_stop = False\n✅ 工单过滤逻辑 测试通过\n\n🧪 运行测试: 目录创建优化\n\n============================================================\n测试目录创建优化\n============================================================\n✅ 无图片内容检测: has_images = False\n✅ 有图片内容检测: has_images = True\n✅ 目录创建优化 测试通过\n\n🧪 运行测试: 配置显示\n\n============================================================\n测试配置显示\n============================================================\n\n配置组合 1:\n   SPECIFIC_CASE_IDS: [5766, 7514]\n   LATEST_N_CASES: 0\n   INCREMENTAL_N_CASES: 0\n   COLLECT_BOTH_STATUS: True\n   SAVE_RAW_DATA: True\n   PACK_RAW_DATA: True\n   采集模式: 指定工单ID - [5766, 7514]\n   工单状态: Open(4) + Closed(5)\n   原始数据: 启用 - 打包模式\n\n配置组合 2:\n   SPECIFIC_CASE_IDS: []\n   LATEST_N_CASES: 10\n   INCREMENTAL_N_CASES: 0\n   COLLECT_BOTH_STATUS: False\n   SAVE_RAW_DATA: False\n   PACK_RAW_DATA: False\n   采集模式: 最新 10 条工单\n   工单状态: 仅Closed(5)\n   原始数据: 禁用\n\n配置组合 3:\n   SPECIFIC_CASE_IDS: []\n   LATEST_N_CASES: 0\n   INCREMENTAL_N_CASES: 5\n   COLLECT_BOTH_STATUS: True\n   SAVE_RAW_DATA: True\n   PACK_RAW_DATA: False\n   采集模式: 增量 5 条工单\n   工单状态: Open(4) + Closed(5)\n   原始数据: 启用 - 分别保存模式\n\n✅ 配置显示测试完成\n✅ 配置显示 测试通过\n\n============================================================\n测试结果总结\n============================================================\n原始数据管理器: ✅ 通过\n采集模式配置: ✅ 通过\n工单过滤逻辑: ✅ 通过\n目录创建优化: ✅ 通过\n配置显示: ✅ 通过\n\n总计: 5/5 测试通过\n🎉 所有测试通过！新功能工作正常\n", "stderr": "2025-05-29 23:05:08,236 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\\temp_20250529_230508\\cases_list_page1_status5_20250529_230508.json\n2025-05-29 23:05:08,237 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\\temp_20250529_230508\\case_detail_123_20250529_230508.json\n2025-05-29 23:05:08,238 - INFO - 数据已保存到: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\\temp_20250529_230508\\api_request_230508_237172.json\n2025-05-29 23:05:08,268 - INFO - 原始数据已打包保存: C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp1l9q0eby\\raw_data_20250529_230508.zip\n2025-05-29 23:05:08,270 - INFO - 已禁用代理\n2025-05-29 23:05:08,277 - INFO - 已禁用代理\n"}]}