#!/usr/bin/env python3
"""
测试相对路径图片处理功能（使用模拟下载）
验证路径更新逻辑
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime
from unittest.mock import patch

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from attachment_manager import AttachmentManager
from models import CaseDetail, CaseSummary, CaseComment
from utils import logger


def create_mock_files(attachment_manager: AttachmentManager, case_id: int, image_urls: list) -> dict:
    """创建模拟的图片文件"""
    case_dir = attachment_manager.get_case_attachments_dir(case_id)
    created_files = {}
    
    for url in image_urls:
        # 从URL生成文件名
        if url.startswith(('http://', 'https://')):
            filename = os.path.basename(url)
        else:
            filename = os.path.basename(url)
        
        if not filename or '.' not in filename:
            import hashlib
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            filename = f"image_{url_hash}.png"
        
        # 创建模拟文件
        file_path = os.path.join(case_dir, filename)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(f"模拟图片文件: {filename}\n")
            f.write(f"原始URL: {url}\n")
            f.write(f"创建时间: {datetime.now()}\n")
        
        created_files[url] = file_path
        logger.info(f"创建模拟图片: {filename}")
    
    return created_files


def mock_download_file(url: str, local_path: str, cookies=None, headers=None) -> bool:
    """模拟文件下载函数"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # 创建模拟文件
        with open(local_path, 'w', encoding='utf-8') as f:
            f.write(f"模拟下载文件\n")
            f.write(f"URL: {url}\n")
            f.write(f"本地路径: {local_path}\n")
            f.write(f"下载时间: {datetime.now()}\n")
        
        logger.info(f"模拟下载成功: {url} -> {local_path}")
        return True
        
    except Exception as e:
        logger.error(f"模拟下载失败: {e}")
        return False


def test_relative_path_with_mock():
    """使用模拟下载测试相对路径处理"""
    print("=" * 60)
    print("测试相对路径处理（模拟下载）")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    original_attachments_dir = config.ATTACHMENTS_DIR
    
    try:
        # 设置临时配置
        config.DATA_DIR = temp_dir
        config.ATTACHMENTS_DIR = os.path.join(temp_dir, "attachments")
        config.ENABLE_SESSION_ATTACHMENTS = True
        config.UPDATE_LOCAL_PATHS = True
        config.BASE_URL = "https://akrocare.com"
        
        # 创建附件管理器
        session_id = "mock_test_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        attachment_manager = AttachmentManager(session_id)
        
        print(f"✅ 附件管理器创建成功")
        print(f"   会话ID: {session_id}")
        print(f"   基础URL: {config.BASE_URL}")
        
        # 测试HTML内容（包含您提供的示例）
        test_html = '''<p><!-- for WeLink copy-->1.MP32的IP在10Gbps速率 Chip to Chip场景时的RX端接收眼图模板是什么？我们需要一个准确的标准来判断我们链路设计是否OK.</p><p>是否按照红色框框的标准就行。</p><p><img src="/uploads/ueditor/image/20250514/1747205872926102.png" title="1747205872926102.png" alt="image.png"/></p><p><br/></p><p>2、<!-- for WeLink copy-->MP32 &nbsp;对于抖动的容忍指标是多少？一般Chip to Chip标准里是有的。</p>'''
        
        case_id = 4001
        
        print(f"\n原始HTML内容:")
        print(test_html[:200] + "...")
        
        # 使用模拟下载函数
        with patch('utils.download_file', side_effect=mock_download_file):
            # 处理HTML内容
            updated_html = attachment_manager.extract_and_download_images(
                case_id, test_html, None, None, config.BASE_URL
            )
        
        print(f"\n处理后HTML内容:")
        print(updated_html[:200] + "...")
        
        # 检查是否有变化
        if updated_html != test_html:
            print("\n✅ HTML内容已更新")
            
            # 查找具体的变化
            original_img_src = "/uploads/ueditor/image/20250514/1747205872926102.png"
            if original_img_src in test_html and original_img_src not in updated_html:
                print(f"✅ 原始路径已替换: {original_img_src}")
                
                # 查找新的路径
                import re
                img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
                new_matches = re.findall(img_pattern, updated_html)
                for new_src in new_matches:
                    if new_src.startswith('attachments/'):
                        print(f"✅ 新路径: {new_src}")
            
        else:
            print("\n⚠️ HTML内容未发生变化")
        
        # 检查生成的文件
        case_dir = attachment_manager.get_case_attachments_dir(case_id)
        if os.path.exists(case_dir):
            files = os.listdir(case_dir)
            print(f"\n✅ 生成的文件:")
            for file in files:
                file_path = os.path.join(case_dir, file)
                print(f"   - {file} ({os.path.getsize(file_path)} bytes)")
        
        # 获取处理摘要
        summary = attachment_manager.get_attachment_summary()
        print(f"\n📊 处理摘要:")
        print(f"   总文件数: {summary['total_files']}")
        print(f"   URL映射: {summary['url_mappings']} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复配置
        config.DATA_DIR = original_data_dir
        config.ATTACHMENTS_DIR = original_attachments_dir
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass


def test_multiple_image_types():
    """测试多种图片类型的处理"""
    print("=" * 60)
    print("测试多种图片类型处理")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    original_attachments_dir = config.ATTACHMENTS_DIR
    
    try:
        # 设置临时配置
        config.DATA_DIR = temp_dir
        config.ATTACHMENTS_DIR = os.path.join(temp_dir, "attachments")
        config.ENABLE_SESSION_ATTACHMENTS = True
        config.UPDATE_LOCAL_PATHS = True
        config.BASE_URL = "https://akrocare.com"
        
        # 创建附件管理器
        session_id = "multi_test_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        attachment_manager = AttachmentManager(session_id)
        
        # 测试多种图片类型的HTML
        test_cases = [
            {
                "name": "绝对路径图片",
                "html": '<p>测试图片：<img src="/uploads/test/image1.png" alt="图片1"/></p>',
                "expected_url": "https://akrocare.com/uploads/test/image1.png"
            },
            {
                "name": "相对路径图片（./开头）",
                "html": '<p>测试图片：<img src="./images/image2.jpg" alt="图片2"/></p>',
                "expected_url": "https://akrocare.com/images/image2.jpg"
            },
            {
                "name": "相对路径图片（直接路径）",
                "html": '<p>测试图片：<img src="docs/diagram.png" alt="图表"/></p>',
                "expected_url": "https://akrocare.com/docs/diagram.png"
            },
            {
                "name": "完整URL图片",
                "html": '<p>测试图片：<img src="https://example.com/external.png" alt="外部图片"/></p>',
                "expected_url": "https://example.com/external.png"
            }
        ]
        
        case_id = 5001
        all_passed = True
        
        # 使用模拟下载函数
        with patch('utils.download_file', side_effect=mock_download_file):
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n测试 {i}: {test_case['name']}")
                print(f"原始HTML: {test_case['html']}")
                
                # 处理HTML
                updated_html = attachment_manager.extract_and_download_images(
                    case_id, test_case['html'], None, None, config.BASE_URL
                )
                
                print(f"更新HTML: {updated_html}")
                
                # 检查是否正确处理
                if updated_html != test_case['html']:
                    print("✅ HTML已更新")
                    
                    # 检查是否包含本地路径
                    if 'attachments/' in updated_html:
                        print("✅ 包含本地路径引用")
                    else:
                        print("⚠️ 未包含本地路径引用")
                        all_passed = False
                else:
                    print("⚠️ HTML未更新")
                    # 对于完整URL，不更新是正常的
                    if not test_case['expected_url'].startswith('https://example.com'):
                        all_passed = False
        
        # 检查生成的文件
        case_dir = attachment_manager.get_case_attachments_dir(case_id)
        if os.path.exists(case_dir):
            files = os.listdir(case_dir)
            print(f"\n✅ 生成的文件: {len(files)} 个")
            for file in files:
                print(f"   - {file}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 多类型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复配置
        config.DATA_DIR = original_data_dir
        config.ATTACHMENTS_DIR = original_attachments_dir
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass


def main():
    """主函数"""
    print("=" * 80)
    print("相对路径图片处理功能测试（模拟下载）")
    print("=" * 80)
    
    tests = [
        ("相对路径处理（模拟下载）", test_relative_path_with_mock),
        ("多种图片类型处理", test_multiple_image_types),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！相对路径图片处理功能完全正常")
        print("\n✅ 修正完成的功能:")
        print("1. ✅ 支持绝对路径图片（/uploads/...）")
        print("2. ✅ 支持相对路径图片（./images/...）")
        print("3. ✅ 支持直接路径图片（images/...）")
        print("4. ✅ 支持完整URL图片（https://...）")
        print("5. ✅ 自动构建完整下载URL")
        print("6. ✅ 正确更新本地路径引用")
        print("7. ✅ 处理您提供的示例数据格式")
    else:
        print("⚠️  部分测试失败，需要检查相关功能")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
