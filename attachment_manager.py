#!/usr/bin/env python3
"""
附件管理器
处理附件的存储、路径更新和打包
"""
import os
import re
import shutil
import hashlib
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse

import config
from utils import logger, get_safe_filename
from models import CaseDetail, CaseAttachment


class AttachmentManager:
    """附件管理器"""

    def __init__(self, session_id: Optional[str] = None):
        self.session_id = session_id or datetime.now().strftime("%Y%m%d_%H%M%S")

        # 设置附件目录
        if config.ENABLE_SESSION_ATTACHMENTS:
            self.session_attachments_dir = os.path.join(config.ATTACHMENTS_DIR, f"session_{self.session_id}")
        else:
            self.session_attachments_dir = config.ATTACHMENTS_DIR

        # 确保目录存在
        os.makedirs(self.session_attachments_dir, exist_ok=True)

        # 附件映射表：原始URL -> 本地路径
        self.attachment_mapping = {}

        logger.info(f"附件管理器初始化完成，会话ID: {self.session_id}")
        logger.info(f"附件存储目录: {self.session_attachments_dir}")

    def get_case_attachments_dir(self, case_id: int) -> str:
        """获取工单附件目录"""
        case_dir = os.path.join(self.session_attachments_dir, str(case_id))
        os.makedirs(case_dir, exist_ok=True)
        return case_dir

    def download_and_store_attachment(self, case_id: int, attachment: CaseAttachment,
                                    cookies: Optional[Dict] = None,
                                    headers: Optional[Dict] = None) -> str:
        """下载并存储附件，返回本地路径"""
        try:
            # 获取工单附件目录
            case_attachments_dir = self.get_case_attachments_dir(case_id)

            # 生成安全的文件名
            safe_filename = get_safe_filename(attachment.file_name)

            # 检查文件名冲突，如果存在则添加版本号
            local_path = os.path.join(case_attachments_dir, safe_filename)
            version = 1
            base_name, ext = os.path.splitext(safe_filename)

            while os.path.exists(local_path):
                version += 1
                versioned_name = f"{base_name}_v{version}{ext}"
                local_path = os.path.join(case_attachments_dir, versioned_name)

            # 下载文件
            from utils import download_file
            success = download_file(attachment.download_url, local_path, cookies, headers)

            if success:
                # 记录映射关系
                self.attachment_mapping[attachment.download_url] = local_path

                # 更新附件对象的本地路径
                attachment.local_path = local_path

                logger.info(f"附件下载成功: {case_id}/{safe_filename}")
                return local_path
            else:
                logger.error(f"附件下载失败: {case_id}/{safe_filename}")
                return ""

        except Exception as e:
            logger.error(f"附件下载异常 {case_id}/{attachment.file_name}: {e}")
            return ""

    def extract_and_download_images(self, case_id: int, content: str,
                                  cookies: Optional[Dict] = None,
                                  headers: Optional[Dict] = None,
                                  base_url: Optional[str] = None) -> str:
        """提取并下载内容中的图片，返回更新后的内容"""
        if not content:
            return content

        try:
            # 查找所有图片标签
            img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
            img_matches = re.finditer(img_pattern, content, re.IGNORECASE)

            updated_content = content

            for match in img_matches:
                img_tag = match.group(0)
                img_src = match.group(1)

                # 构建完整的图片URL
                full_img_url = self._build_full_url(img_src, base_url)

                if full_img_url:
                    # 下载图片
                    local_path = self._download_image(case_id, full_img_url, cookies, headers, img_src)

                    if local_path and config.UPDATE_LOCAL_PATHS:
                        # 计算相对路径
                        relative_path = self._get_relative_path(local_path)

                        # 更新图片标签中的src
                        new_img_tag = img_tag.replace(img_src, relative_path)
                        updated_content = updated_content.replace(img_tag, new_img_tag)

                        logger.info(f"图片路径已更新: {img_src} -> {relative_path}")
                else:
                    logger.warning(f"无法构建图片URL: {img_src}")

            return updated_content

        except Exception as e:
            logger.error(f"图片提取和下载失败 {case_id}: {e}")
            return content

    def _build_full_url(self, img_src: str, base_url: Optional[str] = None) -> Optional[str]:
        """构建完整的图片URL"""
        try:
            # 如果已经是完整URL，直接返回
            if img_src.startswith(('http://', 'https://')):
                return img_src

            # 如果是相对路径，需要基础URL
            if base_url:
                # 确保base_url以/结尾
                if not base_url.endswith('/'):
                    base_url += '/'

                # 处理不同类型的相对路径
                if img_src.startswith('/'):
                    # 绝对路径（相对于域名根目录）
                    from urllib.parse import urlparse
                    parsed_base = urlparse(base_url)
                    return f"{parsed_base.scheme}://{parsed_base.netloc}{img_src}"
                else:
                    # 相对路径
                    return base_url + img_src.lstrip('./')
            else:
                # 没有基础URL，尝试从配置中获取
                if hasattr(config, 'BASE_URL') and config.BASE_URL:
                    return self._build_full_url(img_src, config.BASE_URL)

                # 如果是相对路径但没有基础URL，尝试使用默认的AkroCare URL
                default_base_url = "https://akrocare.com"
                logger.info(f"使用默认基础URL构建图片路径: {default_base_url}")
                return self._build_full_url(img_src, default_base_url)

        except Exception as e:
            logger.error(f"构建完整URL失败 {img_src}: {e}")
            return None

    def _download_image(self, case_id: int, image_url: str,
                       cookies: Optional[Dict] = None,
                       headers: Optional[Dict] = None,
                       original_src: Optional[str] = None) -> str:
        """下载单个图片"""
        try:
            # 获取工单附件目录
            case_attachments_dir = self.get_case_attachments_dir(case_id)

            # 优先从原始src获取文件名，然后从完整URL获取
            filename = None

            # 尝试从原始src获取文件名
            if original_src:
                filename = os.path.basename(original_src)

            # 如果原始src没有文件名，从完整URL获取
            if not filename or '.' not in filename:
                parsed_url = urlparse(image_url)
                filename = os.path.basename(parsed_url.path)

            # 如果仍然无法获取文件名，生成一个
            if not filename or '.' not in filename:
                # 使用原始src或URL生成hash
                src_for_hash = original_src or image_url
                url_hash = hashlib.md5(src_for_hash.encode()).hexdigest()[:8]
                filename = f"image_{url_hash}.png"

            # 生成安全的文件名
            safe_filename = get_safe_filename(filename)
            local_path = os.path.join(case_attachments_dir, safe_filename)

            # 检查是否已经下载过
            if image_url in self.attachment_mapping:
                return self.attachment_mapping[image_url]

            # 下载图片
            from utils import download_file
            success = download_file(image_url, local_path, cookies, headers)

            if success:
                # 记录映射关系
                self.attachment_mapping[image_url] = local_path
                logger.info(f"图片下载成功: {case_id}/{safe_filename}")
                return local_path
            else:
                logger.error(f"图片下载失败: {case_id}/{safe_filename}")
                return ""

        except Exception as e:
            logger.error(f"图片下载异常 {case_id}/{image_url}: {e}")
            return ""

    def _get_relative_path(self, local_path: str) -> str:
        """获取相对路径（相对于数据包根目录）"""
        try:
            # 计算相对于session附件目录的路径
            rel_path = os.path.relpath(local_path, self.session_attachments_dir)

            # 添加附件路径前缀
            if config.ATTACHMENT_PATH_PREFIX:
                rel_path = f"{config.ATTACHMENT_PATH_PREFIX}/{rel_path}"

            # 统一使用正斜杠
            rel_path = rel_path.replace('\\', '/')

            return rel_path

        except Exception as e:
            logger.error(f"计算相对路径失败 {local_path}: {e}")
            return local_path

    def process_case_detail(self, case_detail: CaseDetail,
                          cookies: Optional[Dict] = None,
                          headers: Optional[Dict] = None,
                          base_url: Optional[str] = None) -> CaseDetail:
        """处理工单详情，下载附件和图片，更新路径"""
        case_id = case_detail.summary.id

        logger.info(f"开始处理工单 {case_id} 的附件和图片")

        try:
            # 1. 处理工单描述中的图片
            if case_detail.summary.issue_description:
                case_detail.summary.issue_description = self.extract_and_download_images(
                    case_id, case_detail.summary.issue_description, cookies, headers, base_url
                )

            # 2. 处理评论中的图片
            for comment in case_detail.comments:
                if comment.content:
                    comment.content = self.extract_and_download_images(
                        case_id, comment.content, cookies, headers, base_url
                    )

            # 3. 处理附件
            for attachment in case_detail.attachments:
                if attachment.download_url:
                    local_path = self.download_and_store_attachment(
                        case_id, attachment, cookies, headers
                    )

                    if local_path and config.UPDATE_LOCAL_PATHS:
                        # 更新为相对路径
                        attachment.local_path = self._get_relative_path(local_path)

            logger.info(f"工单 {case_id} 附件和图片处理完成")
            return case_detail

        except Exception as e:
            logger.error(f"处理工单 {case_id} 附件失败: {e}")
            return case_detail

    def copy_attachments_to_package(self, package_dir: str, case_details: List[CaseDetail]) -> bool:
        """将附件复制到数据包目录"""
        if not config.COPY_ATTACHMENTS_TO_PACKAGE:
            return True

        try:
            # 创建附件目录
            package_attachments_dir = os.path.join(package_dir, config.ATTACHMENT_PATH_PREFIX)
            os.makedirs(package_attachments_dir, exist_ok=True)

            copied_files = 0

            for case_detail in case_details:
                case_id = case_detail.summary.id
                case_package_dir = os.path.join(package_attachments_dir, str(case_id))

                # 检查是否有附件需要复制
                has_attachments = False

                # 检查附件文件
                for attachment in case_detail.attachments:
                    if attachment.local_path and os.path.exists(attachment.local_path):
                        has_attachments = True
                        break

                # 检查工单目录中的所有文件
                case_attachments_dir = self.get_case_attachments_dir(case_id)
                if os.path.exists(case_attachments_dir):
                    files = os.listdir(case_attachments_dir)
                    if files:
                        has_attachments = True

                # 如果有附件，创建目录并复制文件
                if has_attachments:
                    os.makedirs(case_package_dir, exist_ok=True)

                    # 复制工单目录中的所有文件
                    if os.path.exists(case_attachments_dir):
                        for filename in os.listdir(case_attachments_dir):
                            src_path = os.path.join(case_attachments_dir, filename)
                            dst_path = os.path.join(case_package_dir, filename)

                            if os.path.isfile(src_path):
                                shutil.copy2(src_path, dst_path)
                                copied_files += 1
                                logger.debug(f"复制附件: {src_path} -> {dst_path}")

            logger.info(f"附件复制完成，共复制 {copied_files} 个文件到数据包")
            return True

        except Exception as e:
            logger.error(f"复制附件到数据包失败: {e}")
            return False

    def get_attachment_summary(self) -> Dict[str, Any]:
        """获取附件处理摘要"""
        total_files = 0
        total_size = 0

        try:
            for root, _, files in os.walk(self.session_attachments_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.isfile(file_path):
                        total_files += 1
                        total_size += os.path.getsize(file_path)
        except Exception as e:
            logger.error(f"计算附件摘要失败: {e}")

        return {
            "session_id": self.session_id,
            "attachments_dir": self.session_attachments_dir,
            "total_files": total_files,
            "total_size": total_size,
            "total_size_mb": round(total_size / 1024 / 1024, 2),
            "url_mappings": len(self.attachment_mapping)
        }

    def cleanup_session(self):
        """清理会话（可选）"""
        try:
            if os.path.exists(self.session_attachments_dir):
                # 这里可以选择是否删除附件目录
                # shutil.rmtree(self.session_attachments_dir)
                logger.info(f"会话附件目录保留: {self.session_attachments_dir}")
        except Exception as e:
            logger.error(f"清理会话失败: {e}")


# 全局附件管理器实例
_attachment_manager = None

def get_attachment_manager(session_id: Optional[str] = None) -> AttachmentManager:
    """获取全局附件管理器实例"""
    global _attachment_manager
    if _attachment_manager is None or session_id:
        _attachment_manager = AttachmentManager(session_id)
    return _attachment_manager
