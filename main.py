#!/usr/bin/env python3
"""
工单采集程序主入口
"""
import sys
import argparse
from datetime import datetime

from case_collector import CaseCollector
from utils import logger
import config


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='AkroCare工单采集程序')
    parser.add_argument('--email', type=str, help='登录邮箱')
    parser.add_argument('--password', type=str, help='登录密码')
    parser.add_argument('--driver-path', type=str, help='ChromeDriver可执行文件路径')
    parser.add_argument('--api-method', type=str, choices=['requests', 'selenium'],
                       help='API请求方式：requests或selenium')
    parser.add_argument('--disable-proxy', action='store_true', help='禁用代理')
    parser.add_argument('--enable-proxy', action='store_true', help='启用代理')
    parser.add_argument('--incremental', action='store_true', help='启用增量更新模式')
    parser.add_argument('--disable-incremental', action='store_true', help='禁用增量更新模式')

    # 新增的采集模式参数
    parser.add_argument('--case-ids', type=str, help='指定工单ID列表，用逗号分隔，例如: 5766,7514')
    parser.add_argument('--latest-n', type=int, help='限制采集最新的N条工单')
    parser.add_argument('--incremental-n', type=int, help='限制采集离上次更新后的N条工单')
    parser.add_argument('--both-status', action='store_true', help='采集Open和Closed两种状态的工单')
    parser.add_argument('--only-open', action='store_true', help='只采集Open状态的工单')
    parser.add_argument('--only-closed', action='store_true', help='只采集Closed状态的工单')

    # 调试和原始数据参数
    parser.add_argument('--save-raw-data', action='store_true', help='保存原始响应数据')
    parser.add_argument('--pack-raw-data', action='store_true', help='打包原始数据')
    parser.add_argument('--separate-raw-data', action='store_true', help='分别保存原始数据文件')

    # 数据模式参数（新增）
    parser.add_argument('--full-mode', action='store_true', help='启用全量模式')
    parser.add_argument('--incremental-mode', action='store_true', help='启用增量模式')
    parser.add_argument('--enable-zip-package', action='store_true', help='启用ZIP打包')
    parser.add_argument('--base-version', type=str, help='增量模式的基础版本')

    args = parser.parse_args()

    # 更新配置
    if args.email:
        config.EMAIL = args.email
    if args.password:
        config.PASSWORD = args.password
    if args.driver_path:
        config.CHROME_DRIVER_PATH = args.driver_path
    if args.api_method:
        config.API_REQUEST_METHOD = args.api_method
    if args.disable_proxy:
        config.DISABLE_PROXY = True
    if args.enable_proxy:
        config.DISABLE_PROXY = False
    if args.incremental:
        config.ENABLE_INCREMENTAL_UPDATE = True
    if args.disable_incremental:
        config.ENABLE_INCREMENTAL_UPDATE = False

    # 新增的采集模式配置
    if args.case_ids:
        case_ids = [int(id.strip()) for id in args.case_ids.split(',') if id.strip().isdigit()]
        config.SPECIFIC_CASE_IDS = case_ids
        logger.info(f"设置指定工单ID: {case_ids}")

    if args.latest_n:
        config.LATEST_N_CASES = args.latest_n
        config.ENABLE_INCREMENTAL_UPDATE = False  # 最新N条模式不使用增量更新
        logger.info(f"设置采集最新 {args.latest_n} 条工单")

    if args.incremental_n:
        config.INCREMENTAL_N_CASES = args.incremental_n
        config.ENABLE_INCREMENTAL_UPDATE = True  # 增量N条模式使用增量更新
        logger.info(f"设置采集增量 {args.incremental_n} 条工单")

    # 工单状态配置
    if args.both_status:
        config.COLLECT_BOTH_STATUS = True
        logger.info("设置采集Open和Closed两种状态的工单")
    elif args.only_open:
        config.COLLECT_BOTH_STATUS = False
        config.CASE_STATUS_CLOSED = config.CASE_STATUS_OPEN
        logger.info("设置只采集Open状态的工单")
    elif args.only_closed:
        config.COLLECT_BOTH_STATUS = False
        logger.info("设置只采集Closed状态的工单")

    # 原始数据配置
    if args.save_raw_data:
        config.SAVE_RAW_DATA = True
        logger.info("启用原始数据保存")

    if args.pack_raw_data:
        config.PACK_RAW_DATA = True
        logger.info("启用原始数据打包模式")

    if args.separate_raw_data:
        config.PACK_RAW_DATA = False
        logger.info("启用原始数据分别保存模式")

    # 数据模式配置（新增）
    data_mode = "normal"  # 默认模式
    enable_zip_package = False
    base_version = None

    if args.full_mode:
        data_mode = "full"
        logger.info("启用全量模式")
    elif args.incremental_mode:
        data_mode = "incremental"
        logger.info("启用增量模式")

    if args.enable_zip_package:
        enable_zip_package = True
        logger.info("启用ZIP打包")

    if args.base_version:
        base_version = args.base_version
        logger.info(f"设置基础版本: {base_version}")

    # 检查必要的配置
    if not config.EMAIL or config.EMAIL == "<EMAIL>":
        logger.error("请在config.py中设置正确的EMAIL或使用--email参数")
        sys.exit(1)

    if not config.PASSWORD or config.PASSWORD == "your_password":
        logger.error("请在config.py中设置正确的PASSWORD或使用--password参数")
        sys.exit(1)

    logger.info("=" * 60)
    logger.info("AkroCare工单采集程序启动")
    logger.info(f"登录邮箱: {config.EMAIL}")
    logger.info(f"ChromeDriver: {'自定义路径' if config.CHROME_DRIVER_PATH else '自动下载'}")
    if config.CHROME_DRIVER_PATH:
        logger.info(f"Driver路径: {config.CHROME_DRIVER_PATH}")
    logger.info(f"API请求方式: {config.API_REQUEST_METHOD}")
    logger.info(f"代理设置: {'禁用' if config.DISABLE_PROXY else '启用'}")
    logger.info(f"增量更新: {'启用' if config.ENABLE_INCREMENTAL_UPDATE else '禁用'}")

    # 显示采集模式信息
    if config.SPECIFIC_CASE_IDS:
        logger.info(f"采集模式: 指定工单ID - {config.SPECIFIC_CASE_IDS}")
    elif config.LATEST_N_CASES > 0:
        logger.info(f"采集模式: 最新 {config.LATEST_N_CASES} 条工单")
    elif config.INCREMENTAL_N_CASES > 0:
        logger.info(f"采集模式: 增量 {config.INCREMENTAL_N_CASES} 条工单")
    else:
        logger.info("采集模式: 全量采集")

    # 显示工单状态配置
    if config.COLLECT_BOTH_STATUS:
        logger.info(f"工单状态: Open({config.CASE_STATUS_OPEN}) + Closed({config.CASE_STATUS_CLOSED})")
    else:
        status_name = "Open" if config.CASE_STATUS_CLOSED == config.CASE_STATUS_OPEN else "Closed"
        logger.info(f"工单状态: 仅{status_name}({config.CASE_STATUS_CLOSED})")

    # 显示原始数据配置
    if config.SAVE_RAW_DATA:
        pack_mode = "打包模式" if config.PACK_RAW_DATA else "分别保存模式"
        logger.info(f"原始数据: 启用 - {pack_mode}")
    else:
        logger.info("原始数据: 禁用")

    # 显示数据模式配置
    if data_mode == "full":
        package_mode = "ZIP打包" if enable_zip_package else "目录模式"
        logger.info(f"数据模式: 全量模式 - {package_mode}")
    elif data_mode == "incremental":
        logger.info(f"数据模式: 增量模式")
        if base_version:
            logger.info(f"基础版本: {base_version}")
    else:
        logger.info("数据模式: 标准模式")

    logger.info(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    logger.info("=" * 60)

    try:
        # 使用上下文管理器确保资源正确释放
        with CaseCollector() as collector:
            # 登录系统
            if not collector.login():
                logger.error("登录失败，程序退出")
                sys.exit(1)

            # 导航到工单页面
            if not collector.navigate_to_cases():
                logger.error("导航到工单页面失败，程序退出")
                sys.exit(1)

            # 采集工单数据
            logger.info("开始采集工单数据...")
            case_details = collector.collect_all_cases()

            if not case_details:
                logger.warning("没有采集到工单数据")
                return

            # 根据数据模式保存数据
            if data_mode == "full":
                # 全量模式
                from data_mode_manager import get_data_mode_manager
                data_manager = get_data_mode_manager()
                package_path = data_manager.create_full_mode_package(
                    case_details, enable_zip_package
                )
                logger.info("=" * 60)
                logger.info(f"全量模式采集完成！共采集 {len(case_details)} 条工单")
                logger.info(f"数据包保存位置: {package_path}")
                logger.info(f"附件保存位置: {config.ATTACHMENTS_DIR}")
                logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info("=" * 60)
            elif data_mode == "incremental":
                # 增量模式
                from data_mode_manager import get_data_mode_manager
                data_manager = get_data_mode_manager()
                patch_path = data_manager.create_incremental_patch(
                    case_details, base_version
                )
                logger.info("=" * 60)
                logger.info(f"增量模式采集完成！共采集 {len(case_details)} 条工单")
                logger.info(f"Patch保存位置: {patch_path}")
                logger.info(f"附件保存位置: {config.ATTACHMENTS_DIR}")
                logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info("=" * 60)
            else:
                # 标准模式
                collector.save_cases_data(case_details)
                logger.info("=" * 60)
                logger.info(f"采集完成！共采集 {len(case_details)} 条工单")
                logger.info(f"数据保存位置: {config.CASES_DATA_FILE}")
                logger.info(f"附件保存位置: {config.ATTACHMENTS_DIR}")
                logger.info(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                logger.info("=" * 60)

    except KeyboardInterrupt:
        logger.info("用户中断程序")
        sys.exit(0)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
