#!/usr/bin/env python3
"""
调试登录后的工单5766 HTML内容
"""
import os
import sys
import tempfile
import shutil
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from case_collector import CaseCollector
from utils import logger


def debug_logged_in_case_5766():
    """调试登录后的工单5766内容"""
    print("=" * 80)
    print("调试登录后的工单5766内容")
    print("=" * 80)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    
    try:
        print(f"✅ 调试环境设置完成")
        print(f"   临时目录: {temp_dir}")
        
        # 创建工单采集器并登录
        with CaseCollector() as collector:
            print(f"\n🔐 正在登录...")
            if not collector.login():
                print(f"❌ 登录失败")
                return False
            
            print(f"✅ 登录成功")
            
            # 导航到工单页面
            print(f"🧭 导航到工单页面...")
            if not collector.navigate_to_cases():
                print(f"❌ 导航失败")
                return False
            
            print(f"✅ 导航成功")
            
            # 直接访问工单5766页面
            detail_url = config.CASE_DETAIL_URL_TEMPLATE.format(5766)
            print(f"\n🔍 访问工单5766页面: {detail_url}")
            
            collector.driver.get(detail_url)
            
            # 等待页面加载
            import time
            time.sleep(5)
            
            # 获取页面源码
            page_source = collector.driver.page_source
            current_url = collector.driver.current_url
            page_title = collector.driver.title
            
            print(f"✅ 页面加载完成")
            print(f"   当前URL: {current_url}")
            print(f"   页面标题: {page_title}")
            print(f"   页面大小: {len(page_source)} 字符")
            
            # 保存完整HTML到文件
            html_file = os.path.join(temp_dir, "case_5766_logged_in.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            print(f"✅ HTML内容已保存到: {html_file}")
            
            # 使用BeautifulSoup解析
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 详细分析HTML结构
            print(f"\n🔍 详细分析HTML结构...")
            
            # 1. 检查页面基本信息
            print(f"1. 页面基本信息:")
            print(f"   是否包含5766: {'5766' in page_source}")
            print(f"   是否包含DDRPHY: {'DDRPHY' in page_source}")
            print(f"   是否包含Attachments: {'Attachments' in page_source}")
            print(f"   是否包含attachment: {'attachment' in page_source.lower()}")
            print(f"   是否包含download: {'download' in page_source.lower()}")
            
            # 2. 查找所有表格
            tables = soup.find_all('table')
            print(f"\n2. 页面中的表格: {len(tables)} 个")
            for i, table in enumerate(tables, 1):
                rows = table.find_all('tr')
                print(f"   表格 {i}: {len(rows)} 行")
                
                # 显示表格的前几行内容
                for j, row in enumerate(rows[:3]):
                    cells = row.find_all(['th', 'td'])
                    cell_texts = [cell.get_text(strip=True)[:50] for cell in cells]  # 限制长度
                    print(f"     行 {j+1}: {cell_texts}")
                
                # 检查是否是附件表格
                table_text = table.get_text().lower()
                if any(keyword in table_text for keyword in ['attachment', 'file', 'download', 'ddrphy']):
                    print(f"     ⭐ 表格 {i} 可能包含附件信息")
                    
                    # 保存这个表格的HTML
                    table_file = os.path.join(temp_dir, f"table_{i}.html")
                    with open(table_file, 'w', encoding='utf-8') as f:
                        f.write(str(table))
                    print(f"     表格HTML已保存到: {table_file}")
                print()
            
            # 3. 查找所有包含特定关键词的元素
            keywords = ['DDRPHY', 'attachment', 'download', 'file', 'docx', 'pdf']
            for keyword in keywords:
                elements = soup.find_all(string=lambda text: text and keyword.lower() in text.lower())
                if elements:
                    print(f"3. 包含'{keyword}'的文本: {len(elements)} 个")
                    for i, text in enumerate(elements[:5], 1):  # 只显示前5个
                        clean_text = ' '.join(text.strip().split())[:100]  # 清理并限制长度
                        print(f"   {i}. '{clean_text}...'")
                        
                        # 查找父元素
                        parent = text.parent
                        if parent:
                            parent_info = f"{parent.name}"
                            if parent.get('class'):
                                parent_info += f" class={parent.get('class')}"
                            if parent.get('id'):
                                parent_info += f" id={parent.get('id')}"
                            print(f"      父元素: {parent_info}")
                    print()
            
            # 4. 查找所有链接
            links = soup.find_all('a', href=True)
            download_links = [link for link in links if 'download' in link.get('href', '').lower()]
            file_links = [link for link in links if any(ext in link.get('href', '').lower() for ext in ['.docx', '.pdf', '.txt', '.xlsx'])]
            
            print(f"4. 链接分析:")
            print(f"   总链接数: {len(links)}")
            print(f"   下载链接: {len(download_links)}")
            print(f"   文件链接: {len(file_links)}")
            
            if download_links:
                print(f"   下载链接详情:")
                for i, link in enumerate(download_links[:5], 1):
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    print(f"     {i}. '{text}' -> {href}")
            
            if file_links:
                print(f"   文件链接详情:")
                for i, link in enumerate(file_links[:5], 1):
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    print(f"     {i}. '{text}' -> {href}")
            
            # 5. 查找特定的HTML结构
            print(f"\n5. 查找特定结构:")
            
            # 查找akro-attach
            akro_attach = soup.find(class_="akro-attach")
            if akro_attach:
                print(f"   ✅ 找到akro-attach元素")
                attach_file = os.path.join(temp_dir, "akro_attach.html")
                with open(attach_file, 'w', encoding='utf-8') as f:
                    f.write(str(akro_attach))
                print(f"   akro-attach HTML已保存到: {attach_file}")
            else:
                print(f"   ❌ 未找到akro-attach元素")
            
            # 查找包含"Attachments"的标题
            attachment_headers = soup.find_all(['h1', 'h2', 'h3', 'h4', 'h5', 'h6'], 
                                             string=lambda text: text and 'attachment' in text.lower())
            if attachment_headers:
                print(f"   ✅ 找到附件标题: {len(attachment_headers)} 个")
                for i, header in enumerate(attachment_headers, 1):
                    print(f"     {i}. {header.name}: '{header.get_text(strip=True)}'")
            else:
                print(f"   ❌ 未找到附件标题")
            
            # 6. 查找工单内容区域
            print(f"\n6. 查找工单内容区域:")
            
            # 查找可能的工单内容容器
            content_containers = soup.find_all(['div', 'section'], 
                                             class_=lambda cls: cls and any(keyword in ' '.join(cls).lower() 
                                                                           for keyword in ['content', 'detail', 'case', 'main']))
            
            print(f"   内容容器: {len(content_containers)} 个")
            for i, container in enumerate(content_containers[:3], 1):
                classes = container.get('class', [])
                container_text = container.get_text(strip=True)[:200]
                print(f"     {i}. class={classes} - '{container_text}...'")
                
                # 检查容器中是否有附件相关内容
                if any(keyword in container_text.lower() for keyword in ['attachment', 'file', 'download', 'ddrphy']):
                    print(f"        ⭐ 容器 {i} 可能包含附件信息")
                    container_file = os.path.join(temp_dir, f"container_{i}.html")
                    with open(container_file, 'w', encoding='utf-8') as f:
                        f.write(str(container))
                    print(f"        容器HTML已保存到: {container_file}")
            
            # 7. 保存调试信息
            debug_info = {
                "url": current_url,
                "title": page_title,
                "page_size": len(page_source),
                "contains_5766": "5766" in page_source,
                "contains_ddrphy": "DDRPHY" in page_source,
                "contains_attachments": "Attachments" in page_source,
                "table_count": len(tables),
                "link_count": len(links),
                "download_link_count": len(download_links),
                "file_link_count": len(file_links),
                "has_akro_attach": akro_attach is not None,
                "attachment_header_count": len(attachment_headers),
                "content_container_count": len(content_containers)
            }
            
            debug_file = os.path.join(temp_dir, "debug_info.json")
            with open(debug_file, 'w', encoding='utf-8') as f:
                json.dump(debug_info, f, indent=2, ensure_ascii=False)
            
            print(f"\n📊 调试信息已保存到: {debug_file}")
            print(f"📁 所有文件保存在: {temp_dir}")
            
            return True, temp_dir
    
    except Exception as e:
        print(f"❌ 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False, None
    
    finally:
        # 不清理临时目录，保留文件供查看
        pass


def main():
    """主函数"""
    print("🔍 工单5766登录后HTML内容调试")
    
    try:
        result, temp_dir = debug_logged_in_case_5766()
        
        if result:
            print(f"\n🎉 调试完成！")
            print(f"\n📋 下一步:")
            print(f"1. 查看保存的HTML文件: {temp_dir}")
            print(f"2. 分析debug_info.json了解页面结构")
            print(f"3. 检查table_*.html和container_*.html文件")
            print(f"4. 根据实际HTML结构修正解析逻辑")
            
            if temp_dir:
                print(f"\n📁 保存的文件:")
                for file in os.listdir(temp_dir):
                    file_path = os.path.join(temp_dir, file)
                    size = os.path.getsize(file_path)
                    print(f"   - {file} ({size} bytes)")
            
            return True
        else:
            print(f"\n❌ 调试失败")
            return False
            
    except Exception as e:
        print(f"❌ 主函数异常: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
