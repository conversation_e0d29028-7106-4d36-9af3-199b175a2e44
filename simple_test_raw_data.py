#!/usr/bin/env python3
"""
简单测试RawDataManager
"""
import os
import sys
import tempfile

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_raw_data_manager():
    """测试RawDataManager"""
    print("开始测试RawDataManager...")
    
    try:
        # 设置临时配置
        import config
        original_save_raw_data = config.SAVE_RAW_DATA
        original_pack_raw_data = config.PACK_RAW_DATA
        original_raw_data_dir = config.RAW_DATA_DIR
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp()
        config.SAVE_RAW_DATA = True
        config.PACK_RAW_DATA = True
        config.RAW_DATA_DIR = temp_dir
        
        print(f"临时目录: {temp_dir}")
        print(f"SAVE_RAW_DATA: {config.SAVE_RAW_DATA}")
        print(f"PACK_RAW_DATA: {config.PACK_RAW_DATA}")
        
        # 导入RawDataManager
        from raw_data_manager import RawDataManager
        
        # 创建实例
        manager = RawDataManager()
        print("✅ RawDataManager创建成功")
        
        # 检查属性
        print(f"temp_dir: {manager.temp_dir}")
        print(f"pack_data: {manager.pack_data}")
        print(f"session_id: {manager.session_id}")
        
        # 测试保存方法
        manager.save_cases_list_response(1, {"test": "data"}, "5")
        print("✅ save_cases_list_response成功")
        
        # 完成会话
        manager.finalize_session()
        print("✅ finalize_session成功")
        
        # 检查生成的文件
        files = os.listdir(temp_dir)
        print(f"生成的文件: {files}")
        
        # 恢复配置
        config.SAVE_RAW_DATA = original_save_raw_data
        config.PACK_RAW_DATA = original_pack_raw_data
        config.RAW_DATA_DIR = original_raw_data_dir
        
        # 清理临时目录
        import shutil
        shutil.rmtree(temp_dir, ignore_errors=True)
        
        print("🎉 测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_raw_data_manager()
    if success:
        print("\n✅ RawDataManager修复验证成功")
    else:
        print("\n❌ RawDataManager仍有问题")
    sys.exit(0 if success else 1)
