#!/usr/bin/env python3
"""
测试相对路径图片处理功能
验证系统能够正确处理相对路径的图片
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from attachment_manager import AttachmentManager
from models import CaseDetail, CaseSummary, CaseComment, CaseAttachment
from utils import logger


def create_test_case_with_relative_images(case_id: int) -> CaseDetail:
    """创建包含相对路径图片的测试工单"""
    
    # 测试用的HTML内容，包含您提供的示例
    issue_description = '''<p><!-- for WeLink copy-->1.MP32的IP在10Gbps速率 Chip to Chip场景时的RX端接收眼图模板是什么？我们需要一个准确的标准来判断我们链路设计是否OK.</p><p>是否按照红色框框的标准就行。</p><p><img src="/uploads/ueditor/image/20250514/1747205872926102.png" title="1747205872926102.png" alt="image.png"/></p><p><br/></p><p>2、<!-- for WeLink copy-->MP32 &nbsp;对于抖动的容忍指标是多少？一般Chip to Chip标准里是有的。</p>'''
    
    # 创建工单概要
    summary = CaseSummary(
        id=case_id,
        case_number=f"TEST{case_id:06d}",
        subject=f"相对路径图片测试工单{case_id}",
        product_line="MP32",
        product_name="MP32产品",
        project_name="Chip to Chip项目",
        cnum="MP32",
        product_code="MP32",
        case_level=1,
        issue_type=1,
        issue_description=issue_description,
        attach=1,
        priority=4,
        version="v1.0",
        notify=None,
        status=5,
        uid=1,
        deadline=None,
        is_admin_create=0,
        created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        updated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        company_name="测试公司",
        username="测试用户"
    )
    
    # 创建评论（包含不同类型的相对路径图片）
    comments = [
        CaseComment(
            source="测试用户1",
            content='<p>这是评论1，包含绝对路径图片：<img src="/uploads/images/diagram1.png" alt="图表1"/></p>',
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ),
        CaseComment(
            source="测试用户2", 
            content='<p>这是评论2，包含相对路径图片：<img src="./images/screenshot.jpg" alt="截图"/></p>',
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        ),
        CaseComment(
            source="测试用户3",
            content='<p>这是评论3，包含完整URL图片：<img src="https://example.com/full_url_image.png" alt="完整URL图片"/></p>',
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    ]
    
    # 创建附件
    attachments = [
        CaseAttachment(
            attachment_id=f"att_{case_id}_001",
            file_name=f"spec_{case_id}.pdf",
            file_size="2048",
            owner="测试用户",
            last_modified=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            download_url=f"https://akrocare.com/download/{case_id}/spec.pdf",
            local_path=""
        )
    ]
    
    return CaseDetail(
        summary=summary,
        comments=comments,
        attachments=attachments
    )


def test_url_building():
    """测试URL构建功能"""
    print("=" * 60)
    print("测试URL构建功能")
    print("=" * 60)
    
    try:
        # 创建附件管理器
        session_id = "url_test_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        attachment_manager = AttachmentManager(session_id)
        
        # 测试不同类型的URL构建
        test_cases = [
            {
                "img_src": "/uploads/ueditor/image/20250514/1747205872926102.png",
                "base_url": "https://akrocare.com",
                "expected": "https://akrocare.com/uploads/ueditor/image/20250514/1747205872926102.png",
                "description": "绝对路径（以/开头）"
            },
            {
                "img_src": "./images/screenshot.jpg",
                "base_url": "https://akrocare.com/cases/",
                "expected": "https://akrocare.com/cases/images/screenshot.jpg",
                "description": "相对路径（以./开头）"
            },
            {
                "img_src": "images/diagram.png",
                "base_url": "https://akrocare.com/cases/",
                "expected": "https://akrocare.com/cases/images/diagram.png",
                "description": "相对路径（直接文件路径）"
            },
            {
                "img_src": "https://example.com/full_url.png",
                "base_url": "https://akrocare.com",
                "expected": "https://example.com/full_url.png",
                "description": "完整URL（不需要处理）"
            },
            {
                "img_src": "/uploads/test.png",
                "base_url": None,
                "expected": "https://akrocare.com/uploads/test.png",
                "description": "无base_url，使用默认"
            }
        ]
        
        print("URL构建测试:")
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            img_src = test_case["img_src"]
            base_url = test_case["base_url"]
            expected = test_case["expected"]
            description = test_case["description"]
            
            result = attachment_manager._build_full_url(img_src, base_url)
            
            if result == expected:
                print(f"✅ 测试 {i}: {description}")
                print(f"   输入: {img_src}")
                print(f"   基础URL: {base_url}")
                print(f"   结果: {result}")
            else:
                print(f"❌ 测试 {i}: {description}")
                print(f"   输入: {img_src}")
                print(f"   基础URL: {base_url}")
                print(f"   期望: {expected}")
                print(f"   实际: {result}")
                all_passed = False
            print()
        
        return all_passed
        
    except Exception as e:
        print(f"❌ URL构建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_relative_path_processing():
    """测试相对路径图片处理"""
    print("=" * 60)
    print("测试相对路径图片处理")
    print("=" * 60)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    original_attachments_dir = config.ATTACHMENTS_DIR
    
    try:
        # 设置临时配置
        config.DATA_DIR = temp_dir
        config.ATTACHMENTS_DIR = os.path.join(temp_dir, "attachments")
        config.ENABLE_SESSION_ATTACHMENTS = True
        config.UPDATE_LOCAL_PATHS = True
        config.BASE_URL = "https://akrocare.com"
        
        # 创建附件管理器
        session_id = "relative_test_" + datetime.now().strftime("%Y%m%d_%H%M%S")
        attachment_manager = AttachmentManager(session_id)
        
        print(f"✅ 附件管理器创建成功")
        print(f"   会话ID: {session_id}")
        print(f"   基础URL: {config.BASE_URL}")
        
        # 创建测试工单
        test_case = create_test_case_with_relative_images(3001)
        
        print(f"✅ 创建测试工单: {test_case.summary.id}")
        
        # 显示原始内容
        print("\n原始工单描述:")
        print(test_case.summary.issue_description[:200] + "...")
        
        print("\n原始评论内容:")
        for i, comment in enumerate(test_case.comments):
            print(f"评论 {i+1}: {comment.content[:100]}...")
        
        # 测试图片提取和路径更新（不实际下载）
        print("\n开始处理图片路径...")
        
        # 处理工单描述
        original_description = test_case.summary.issue_description
        updated_description = attachment_manager.extract_and_download_images(
            test_case.summary.id, original_description, None, None, config.BASE_URL
        )
        
        print("\n工单描述处理结果:")
        if updated_description != original_description:
            print("✅ 工单描述路径已更新")
            # 查找变更的部分
            if "/uploads/ueditor/image/20250514/1747205872926102.png" in original_description:
                if "attachments/" in updated_description:
                    print("   相对路径图片已转换为本地路径")
                else:
                    print("   ⚠️ 路径更新可能未完全生效")
        else:
            print("⚠️ 工单描述未发生变化（可能是下载失败）")
        
        # 处理评论
        print("\n评论处理结果:")
        for i, comment in enumerate(test_case.comments):
            original_content = comment.content
            updated_content = attachment_manager.extract_and_download_images(
                test_case.summary.id, original_content, None, None, config.BASE_URL
            )
            
            if updated_content != original_content:
                print(f"✅ 评论 {i+1} 路径已更新")
            else:
                print(f"⚠️ 评论 {i+1} 未发生变化")
        
        # 检查生成的目录结构
        case_dir = attachment_manager.get_case_attachments_dir(test_case.summary.id)
        if os.path.exists(case_dir):
            files = os.listdir(case_dir)
            print(f"\n✅ 工单附件目录已创建: {case_dir}")
            print(f"   包含文件: {len(files)} 个")
            for file in files:
                print(f"     - {file}")
        else:
            print(f"\n⚠️ 工单附件目录未创建（可能是下载失败）")
        
        # 获取处理摘要
        summary = attachment_manager.get_attachment_summary()
        print(f"\n📊 处理摘要:")
        print(f"   总文件数: {summary['total_files']}")
        print(f"   URL映射: {summary['url_mappings']} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 相对路径处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复配置
        config.DATA_DIR = original_data_dir
        config.ATTACHMENTS_DIR = original_attachments_dir
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass


def test_image_extraction_patterns():
    """测试图片提取模式"""
    print("=" * 60)
    print("测试图片提取模式")
    print("=" * 60)
    
    try:
        import re
        
        # 测试HTML内容
        test_html = '''
        <p>这是一个包含多种图片的HTML内容</p>
        <img src="/uploads/ueditor/image/20250514/1747205872926102.png" title="1747205872926102.png" alt="image.png"/>
        <p>还有其他图片：</p>
        <img src="./images/screenshot.jpg" alt="截图">
        <img src="https://example.com/full_url.png" alt="完整URL">
        <img src='images/diagram.png' title='图表'>
        '''
        
        # 使用正则表达式提取图片
        img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
        img_matches = re.finditer(img_pattern, test_html, re.IGNORECASE)
        
        print("提取到的图片路径:")
        extracted_paths = []
        for i, match in enumerate(img_matches, 1):
            img_src = match.group(1)
            extracted_paths.append(img_src)
            print(f"{i}. {img_src}")
        
        # 验证提取结果
        expected_paths = [
            "/uploads/ueditor/image/20250514/1747205872926102.png",
            "./images/screenshot.jpg",
            "https://example.com/full_url.png",
            "images/diagram.png"
        ]
        
        if len(extracted_paths) == len(expected_paths):
            print(f"\n✅ 提取数量正确: {len(extracted_paths)} 个")
            
            all_found = True
            for expected in expected_paths:
                if expected in extracted_paths:
                    print(f"✅ 找到: {expected}")
                else:
                    print(f"❌ 缺失: {expected}")
                    all_found = False
            
            return all_found
        else:
            print(f"\n❌ 提取数量不正确: 期望 {len(expected_paths)}, 实际 {len(extracted_paths)}")
            return False
        
    except Exception as e:
        print(f"❌ 图片提取模式测试失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 80)
    print("相对路径图片处理功能测试")
    print("=" * 80)
    
    tests = [
        ("图片提取模式测试", test_image_extraction_patterns),
        ("URL构建功能测试", test_url_building),
        ("相对路径处理测试", test_relative_path_processing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！相对路径图片处理功能正常")
        print("\n功能说明:")
        print("1. ✅ 支持绝对路径图片（以/开头）")
        print("2. ✅ 支持相对路径图片（./或直接路径）")
        print("3. ✅ 支持完整URL图片（http/https）")
        print("4. ✅ 自动构建完整下载URL")
        print("5. ✅ 正确更新本地路径引用")
    else:
        print("⚠️  部分测试失败，需要检查相关功能")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
