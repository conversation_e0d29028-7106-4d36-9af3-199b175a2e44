{"test_summary": {"total_tests": 6, "passed_tests": 6, "failed_tests": 0, "success_rate": "100.0%", "execution_time": "2025-05-29T23:38:59.781251"}, "test_results": [{"test_name": "数据生成测试", "success": true, "timestamp": "2025-05-29T23:38:59.533171", "details": "生成5个基础工单，5个增量工单，16个附件"}, {"test_name": "附件处理测试", "success": true, "timestamp": "2025-05-29T23:38:59.589172", "details": "处理9个附件（3个图片），ZIP包含0个文件"}, {"test_name": "图片内容检测测试", "success": true, "timestamp": "2025-05-29T23:38:59.590171", "details": "工单描述图片: 3, 评论图片: 3"}, {"test_name": "全量模式测试", "success": true, "timestamp": "2025-05-29T23:38:59.628738", "details": "目录模式: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_e0hqdee3\\full_20250529_233859, ZIP模式: C:\\Users\\<USER>\\AppData\\Local\\Temp\\comprehensive_test_e0hqdee3\\full_20250529_233859.zip"}, {"test_name": "增量模式测试", "success": true, "timestamp": "2025-05-29T23:38:59.698739", "details": "基础版本: 20250529_233859, 新增: 3, 更新: 1"}, {"test_name": "Patch应用测试", "success": true, "timestamp": "2025-05-29T23:38:59.780252", "details": "解析1个Patch, 空目录提取3个工单, 合并后8个工单"}], "conclusion": "所有测试通过"}