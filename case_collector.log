2025-05-24 11:41:01,588 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:41:01,592 - INFO - ====== WebDriver manager ======
2025-05-24 11:41:02,690 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-24 11:41:02,731 - ERROR - WebDriver初始化失败: Could not reach host. Are you offline?
2025-05-24 11:41:02,887 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:41:02,889 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpg7e41_18\test.json
2025-05-24 11:41:33,673 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:41:33,677 - INFO - WebDriver初始化成功
2025-05-24 11:41:33,681 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:41:33,683 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp9j9lfczo\test.json
2025-05-24 11:42:19,057 - INFO - 创建演示数据...
2025-05-24 11:42:19,057 - INFO - 保存演示数据...
2025-05-24 11:42:19,059 - INFO - 数据已保存到: data\cases_data.json
2025-05-24 11:42:19,061 - INFO - 演示数据已保存到: data\cases_data.json
2025-05-24 11:42:37,784 - INFO - 显示演示数据...
2025-05-24 11:48:58,439 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:48:58,444 - INFO - WebDriver初始化成功
2025-05-24 11:48:58,446 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:48:58,448 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp9a2cv983\test.json
2025-05-24 11:50:10,653 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:50:10,658 - INFO - WebDriver初始化成功
2025-05-24 11:50:10,660 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:50:10,661 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpn83jrsok\test.json
2025-05-24 11:50:19,029 - INFO - 显示演示数据...
2025-05-24 11:50:40,626 - INFO - ============================================================
2025-05-24 11:50:40,626 - INFO - AkroCare工单采集程序启动
2025-05-24 11:50:40,627 - INFO - 登录邮箱: <EMAIL>
2025-05-24 11:50:40,627 - INFO - 增量更新: 启用
2025-05-24 11:50:40,627 - INFO - 开始时间: 2025-05-24 11:50:40
2025-05-24 11:50:40,627 - INFO - ============================================================
2025-05-24 11:50:40,628 - INFO - ====== WebDriver manager ======
2025-05-24 11:50:41,747 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-24 11:50:41,762 - ERROR - WebDriver初始化失败: Could not reach host. Are you offline?
2025-05-24 11:50:41,763 - ERROR - 程序执行失败: Could not reach host. Are you offline?
2025-05-24 11:54:03,910 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:54:03,915 - INFO - 使用自动下载的ChromeDriver
2025-05-24 11:54:03,916 - INFO - WebDriver初始化成功
2025-05-24 11:54:03,918 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:54:03,920 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpdp1d5xy1\test.json
2025-05-24 11:54:39,529 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:54:39,533 - INFO - 使用自动下载的ChromeDriver
2025-05-24 11:54:39,534 - INFO - WebDriver初始化成功
2025-05-24 11:54:39,536 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:54:39,537 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpyqmsdm04\test.json
2025-05-24 11:56:00,950 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 11:56:00,954 - INFO - 使用自动下载的ChromeDriver
2025-05-24 11:56:00,955 - INFO - WebDriver初始化成功
2025-05-24 11:56:00,957 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 11:56:00,958 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmps_j8_a2r\\last_update.json'
2025-05-24 11:56:00,959 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 11:56:00,959 - INFO - WebDriver初始化成功
2025-05-24 11:56:00,961 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpe5i2wr0l\test.json
2025-05-24 12:01:17,246 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:01:17,251 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:01:17,252 - INFO - WebDriver初始化成功
2025-05-24 12:01:17,254 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:01:17,255 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpyl6w7au2\\last_update.json'
2025-05-24 12:01:17,256 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:01:17,256 - INFO - WebDriver初始化成功
2025-05-24 12:01:17,258 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpau_j5t3v\test.json
2025-05-24 12:01:35,439 - INFO - ============================================================
2025-05-24 12:01:35,440 - INFO - AkroCare工单采集程序启动
2025-05-24 12:01:35,440 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:01:35,440 - INFO - ChromeDriver: 自定义路径
2025-05-24 12:01:35,440 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:01:35,440 - INFO - 增量更新: 启用
2025-05-24 12:01:35,440 - INFO - 开始时间: 2025-05-24 12:01:35
2025-05-24 12:01:35,440 - INFO - ============================================================
2025-05-24 12:01:35,441 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:01:36,756 - INFO - WebDriver初始化成功
2025-05-24 12:01:36,757 - INFO - 开始登录...
2025-05-24 12:01:50,830 - INFO - 登录成功
2025-05-24 12:01:50,834 - INFO - 导航到工单页面...
2025-05-24 12:01:53,939 - INFO - 成功导航到工单页面
2025-05-24 12:01:53,939 - INFO - 开始采集工单数据...
2025-05-24 12:01:53,939 - INFO - 正在获取第 1 页工单数据...
2025-05-24 12:01:53,960 - ERROR - 获取工单概要信息失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:01:53,961 - INFO - 总共获取到 0 条工单概要信息
2025-05-24 12:01:53,962 - WARNING - 没有获取到工单概要信息
2025-05-24 12:01:53,962 - WARNING - 没有采集到工单数据
2025-05-24 12:01:56,074 - INFO - 资源已释放
2025-05-24 12:08:39,023 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:08:39,027 - INFO - 使用自动下载的ChromeDriver
2025-05-24 12:08:39,028 - INFO - WebDriver初始化成功
2025-05-24 12:08:39,030 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:08:39,032 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpa1zwy9w3\\last_update.json'
2025-05-24 12:08:39,032 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:08:39,033 - INFO - WebDriver初始化成功
2025-05-24 12:08:39,038 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmprse0phia\test.json
2025-05-24 12:34:17,985 - INFO - ============================================================
2025-05-24 12:34:17,985 - INFO - AkroCare工单采集程序启动
2025-05-24 12:34:17,985 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:34:17,985 - INFO - ChromeDriver: 自动下载
2025-05-24 12:34:17,985 - INFO - API请求方式: requests
2025-05-24 12:34:17,985 - INFO - 增量更新: 启用
2025-05-24 12:34:17,986 - INFO - 开始时间: 2025-05-24 12:34:17
2025-05-24 12:34:17,986 - INFO - ============================================================
2025-05-24 12:34:17,986 - INFO - ====== WebDriver manager ======
2025-05-24 12:34:18,992 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-24 12:34:19,008 - ERROR - WebDriver初始化失败: Could not reach host. Are you offline?
2025-05-24 12:34:19,009 - ERROR - 程序执行失败: Could not reach host. Are you offline?
2025-05-24 12:34:48,801 - INFO - ============================================================
2025-05-24 12:34:48,802 - INFO - AkroCare工单采集程序启动
2025-05-24 12:34:48,802 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:34:48,802 - INFO - ChromeDriver: 自定义路径
2025-05-24 12:34:48,802 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:34:48,802 - INFO - API请求方式: requests
2025-05-24 12:34:48,802 - INFO - 增量更新: 启用
2025-05-24 12:34:48,803 - INFO - 开始时间: 2025-05-24 12:34:48
2025-05-24 12:34:48,803 - INFO - ============================================================
2025-05-24 12:34:48,804 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:34:49,980 - INFO - WebDriver初始化成功
2025-05-24 12:34:49,981 - INFO - 开始登录...
2025-05-24 12:35:04,775 - INFO - 登录成功
2025-05-24 12:35:04,784 - INFO - Session headers已更新
2025-05-24 12:35:04,784 - INFO - 导航到工单页面...
2025-05-24 12:35:07,896 - INFO - 成功导航到工单页面
2025-05-24 12:35:07,896 - INFO - 开始采集工单数据...
2025-05-24 12:35:07,896 - INFO - 正在获取第 1 页工单数据...
2025-05-24 12:35:07,911 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:35:07,911 - ERROR - API请求失败：无响应数据
2025-05-24 12:35:07,911 - INFO - 尝试使用Selenium方式重新请求...
2025-05-24 12:35:37,925 - ERROR - Selenium POST请求失败: Message: script timeout
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF70155CF45+75717]
	GetHandleVerifier [0x00007FF70155CFA0+75808]
	(No symbol) [0x00007FF701328DCC]
	(No symbol) [0x00007FF7013D0C69]
	(No symbol) [0x00007FF7013A737A]
	(No symbol) [0x00007FF7013CF39C]
	(No symbol) [0x00007FF7013A7153]
	(No symbol) [0x00007FF701370421]
	(No symbol) [0x00007FF7013711B3]
	GetHandleVerifier [0x00007FF70185D71D+3223453]
	GetHandleVerifier [0x00007FF701857CC2+3200322]
	GetHandleVerifier [0x00007FF701875AF3+3322739]
	GetHandleVerifier [0x00007FF701576A1A+180890]
	GetHandleVerifier [0x00007FF70157E11F+211359]
	GetHandleVerifier [0x00007FF701565294+109332]
	GetHandleVerifier [0x00007FF701565442+109762]
	GetHandleVerifier [0x00007FF70154BA59+4825]
	BaseThreadInitThunk [0x00007FFAE2997374+20]
	RtlUserThreadStart [0x00007FFAE47DCC91+33]

2025-05-24 12:35:37,926 - INFO - 总共获取到 0 条工单概要信息
2025-05-24 12:35:37,927 - WARNING - 没有获取到工单概要信息
2025-05-24 12:35:37,927 - WARNING - 没有采集到工单数据
2025-05-24 12:35:40,042 - INFO - 资源已释放
2025-05-24 12:40:19,811 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:40:19,815 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:40:19,816 - INFO - WebDriver初始化成功
2025-05-24 12:40:19,818 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:40:19,821 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpwfoz_rz5\\last_update.json'
2025-05-24 12:40:19,822 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:40:19,822 - INFO - WebDriver初始化成功
2025-05-24 12:40:19,824 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 12:40:19,825 - ERROR - Requests POST请求失败: Mock.keys() returned a non-iterable (type Mock)
2025-05-24 12:40:19,825 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:40:19,828 - ERROR - Selenium POST请求失败: Object of type Mock is not JSON serializable
2025-05-24 12:40:19,829 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpkljn51mm\test.json
2025-05-24 12:40:19,833 - INFO - Session headers和cookies已更新
2025-05-24 12:41:43,347 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:41:43,351 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:41:43,351 - INFO - WebDriver初始化成功
2025-05-24 12:41:43,353 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:41:43,357 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpkjcohri4\\last_update.json'
2025-05-24 12:41:43,358 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:41:43,358 - INFO - WebDriver初始化成功
2025-05-24 12:41:43,361 - ERROR - Requests POST请求失败: Mock.keys() returned a non-iterable (type Mock)
2025-05-24 12:41:43,361 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:41:43,365 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpu0pm0tfb\test.json
2025-05-24 12:41:43,369 - INFO - Session headers和cookies已更新
2025-05-24 12:42:27,585 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:42:27,592 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:42:27,594 - INFO - WebDriver初始化成功
2025-05-24 12:42:27,597 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:42:27,602 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmphx77rlyq\\last_update.json'
2025-05-24 12:42:27,603 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:42:27,604 - INFO - WebDriver初始化成功
2025-05-24 12:42:27,607 - ERROR - Requests POST请求失败: object of type 'Mock' has no len()
2025-05-24 12:42:27,607 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:42:27,614 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmptre3juhd\test.json
2025-05-24 12:42:27,620 - INFO - Session headers和cookies已更新
2025-05-24 12:43:20,084 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:43:20,088 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:43:20,088 - INFO - WebDriver初始化成功
2025-05-24 12:43:20,090 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:43:20,093 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpm6cwj8mh\\last_update.json'
2025-05-24 12:43:20,094 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:43:20,095 - INFO - WebDriver初始化成功
2025-05-24 12:43:20,096 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 12:43:20,111 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:43:20,111 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:43:20,115 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpya453rxh\test.json
2025-05-24 12:43:20,119 - INFO - Session headers和cookies已更新
2025-05-24 12:43:33,796 - INFO - ============================================================
2025-05-24 12:43:33,797 - INFO - AkroCare工单采集程序启动
2025-05-24 12:43:33,797 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:43:33,797 - INFO - ChromeDriver: 自定义路径
2025-05-24 12:43:33,797 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:43:33,797 - INFO - API请求方式: requests
2025-05-24 12:43:33,797 - INFO - 增量更新: 启用
2025-05-24 12:43:33,798 - INFO - 开始时间: 2025-05-24 12:43:33
2025-05-24 12:43:33,798 - INFO - ============================================================
2025-05-24 12:43:33,798 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:43:34,895 - INFO - WebDriver初始化成功
2025-05-24 12:43:34,895 - INFO - 开始登录...
2025-05-24 12:43:48,950 - INFO - 登录成功
2025-05-24 12:43:48,967 - INFO - Session headers和cookies已更新
2025-05-24 12:43:48,967 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 12:43:48,968 - INFO - 导航到工单页面...
2025-05-24 12:43:52,121 - INFO - Session headers和cookies已更新
2025-05-24 12:43:52,121 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 12:43:52,121 - INFO - 开始采集工单数据...
2025-05-24 12:43:52,121 - INFO - 正在获取第 1 页工单数据...
2025-05-24 12:43:52,140 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:43:52,140 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:43:52,140 - ERROR - API请求失败：无响应数据
2025-05-24 12:43:52,140 - INFO - 尝试使用Selenium方式重新请求...
2025-05-24 12:44:05,050 - INFO - 总共获取到 0 条工单概要信息
2025-05-24 12:44:05,050 - WARNING - 没有获取到工单概要信息
2025-05-24 12:44:05,050 - WARNING - 没有采集到工单数据
2025-05-24 12:44:07,160 - INFO - 用户中断程序
2025-05-24 12:50:28,304 - INFO - 已禁用代理
2025-05-24 12:50:28,304 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 12:50:28,308 - INFO - 已禁用代理
2025-05-24 12:50:28,309 - INFO - 使用自动下载的ChromeDriver
2025-05-24 12:50:28,310 - INFO - WebDriver初始化成功
2025-05-24 12:50:28,312 - INFO - 已禁用代理
2025-05-24 12:50:28,313 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 12:50:28,314 - INFO - 已禁用代理
2025-05-24 12:50:28,316 - INFO - 已禁用代理
2025-05-24 12:50:28,317 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpk40nnd61\\last_update.json'
2025-05-24 12:50:28,317 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 12:50:28,319 - INFO - WebDriver初始化成功
2025-05-24 12:50:28,320 - INFO - 已禁用代理
2025-05-24 12:50:28,320 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 12:50:28,337 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:50:28,338 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:50:28,341 - INFO - 已禁用代理
2025-05-24 12:50:28,343 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp5_m25nj5\test.json
2025-05-24 12:50:28,348 - INFO - 已禁用代理
2025-05-24 12:50:28,349 - INFO - Session headers和cookies已更新
2025-05-24 12:54:03,317 - INFO - 已禁用代理
2025-05-24 12:56:20,607 - INFO - ============================================================
2025-05-24 12:56:20,607 - INFO - AkroCare工单采集程序启动
2025-05-24 12:56:20,607 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:56:20,607 - INFO - ChromeDriver: 自动下载
2025-05-24 12:56:20,607 - INFO - API请求方式: requests
2025-05-24 12:56:20,607 - INFO - 代理设置: 禁用
2025-05-24 12:56:20,607 - INFO - 增量更新: 启用
2025-05-24 12:56:20,608 - INFO - 开始时间: 2025-05-24 12:56:20
2025-05-24 12:56:20,608 - INFO - ============================================================
2025-05-24 12:56:20,608 - INFO - 已禁用代理
2025-05-24 12:56:20,608 - INFO - ====== WebDriver manager ======
2025-05-24 12:56:21,643 - INFO - Get LATEST chromedriver version for google-chrome
2025-05-24 12:56:21,658 - ERROR - WebDriver初始化失败: Could not reach host. Are you offline?
2025-05-24 12:56:21,659 - ERROR - 程序执行失败: Could not reach host. Are you offline?
2025-05-24 12:56:30,357 - INFO - ============================================================
2025-05-24 12:56:30,358 - INFO - AkroCare工单采集程序启动
2025-05-24 12:56:30,358 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:56:30,358 - INFO - ChromeDriver: 自定义路径
2025-05-24 12:56:30,358 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:56:30,358 - INFO - API请求方式: requests
2025-05-24 12:56:30,358 - INFO - 代理设置: 禁用
2025-05-24 12:56:30,358 - INFO - 增量更新: 启用
2025-05-24 12:56:30,358 - INFO - 开始时间: 2025-05-24 12:56:30
2025-05-24 12:56:30,359 - INFO - ============================================================
2025-05-24 12:56:30,359 - INFO - 已禁用代理
2025-05-24 12:56:30,359 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:56:31,518 - INFO - WebDriver初始化成功
2025-05-24 12:56:31,518 - INFO - 开始登录...
2025-05-24 12:56:50,966 - INFO - 登录成功
2025-05-24 12:56:50,979 - INFO - Session headers和cookies已更新
2025-05-24 12:56:50,979 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 12:56:50,979 - INFO - 导航到工单页面...
2025-05-24 12:56:54,107 - INFO - Session headers和cookies已更新
2025-05-24 12:56:54,107 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 12:56:54,107 - INFO - 开始采集工单数据...
2025-05-24 12:56:54,108 - INFO - 正在获取第 1 页工单数据...
2025-05-24 12:56:54,125 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:56:54,125 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:56:54,125 - ERROR - API请求失败：无响应数据
2025-05-24 12:56:54,125 - INFO - 尝试使用Selenium方式重新请求...
2025-05-24 12:57:24,136 - ERROR - Selenium POST请求失败: Message: script timeout
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF70155CF45+75717]
	GetHandleVerifier [0x00007FF70155CFA0+75808]
	(No symbol) [0x00007FF701328DCC]
	(No symbol) [0x00007FF7013D0C69]
	(No symbol) [0x00007FF7013A737A]
	(No symbol) [0x00007FF7013CF39C]
	(No symbol) [0x00007FF7013A7153]
	(No symbol) [0x00007FF701370421]
	(No symbol) [0x00007FF7013711B3]
	GetHandleVerifier [0x00007FF70185D71D+3223453]
	GetHandleVerifier [0x00007FF701857CC2+3200322]
	GetHandleVerifier [0x00007FF701875AF3+3322739]
	GetHandleVerifier [0x00007FF701576A1A+180890]
	GetHandleVerifier [0x00007FF70157E11F+211359]
	GetHandleVerifier [0x00007FF701565294+109332]
	GetHandleVerifier [0x00007FF701565442+109762]
	GetHandleVerifier [0x00007FF70154BA59+4825]
	BaseThreadInitThunk [0x00007FFAE2997374+20]
	RtlUserThreadStart [0x00007FFAE47DCC91+33]

2025-05-24 12:57:24,137 - INFO - 总共获取到 0 条工单概要信息
2025-05-24 12:57:24,137 - WARNING - 没有获取到工单概要信息
2025-05-24 12:57:24,138 - WARNING - 没有采集到工单数据
2025-05-24 12:57:26,264 - INFO - 资源已释放
2025-05-24 12:57:54,239 - INFO - ============================================================
2025-05-24 12:57:54,240 - INFO - AkroCare工单采集程序启动
2025-05-24 12:57:54,240 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:57:54,240 - INFO - ChromeDriver: 自定义路径
2025-05-24 12:57:54,240 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:57:54,240 - INFO - API请求方式: requests
2025-05-24 12:57:54,240 - INFO - 代理设置: 禁用
2025-05-24 12:57:54,240 - INFO - 增量更新: 启用
2025-05-24 12:57:54,241 - INFO - 开始时间: 2025-05-24 12:57:54
2025-05-24 12:57:54,241 - INFO - ============================================================
2025-05-24 12:57:54,241 - INFO - 已禁用代理
2025-05-24 12:57:54,242 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:57:55,353 - INFO - WebDriver初始化成功
2025-05-24 12:57:55,353 - INFO - 开始登录...
2025-05-24 12:58:14,885 - INFO - 登录成功
2025-05-24 12:58:14,900 - INFO - Session headers和cookies已更新
2025-05-24 12:58:14,900 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 12:58:14,901 - INFO - 导航到工单页面...
2025-05-24 12:58:18,026 - INFO - Session headers和cookies已更新
2025-05-24 12:58:18,027 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 12:58:18,027 - INFO - 开始采集工单数据...
2025-05-24 12:58:18,027 - INFO - 正在获取第 1 页工单数据...
2025-05-24 12:58:18,046 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-24 12:58:18,046 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 12:58:18,046 - ERROR - API请求失败：无响应数据
2025-05-24 12:58:18,046 - INFO - 尝试使用Selenium方式重新请求...
2025-05-24 12:58:39,546 - ERROR - Selenium POST请求失败: Message: script timeout
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF70155CF45+75717]
	GetHandleVerifier [0x00007FF70155CFA0+75808]
	(No symbol) [0x00007FF701328DCC]
	(No symbol) [0x00007FF7013D0C69]
	(No symbol) [0x00007FF7013A737A]
	(No symbol) [0x00007FF7013CF39C]
	(No symbol) [0x00007FF7013A7153]
	(No symbol) [0x00007FF701370421]
	(No symbol) [0x00007FF7013711B3]
	GetHandleVerifier [0x00007FF70185D71D+3223453]
	GetHandleVerifier [0x00007FF701857CC2+3200322]
	GetHandleVerifier [0x00007FF701875AF3+3322739]
	GetHandleVerifier [0x00007FF701576A1A+180890]
	GetHandleVerifier [0x00007FF70157E11F+211359]
	GetHandleVerifier [0x00007FF701565294+109332]
	GetHandleVerifier [0x00007FF701565442+109762]
	GetHandleVerifier [0x00007FF70154BA59+4825]
	BaseThreadInitThunk [0x00007FFAE2997374+20]
	RtlUserThreadStart [0x00007FFAE47DCC91+33]

2025-05-24 12:58:39,547 - INFO - 总共获取到 0 条工单概要信息
2025-05-24 12:58:39,547 - WARNING - 没有获取到工单概要信息
2025-05-24 12:58:39,547 - WARNING - 没有采集到工单数据
2025-05-24 12:58:41,676 - INFO - 资源已释放
2025-05-24 12:58:47,525 - INFO - ============================================================
2025-05-24 12:58:47,525 - INFO - AkroCare工单采集程序启动
2025-05-24 12:58:47,525 - INFO - 登录邮箱: <EMAIL>
2025-05-24 12:58:47,525 - INFO - ChromeDriver: 自定义路径
2025-05-24 12:58:47,525 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:58:47,526 - INFO - API请求方式: requests
2025-05-24 12:58:47,526 - INFO - 代理设置: 禁用
2025-05-24 12:58:47,526 - INFO - 增量更新: 启用
2025-05-24 12:58:47,526 - INFO - 开始时间: 2025-05-24 12:58:47
2025-05-24 12:58:47,526 - INFO - ============================================================
2025-05-24 12:58:47,527 - INFO - 已禁用代理
2025-05-24 12:58:47,527 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 12:58:48,677 - INFO - WebDriver初始化成功
2025-05-24 12:58:48,678 - INFO - 开始登录...
2025-05-24 12:59:24,500 - ERROR - 登录过程中发生错误: Message: timeout: Timed out receiving message from renderer: 30.000
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF70155CF45+75717]
	GetHandleVerifier [0x00007FF70155CFA0+75808]
	(No symbol) [0x00007FF701328F9A]
	(No symbol) [0x00007FF7013164EC]
	(No symbol) [0x00007FF7013161DA]
	(No symbol) [0x00007FF701313E8A]
	(No symbol) [0x00007FF70131483F]
	(No symbol) [0x00007FF7013233AE]
	(No symbol) [0x00007FF7013397E1]
	(No symbol) [0x00007FF70134091A]
	(No symbol) [0x00007FF701314FAD]
	(No symbol) [0x00007FF7013394D0]
	(No symbol) [0x00007FF7013CF276]
	(No symbol) [0x00007FF7013A7153]
	(No symbol) [0x00007FF701370421]
	(No symbol) [0x00007FF7013711B3]
	GetHandleVerifier [0x00007FF70185D71D+3223453]
	GetHandleVerifier [0x00007FF701857CC2+3200322]
	GetHandleVerifier [0x00007FF701875AF3+3322739]
	GetHandleVerifier [0x00007FF701576A1A+180890]
	GetHandleVerifier [0x00007FF70157E11F+211359]
	GetHandleVerifier [0x00007FF701565294+109332]
	GetHandleVerifier [0x00007FF701565442+109762]
	GetHandleVerifier [0x00007FF70154BA59+4825]
	BaseThreadInitThunk [0x00007FFAE2997374+20]
	RtlUserThreadStart [0x00007FFAE47DCC91+33]

2025-05-24 12:59:24,501 - ERROR - 登录失败，程序退出
2025-05-24 12:59:26,623 - INFO - 资源已释放
2025-05-24 13:00:05,583 - INFO - ============================================================
2025-05-24 13:00:05,584 - INFO - AkroCare工单采集程序启动
2025-05-24 13:00:05,584 - INFO - 登录邮箱: <EMAIL>
2025-05-24 13:00:05,584 - INFO - ChromeDriver: 自定义路径
2025-05-24 13:00:05,584 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:00:05,584 - INFO - API请求方式: requests
2025-05-24 13:00:05,584 - INFO - 代理设置: 禁用
2025-05-24 13:00:05,585 - INFO - 增量更新: 启用
2025-05-24 13:00:05,585 - INFO - 开始时间: 2025-05-24 13:00:05
2025-05-24 13:00:05,585 - INFO - ============================================================
2025-05-24 13:00:05,585 - INFO - 已禁用代理
2025-05-24 13:00:05,586 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:00:06,673 - INFO - WebDriver初始化成功
2025-05-24 13:00:06,673 - INFO - 开始登录...
2025-05-24 13:00:21,014 - INFO - 登录成功
2025-05-24 13:00:21,028 - INFO - Session headers和cookies已更新
2025-05-24 13:00:21,029 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 13:00:21,029 - INFO - 导航到工单页面...
2025-05-24 13:00:24,151 - INFO - Session headers和cookies已更新
2025-05-24 13:00:24,152 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 13:00:24,152 - INFO - 开始采集工单数据...
2025-05-24 13:00:24,152 - INFO - 正在获取第 1 页工单数据...
2025-05-24 13:00:24,532 - INFO - 没有更多工单数据
2025-05-24 13:00:24,532 - INFO - 总共获取到 0 条工单概要信息
2025-05-24 13:00:24,532 - WARNING - 没有获取到工单概要信息
2025-05-24 13:00:24,532 - WARNING - 没有采集到工单数据
2025-05-24 13:00:26,644 - INFO - 资源已释放
2025-05-24 13:00:45,470 - INFO - ============================================================
2025-05-24 13:00:45,471 - INFO - AkroCare工单采集程序启动
2025-05-24 13:00:45,471 - INFO - 登录邮箱: <EMAIL>
2025-05-24 13:00:45,471 - INFO - ChromeDriver: 自定义路径
2025-05-24 13:00:45,471 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:00:45,471 - INFO - API请求方式: requests
2025-05-24 13:00:45,471 - INFO - 代理设置: 禁用
2025-05-24 13:00:45,471 - INFO - 增量更新: 启用
2025-05-24 13:00:45,472 - INFO - 开始时间: 2025-05-24 13:00:45
2025-05-24 13:00:45,472 - INFO - ============================================================
2025-05-24 13:00:45,472 - INFO - 已禁用代理
2025-05-24 13:00:45,473 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:00:46,652 - INFO - WebDriver初始化成功
2025-05-24 13:00:46,652 - INFO - 开始登录...
2025-05-24 13:01:00,317 - INFO - 登录成功
2025-05-24 13:01:00,330 - INFO - Session headers和cookies已更新
2025-05-24 13:01:00,330 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 13:01:00,331 - INFO - 导航到工单页面...
2025-05-24 13:01:03,448 - INFO - Session headers和cookies已更新
2025-05-24 13:01:03,449 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 13:01:03,449 - INFO - 开始采集工单数据...
2025-05-24 13:01:03,449 - INFO - 正在获取第 1 页工单数据...
2025-05-24 13:01:03,853 - INFO - 第 1 页获取到 5 条工单
2025-05-24 13:01:03,854 - INFO - 总共获取到 5 条工单概要信息
2025-05-24 13:01:03,854 - INFO - 正在处理第 1/5 个工单: 7514
2025-05-24 13:01:03,854 - INFO - 正在获取工单 7514 的详细信息...
2025-05-24 13:01:07,449 - INFO - 工单 7514 提取到 0 条评论
2025-05-24 13:01:07,450 - INFO - 工单 7514 没有附件
2025-05-24 13:01:07,651 - INFO - 文件下载成功: data\attachments\7514\images\1747205872926102.png
2025-05-24 13:01:07,652 - INFO - 图片下载成功: 1747205872926102.png
2025-05-24 13:01:07,652 - INFO - 工单 7514 详细信息采集完成
2025-05-24 13:01:09,653 - INFO - 正在处理第 2/5 个工单: 6867
2025-05-24 13:01:09,653 - INFO - 正在获取工单 6867 的详细信息...
2025-05-24 13:01:12,950 - INFO - 工单 6867 提取到 0 条评论
2025-05-24 13:01:12,951 - INFO - 工单 6867 没有附件
2025-05-24 13:01:12,952 - INFO - 工单 6867 详细信息采集完成
2025-05-24 13:01:14,954 - INFO - 正在处理第 3/5 个工单: 5829
2025-05-24 13:01:14,954 - INFO - 正在获取工单 5829 的详细信息...
2025-05-24 13:01:18,161 - INFO - 工单 5829 提取到 0 条评论
2025-05-24 13:01:18,162 - INFO - 工单 5829 没有附件
2025-05-24 13:01:18,163 - INFO - 工单 5829 详细信息采集完成
2025-05-24 13:01:20,168 - INFO - 正在处理第 4/5 个工单: 4776
2025-05-24 13:01:20,168 - INFO - 正在获取工单 4776 的详细信息...
2025-05-24 13:01:23,358 - INFO - 工单 4776 提取到 0 条评论
2025-05-24 13:01:23,359 - INFO - 工单 4776 没有附件
2025-05-24 13:01:23,360 - INFO - 工单 4776 详细信息采集完成
2025-05-24 13:01:25,366 - INFO - 正在处理第 5/5 个工单: 4283
2025-05-24 13:01:25,366 - INFO - 正在获取工单 4283 的详细信息...
2025-05-24 13:01:28,903 - INFO - 工单 4283 提取到 0 条评论
2025-05-24 13:01:28,905 - INFO - 工单 4283 提取到 0 个附件
2025-05-24 13:01:29,049 - INFO - 文件下载成功: data\attachments\4283\images\1717727357199915.png
2025-05-24 13:01:29,050 - INFO - 图片下载成功: 1717727357199915.png
2025-05-24 13:01:29,050 - INFO - 工单 4283 详细信息采集完成
2025-05-24 13:01:31,060 - INFO - 数据已保存到: data\last_update.json
2025-05-24 13:01:31,061 - INFO - 数据已保存到: data\cases_data.json
2025-05-24 13:01:31,061 - INFO - 已保存 5 条工单数据
2025-05-24 13:01:31,062 - INFO - ============================================================
2025-05-24 13:01:31,062 - INFO - 采集完成！共采集 5 条工单
2025-05-24 13:01:31,062 - INFO - 数据保存位置: data\cases_data.json
2025-05-24 13:01:31,062 - INFO - 附件保存位置: data\attachments
2025-05-24 13:01:31,062 - INFO - 结束时间: 2025-05-24 13:01:31
2025-05-24 13:01:31,062 - INFO - ============================================================
2025-05-24 13:01:33,199 - INFO - 资源已释放
2025-05-24 13:04:38,328 - INFO - ============================================================
2025-05-24 13:04:38,328 - INFO - AkroCare工单采集程序启动
2025-05-24 13:04:38,328 - INFO - 登录邮箱: <EMAIL>
2025-05-24 13:04:38,329 - INFO - ChromeDriver: 自定义路径
2025-05-24 13:04:38,329 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:04:38,329 - INFO - API请求方式: requests
2025-05-24 13:04:38,329 - INFO - 代理设置: 禁用
2025-05-24 13:04:38,329 - INFO - 增量更新: 启用
2025-05-24 13:04:38,329 - INFO - 开始时间: 2025-05-24 13:04:38
2025-05-24 13:04:38,329 - INFO - ============================================================
2025-05-24 13:04:38,330 - INFO - 已禁用代理
2025-05-24 13:04:38,331 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:04:39,530 - INFO - WebDriver初始化成功
2025-05-24 13:04:39,530 - INFO - 开始登录...
2025-05-24 13:04:53,250 - INFO - 登录成功
2025-05-24 13:04:53,262 - INFO - Session headers和cookies已更新
2025-05-24 13:04:53,262 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 13:04:53,262 - INFO - 导航到工单页面...
2025-05-24 13:04:56,388 - INFO - Session headers和cookies已更新
2025-05-24 13:04:56,388 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 13:04:56,388 - INFO - 开始采集工单数据...
2025-05-24 13:04:56,388 - INFO - 正在获取第 1 页工单数据...
2025-05-24 13:04:56,856 - INFO - 第 1 页获取到 30 条工单
2025-05-24 13:04:57,862 - INFO - 正在获取第 2 页工单数据...
2025-05-24 13:04:58,251 - INFO - 第 2 页获取到 30 条工单
2025-05-24 13:04:59,265 - INFO - 正在获取第 3 页工单数据...
2025-05-24 13:04:59,658 - INFO - 第 3 页获取到 30 条工单
2025-05-24 13:05:00,671 - INFO - 正在获取第 4 页工单数据...
2025-05-24 13:05:01,024 - INFO - 第 4 页获取到 30 条工单
2025-05-24 13:05:02,033 - INFO - 正在获取第 5 页工单数据...
2025-05-24 13:05:02,389 - INFO - 第 5 页获取到 30 条工单
2025-05-24 13:05:03,391 - INFO - 正在获取第 6 页工单数据...
2025-05-24 13:05:03,791 - INFO - 第 6 页获取到 30 条工单
2025-05-24 13:05:04,799 - INFO - 正在获取第 7 页工单数据...
2025-05-24 13:05:05,182 - INFO - 第 7 页获取到 30 条工单
2025-05-24 13:05:06,184 - INFO - 正在获取第 8 页工单数据...
2025-05-24 13:05:06,543 - INFO - 第 8 页获取到 30 条工单
2025-05-24 13:05:07,547 - INFO - 正在获取第 9 页工单数据...
2025-05-24 13:05:07,906 - INFO - 第 9 页获取到 30 条工单
2025-05-24 13:05:08,920 - INFO - 正在获取第 10 页工单数据...
2025-05-24 13:05:09,282 - INFO - 第 10 页获取到 30 条工单
2025-05-24 13:05:10,288 - INFO - 正在获取第 11 页工单数据...
2025-05-24 13:05:10,655 - INFO - 第 11 页获取到 30 条工单
2025-05-24 13:05:11,657 - INFO - 正在获取第 12 页工单数据...
2025-05-24 13:05:12,008 - INFO - 第 12 页获取到 30 条工单
2025-05-24 13:05:13,012 - INFO - 正在获取第 13 页工单数据...
2025-05-24 13:05:13,401 - INFO - 第 13 页获取到 30 条工单
2025-05-24 13:05:14,414 - INFO - 正在获取第 14 页工单数据...
2025-05-24 13:05:14,766 - INFO - 第 14 页获取到 30 条工单
2025-05-24 13:05:15,768 - INFO - 正在获取第 15 页工单数据...
2025-05-24 13:05:16,145 - INFO - 第 15 页获取到 23 条工单
2025-05-24 13:05:16,145 - INFO - 总共获取到 443 条工单概要信息
2025-05-24 13:05:16,145 - INFO - 正在处理第 1/443 个工单: 7410
2025-05-24 13:05:16,146 - INFO - 正在获取工单 7410 的详细信息...
2025-05-24 13:05:19,779 - INFO - 工单 7410 提取到 0 条评论
2025-05-24 13:05:19,780 - INFO - 工单 7410 没有附件
2025-05-24 13:05:19,783 - INFO - 工单 7410 详细信息采集完成
2025-05-24 13:05:21,785 - INFO - 正在处理第 2/443 个工单: 7263
2025-05-24 13:05:21,785 - INFO - 正在获取工单 7263 的详细信息...
2025-05-24 13:05:25,298 - INFO - 工单 7263 提取到 0 条评论
2025-05-24 13:05:25,300 - INFO - 工单 7263 没有附件
2025-05-24 13:05:25,300 - INFO - 工单 7263 详细信息采集完成
2025-05-24 13:05:27,308 - INFO - 正在处理第 3/443 个工单: 7246
2025-05-24 13:05:27,308 - INFO - 正在获取工单 7246 的详细信息...
2025-05-24 13:05:30,793 - INFO - 工单 7246 提取到 0 条评论
2025-05-24 13:05:30,794 - INFO - 工单 7246 没有附件
2025-05-24 13:05:30,795 - INFO - 工单 7246 详细信息采集完成
2025-05-24 13:05:32,809 - INFO - 正在处理第 4/443 个工单: 7051
2025-05-24 13:05:32,809 - INFO - 正在获取工单 7051 的详细信息...
2025-05-24 13:05:36,285 - INFO - 工单 7051 提取到 0 条评论
2025-05-24 13:05:36,286 - INFO - 工单 7051 没有附件
2025-05-24 13:05:36,287 - INFO - 工单 7051 详细信息采集完成
2025-05-24 13:05:38,301 - INFO - 正在处理第 5/443 个工单: 6921
2025-05-24 13:05:38,301 - INFO - 正在获取工单 6921 的详细信息...
2025-05-24 13:05:41,798 - INFO - 工单 6921 提取到 0 条评论
2025-05-24 13:05:41,799 - INFO - 工单 6921 没有附件
2025-05-24 13:05:41,802 - INFO - 工单 6921 详细信息采集完成
2025-05-24 13:05:43,816 - INFO - 正在处理第 6/443 个工单: 6896
2025-05-24 13:05:43,816 - INFO - 正在获取工单 6896 的详细信息...
2025-05-24 13:05:47,327 - INFO - 工单 6896 提取到 0 条评论
2025-05-24 13:05:47,328 - INFO - 工单 6896 没有附件
2025-05-24 13:05:47,522 - INFO - 文件下载成功: data\attachments\6896\images\1742549605173370.png
2025-05-24 13:05:47,523 - INFO - 图片下载成功: 1742549605173370.png
2025-05-24 13:05:47,523 - INFO - 工单 6896 详细信息采集完成
2025-05-24 13:05:49,539 - INFO - 正在处理第 7/443 个工单: 6877
2025-05-24 13:05:49,539 - INFO - 正在获取工单 6877 的详细信息...
2025-05-24 13:05:53,049 - INFO - 工单 6877 提取到 0 条评论
2025-05-24 13:05:53,050 - INFO - 工单 6877 没有附件
2025-05-24 13:05:53,050 - INFO - 工单 6877 详细信息采集完成
2025-05-24 13:05:55,057 - INFO - 正在处理第 8/443 个工单: 6871
2025-05-24 13:05:55,057 - INFO - 正在获取工单 6871 的详细信息...
2025-05-24 13:05:58,548 - INFO - 工单 6871 提取到 0 条评论
2025-05-24 13:05:58,549 - INFO - 工单 6871 没有附件
2025-05-24 13:05:58,550 - INFO - 工单 6871 详细信息采集完成
2025-05-24 13:06:00,551 - INFO - 正在处理第 9/443 个工单: 6810
2025-05-24 13:06:00,551 - INFO - 正在获取工单 6810 的详细信息...
2025-05-24 13:06:04,264 - INFO - 工单 6810 提取到 0 条评论
2025-05-24 13:06:04,265 - INFO - 工单 6810 没有附件
2025-05-24 13:06:04,613 - INFO - 文件下载成功: data\attachments\6810\images\1742178854911463.png
2025-05-24 13:06:04,614 - INFO - 图片下载成功: 1742178854911463.png
2025-05-24 13:06:04,614 - INFO - 工单 6810 详细信息采集完成
2025-05-24 13:06:06,627 - INFO - 正在处理第 10/443 个工单: 6744
2025-05-24 13:06:06,627 - INFO - 正在获取工单 6744 的详细信息...
2025-05-24 13:06:09,821 - INFO - 工单 6744 提取到 0 条评论
2025-05-24 13:06:09,822 - INFO - 工单 6744 没有附件
2025-05-24 13:06:09,823 - INFO - 工单 6744 详细信息采集完成
2025-05-24 13:06:11,831 - INFO - 正在处理第 11/443 个工单: 6743
2025-05-24 13:06:11,831 - INFO - 正在获取工单 6743 的详细信息...
2025-05-24 13:06:15,137 - INFO - 工单 6743 提取到 0 条评论
2025-05-24 13:06:15,139 - INFO - 工单 6743 没有附件
2025-05-24 13:06:15,141 - INFO - 工单 6743 详细信息采集完成
2025-05-24 13:06:17,148 - INFO - 正在处理第 12/443 个工单: 6741
2025-05-24 13:06:17,148 - INFO - 正在获取工单 6741 的详细信息...
2025-05-24 13:06:20,691 - INFO - 工单 6741 提取到 0 条评论
2025-05-24 13:06:20,692 - INFO - 工单 6741 提取到 0 个附件
2025-05-24 13:06:20,694 - INFO - 工单 6741 详细信息采集完成
2025-05-24 13:06:22,695 - INFO - 正在处理第 13/443 个工单: 6723
2025-05-24 13:06:22,695 - INFO - 正在获取工单 6723 的详细信息...
2025-05-24 13:06:26,177 - INFO - 工单 6723 提取到 0 条评论
2025-05-24 13:06:26,178 - INFO - 工单 6723 没有附件
2025-05-24 13:06:26,179 - INFO - 工单 6723 详细信息采集完成
2025-05-24 13:06:28,191 - INFO - 正在处理第 14/443 个工单: 6722
2025-05-24 13:06:28,191 - INFO - 正在获取工单 6722 的详细信息...
2025-05-24 13:06:31,742 - INFO - 工单 6722 提取到 0 条评论
2025-05-24 13:06:31,743 - INFO - 工单 6722 没有附件
2025-05-24 13:06:31,744 - INFO - 工单 6722 详细信息采集完成
2025-05-24 13:06:33,751 - INFO - 正在处理第 15/443 个工单: 6703
2025-05-24 13:06:33,751 - INFO - 正在获取工单 6703 的详细信息...
2025-05-24 13:06:37,242 - INFO - 工单 6703 提取到 0 条评论
2025-05-24 13:06:37,243 - INFO - 工单 6703 没有附件
2025-05-24 13:06:37,243 - INFO - 工单 6703 详细信息采集完成
2025-05-24 13:06:39,252 - INFO - 正在处理第 16/443 个工单: 6702
2025-05-24 13:06:39,252 - INFO - 正在获取工单 6702 的详细信息...
2025-05-24 13:06:42,755 - INFO - 工单 6702 提取到 0 条评论
2025-05-24 13:06:42,756 - INFO - 工单 6702 没有附件
2025-05-24 13:06:42,756 - INFO - 工单 6702 详细信息采集完成
2025-05-24 13:06:44,760 - INFO - 正在处理第 17/443 个工单: 6392
2025-05-24 13:06:44,760 - INFO - 正在获取工单 6392 的详细信息...
2025-05-24 13:06:48,390 - INFO - 工单 6392 提取到 0 条评论
2025-05-24 13:06:48,392 - INFO - 工单 6392 没有附件
2025-05-24 13:06:48,394 - INFO - 工单 6392 详细信息采集完成
2025-05-24 13:06:50,404 - INFO - 正在处理第 18/443 个工单: 6223
2025-05-24 13:06:50,404 - INFO - 正在获取工单 6223 的详细信息...
2025-05-24 13:06:53,924 - INFO - 工单 6223 提取到 0 条评论
2025-05-24 13:06:53,925 - INFO - 工单 6223 没有附件
2025-05-24 13:06:53,926 - INFO - 工单 6223 详细信息采集完成
2025-05-24 13:06:55,932 - INFO - 正在处理第 19/443 个工单: 6143
2025-05-24 13:06:55,932 - INFO - 正在获取工单 6143 的详细信息...
2025-05-24 13:06:59,469 - INFO - 工单 6143 提取到 0 条评论
2025-05-24 13:06:59,473 - WARNING - 无法解析日期格式: wf w
2025-05-24 13:06:59,473 - INFO - 工单 6143 提取到 1 个附件
2025-05-24 13:06:59,473 - WARNING - 附件 DDRPHY问题记录 - 20250107_v2(WPS文件).xlsx 没有下载链接
2025-05-24 13:06:59,474 - INFO - 工单 6143 详细信息采集完成
2025-05-24 13:07:01,485 - INFO - 正在处理第 20/443 个工单: 6123
2025-05-24 13:07:01,485 - INFO - 正在获取工单 6123 的详细信息...
2025-05-24 13:07:05,055 - INFO - 工单 6123 提取到 0 条评论
2025-05-24 13:07:05,057 - INFO - 工单 6123 没有附件
2025-05-24 13:07:05,534 - INFO - 文件下载成功: data\attachments\6123\images\1736213169214239.png
2025-05-24 13:07:05,535 - INFO - 图片下载成功: 1736213169214239.png
2025-05-24 13:07:05,535 - INFO - 工单 6123 详细信息采集完成
2025-05-24 13:07:07,544 - INFO - 正在处理第 21/443 个工单: 6069
2025-05-24 13:07:07,544 - INFO - 正在获取工单 6069 的详细信息...
2025-05-24 13:07:11,081 - INFO - 工单 6069 提取到 0 条评论
2025-05-24 13:07:11,082 - WARNING - 无法解析日期格式: wf w
2025-05-24 13:07:11,083 - WARNING - 无法解析日期格式: wf w
2025-05-24 13:07:11,083 - WARNING - 无法解析日期格式: wf w
2025-05-24 13:07:11,084 - WARNING - 无法解析日期格式: wf w
2025-05-24 13:07:11,084 - INFO - 工单 6069 提取到 4 个附件
2025-05-24 13:07:11,084 - WARNING - 附件 DDRPHY示例代码走读疑问点check_20250306.txt 没有下载链接
2025-05-24 13:07:11,084 - WARNING - 附件 DDRPHY手册疑问记录_20250214.docx 没有下载链接
2025-05-24 13:07:11,085 - WARNING - 附件 DDRPHY手册疑问记录_20250206.docx 没有下载链接
2025-05-24 13:07:11,085 - WARNING - 附件 DDRPHY手册疑问记录_20241231(2)(1).docx 没有下载链接
2025-05-24 13:07:11,087 - INFO - 工单 6069 详细信息采集完成
2025-05-24 13:07:13,097 - INFO - 正在处理第 22/443 个工单: 6001
2025-05-24 13:07:13,097 - INFO - 正在获取工单 6001 的详细信息...
2025-05-24 13:07:16,651 - INFO - 工单 6001 提取到 0 条评论
2025-05-24 13:07:16,653 - INFO - 工单 6001 没有附件
2025-05-24 13:07:16,654 - INFO - 工单 6001 详细信息采集完成
2025-05-24 13:07:18,655 - INFO - 正在处理第 23/443 个工单: 5997
2025-05-24 13:07:18,655 - INFO - 正在获取工单 5997 的详细信息...
2025-05-24 13:07:21,889 - INFO - 工单 5997 提取到 0 条评论
2025-05-24 13:07:21,891 - INFO - 工单 5997 没有附件
2025-05-24 13:07:21,893 - INFO - 工单 5997 详细信息采集完成
2025-05-24 13:07:23,902 - INFO - 正在处理第 24/443 个工单: 5988
2025-05-24 13:07:23,902 - INFO - 正在获取工单 5988 的详细信息...
2025-05-24 13:07:27,869 - INFO - 工单 5988 提取到 0 条评论
2025-05-24 13:07:27,871 - INFO - 工单 5988 没有附件
2025-05-24 13:07:28,152 - INFO - 文件下载成功: data\attachments\5988\images\1735026051902565.png
2025-05-24 13:07:28,153 - INFO - 图片下载成功: 1735026051902565.png
2025-05-24 13:07:28,153 - INFO - 工单 5988 详细信息采集完成
2025-05-24 13:07:30,154 - INFO - 正在处理第 25/443 个工单: 5967
2025-05-24 13:07:30,154 - INFO - 正在获取工单 5967 的详细信息...
2025-05-24 13:07:33,666 - INFO - 工单 5967 提取到 0 条评论
2025-05-24 13:07:33,668 - INFO - 工单 5967 没有附件
2025-05-24 13:07:33,669 - INFO - 工单 5967 详细信息采集完成
2025-05-24 13:07:35,676 - INFO - 正在处理第 26/443 个工单: 5926
2025-05-24 13:07:35,676 - INFO - 正在获取工单 5926 的详细信息...
2025-05-24 13:07:39,217 - INFO - 工单 5926 提取到 0 条评论
2025-05-24 13:07:39,218 - INFO - 工单 5926 没有附件
2025-05-24 13:07:39,451 - INFO - 文件下载成功: data\attachments\5926\images\1734598431322213.jpg
2025-05-24 13:07:39,452 - INFO - 图片下载成功: 1734598431322213.jpg
2025-05-24 13:07:39,452 - INFO - 工单 5926 详细信息采集完成
2025-05-24 13:07:41,460 - INFO - 正在处理第 27/443 个工单: 5793
2025-05-24 13:07:41,460 - INFO - 正在获取工单 5793 的详细信息...
2025-05-24 13:07:44,997 - INFO - 工单 5793 提取到 0 条评论
2025-05-24 13:07:44,998 - INFO - 工单 5793 没有附件
2025-05-24 13:07:44,999 - INFO - 工单 5793 详细信息采集完成
2025-05-24 13:07:47,003 - INFO - 正在处理第 28/443 个工单: 5790
2025-05-24 13:07:47,003 - INFO - 正在获取工单 5790 的详细信息...
2025-05-24 13:07:50,514 - INFO - 工单 5790 提取到 0 条评论
2025-05-24 13:07:50,515 - INFO - 工单 5790 没有附件
2025-05-24 13:07:50,516 - INFO - 工单 5790 详细信息采集完成
2025-05-24 13:07:52,520 - INFO - 正在处理第 29/443 个工单: 5766
2025-05-24 13:07:52,520 - INFO - 正在获取工单 5766 的详细信息...
2025-05-24 13:07:53,456 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ConnectionResetError(10054, 'An existing connection was forcibly closed by the remote host', None, 10054, None)': /session/219c38d78264573c70f63479d9d0c8db
2025-05-24 13:07:57,547 - INFO - 用户中断程序
2025-05-24 13:17:15,976 - INFO - 已禁用代理
2025-05-24 13:17:15,980 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:17:15,983 - INFO - 已禁用代理
2025-05-24 13:17:15,984 - INFO - 工单 123 提取到 1 条评论
2025-05-24 13:19:11,622 - INFO - 已禁用代理
2025-05-24 13:19:11,622 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 13:19:11,626 - INFO - 已禁用代理
2025-05-24 13:19:11,627 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:19:11,628 - INFO - WebDriver初始化成功
2025-05-24 13:19:11,630 - INFO - 已禁用代理
2025-05-24 13:19:11,630 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 13:19:11,632 - INFO - 已禁用代理
2025-05-24 13:19:11,635 - INFO - 已禁用代理
2025-05-24 13:19:11,635 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpgcujbzmx\\last_update.json'
2025-05-24 13:19:11,635 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 13:19:11,636 - INFO - WebDriver初始化成功
2025-05-24 13:19:11,638 - INFO - 已禁用代理
2025-05-24 13:19:11,638 - INFO - 工单 123 提取到 1 条评论
2025-05-24 13:19:11,653 - INFO - 已禁用代理
2025-05-24 13:19:11,657 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:19:11,660 - INFO - 已禁用代理
2025-05-24 13:19:11,660 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 13:19:11,850 - INFO - 已禁用代理
2025-05-24 13:19:11,852 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpkam835qq\test.json
2025-05-24 13:19:11,856 - INFO - 已禁用代理
2025-05-24 13:19:11,856 - INFO - Session headers和cookies已更新
2025-05-24 13:20:55,772 - INFO - 已禁用代理
2025-05-24 13:20:55,779 - INFO - 工单 5766 提取到 0 条评论
2025-05-24 13:20:55,780 - INFO - 已禁用代理
2025-05-24 13:20:55,781 - INFO - 工单 123 提取到 0 条评论
2025-05-24 13:21:30,445 - INFO - 已禁用代理
2025-05-24 13:21:30,446 - DEBUG - 方法1: 找到 1 个akro-comment容器
2025-05-24 13:21:30,446 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:21:30,447 - DEBUG - 找到Reply Content区域
2025-05-24 13:21:30,447 - DEBUG - 找到 17 个相关div
2025-05-24 13:21:30,452 - INFO - 工单 5766 提取到 0 条评论
2025-05-24 13:21:30,453 - INFO - 已禁用代理
2025-05-24 13:21:30,454 - DEBUG - 方法1: 找到 0 个akro-comment容器
2025-05-24 13:21:30,454 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:21:30,454 - DEBUG - 找到Reply Content区域
2025-05-24 13:21:30,454 - DEBUG - 找到 6 个相关div
2025-05-24 13:21:30,455 - INFO - 工单 123 提取到 0 条评论
2025-05-24 13:22:08,016 - INFO - 已禁用代理
2025-05-24 13:22:08,018 - DEBUG - 方法1: 找到 1 个akro-comment容器
2025-05-24 13:22:08,018 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:22:08,018 - DEBUG - 找到Reply Content区域
2025-05-24 13:22:08,019 - DEBUG - 找到 17 个相关div
2025-05-24 13:22:08,019 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该c...
2025-05-24 13:22:08,019 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:22:08,019 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:22:08,020 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以r...
2025-05-24 13:22:08,020 - DEBUG - 找到内容div: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！2025-03-05 10:14:35...
2025-05-24 13:22:08,020 - DEBUG - 找到 2 个span元素
2025-05-24 13:22:08,020 - DEBUG - 检查span: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！
2025-05-24 13:22:08,020 - DEBUG - 检查span: 2025-03-05 10:14:35
2025-05-24 13:22:08,020 - DEBUG - 提取到时间: 2025-03-05 10:14:35
2025-05-24 13:22:08,021 - DEBUG - 提取到内容: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！...
2025-05-24 13:22:08,021 - DEBUG - 处理div: class=[], text_preview=Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以r...
2025-05-24 13:22:08,021 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不...
2025-05-24 13:22:08,022 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:22:08,022 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:22:08,024 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来...
2025-05-24 13:22:08,024 - DEBUG - 找到内容div: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...
2025-05-24 13:22:08,025 - DEBUG - 找到 2 个span元素
2025-05-24 13:22:08,025 - DEBUG - 检查span: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时无法保证每根线的delay完全一致，因此做出这样的修改后可能会导致读数据错误；b. 同样不建议使用一个rank的训练结果给两个rank使用，原理同上；3. 问题3：a. 贵司可以参考IO Retention流程中需要保存的寄存器值，如果该寄存器值被保存，则意味着热复位后需要将该值重新写入PHY中；4. 问题4：a. 从PHY工作的角度来看，只有DevInit是必须要做的初始化配置，其他训练步骤都是为了保证PHY和颗粒之前可以正常通信，因此不建议关闭WrDQ1D和MxRdlat训练，如果关闭该训练可能会导致写数据错误和不同Dbyte之间的读数据不对齐；谢谢！
2025-05-24 13:22:08,025 - DEBUG - 检查span: 2024-12-10 18:42:23
2025-05-24 13:22:08,025 - DEBUG - 提取到时间: 2024-12-10 18:42:23
2025-05-24 13:22:08,025 - DEBUG - 提取到内容: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...
2025-05-24 13:22:08,026 - DEBUG - 处理div: class=[], text_preview=Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来...
2025-05-24 13:22:08,026 - DEBUG - 处理div: class=[], text_preview=wf w:HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:22:08,026 - DEBUG - 处理div: class=[], text_preview=wf w:HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:22:08,026 - DEBUG - 处理div: class=['col-md-2'], text_preview=wf w:...
2025-05-24 13:22:08,026 - DEBUG - 找到来源div: wf w:
2025-05-24 13:22:08,027 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:22:08,027 - DEBUG - 找到内容div: HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:22:08,027 - DEBUG - 找到 2 个span元素
2025-05-24 13:22:08,027 - DEBUG - 检查span: HI,Aden:有任何进展，可以先同步给我们吗?
2025-05-24 13:22:08,027 - DEBUG - 检查span: 2024-12-10 16:16:43
2025-05-24 13:22:08,027 - DEBUG - 提取到时间: 2024-12-10 16:16:43
2025-05-24 13:22:08,027 - DEBUG - 提取到内容: HI,Aden:有任何进展，可以先同步给我们吗?...
2025-05-24 13:22:08,028 - DEBUG - 处理div: class=[], text_preview=HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:22:08,028 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi NetForward Colleagues，已收到贵司问题，我将...
2025-05-24 13:22:08,028 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:22:08,028 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:22:08,028 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-...
2025-05-24 13:22:08,028 - DEBUG - 找到内容div: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-12-05 20:39:03...
2025-05-24 13:22:08,029 - DEBUG - 找到 2 个span元素
2025-05-24 13:22:08,029 - DEBUG - 检查span: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！
2025-05-24 13:22:08,029 - DEBUG - 检查span: 2024-12-05 20:39:03
2025-05-24 13:22:08,030 - DEBUG - 提取到时间: 2024-12-05 20:39:03
2025-05-24 13:22:08,030 - DEBUG - 提取到内容: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！...
2025-05-24 13:22:08,030 - DEBUG - 处理div: class=[], text_preview=Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-...
2025-05-24 13:22:08,030 - INFO - 工单 5766 提取到 0 条评论
2025-05-24 13:22:08,032 - INFO - 已禁用代理
2025-05-24 13:22:08,032 - DEBUG - 方法1: 找到 0 个akro-comment容器
2025-05-24 13:22:08,033 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:22:08,033 - DEBUG - 找到Reply Content区域
2025-05-24 13:22:08,033 - DEBUG - 找到 6 个相关div
2025-05-24 13:22:08,033 - DEBUG - 处理div: class=[], text_preview=测试用户A这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:22:08,033 - DEBUG - 处理div: class=['col-md-2'], text_preview=测试用户A...
2025-05-24 13:22:08,033 - DEBUG - 找到来源div: 测试用户A
2025-05-24 13:22:08,034 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:22:08,034 - DEBUG - 找到内容div: 这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:22:08,034 - DEBUG - 找到 1 个span元素
2025-05-24 13:22:08,034 - DEBUG - 检查span: 2025-01-01 12:00:00
2025-05-24 13:22:08,034 - DEBUG - 提取到时间: 2025-01-01 12:00:00
2025-05-24 13:22:08,035 - DEBUG - 提取到内容: 这是第一条测试评论内容，包含一些详细的描述信息。...
2025-05-24 13:22:08,035 - DEBUG - 处理div: class=[], text_preview=测试用户B这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:22:08,035 - DEBUG - 处理div: class=['col-md-2'], text_preview=测试用户B...
2025-05-24 13:22:08,035 - DEBUG - 找到来源div: 测试用户B
2025-05-24 13:22:08,035 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:22:08,035 - DEBUG - 找到内容div: 这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:22:08,036 - DEBUG - 找到 1 个span元素
2025-05-24 13:22:08,036 - DEBUG - 检查span: 2025-01-01 15:30:00
2025-05-24 13:22:08,036 - DEBUG - 提取到时间: 2025-01-01 15:30:00
2025-05-24 13:22:08,036 - DEBUG - 提取到内容: 这是第二条测试评论，回复了前面的问题。...
2025-05-24 13:22:08,036 - INFO - 工单 123 提取到 0 条评论
2025-05-24 13:23:23,307 - INFO - 已禁用代理
2025-05-24 13:23:23,308 - DEBUG - 方法1: 找到 1 个akro-comment容器
2025-05-24 13:23:23,309 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:23:23,309 - DEBUG - 找到Reply Content区域
2025-05-24 13:23:23,309 - DEBUG - 找到 17 个相关div
2025-05-24 13:23:23,310 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该c...
2025-05-24 13:23:23,310 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:23:23,310 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:23:23,310 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以r...
2025-05-24 13:23:23,310 - DEBUG - 找到内容div: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！2025-03-05 10:14:35...
2025-05-24 13:23:23,311 - DEBUG - 找到 2 个span元素
2025-05-24 13:23:23,311 - DEBUG - 检查span: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！
2025-05-24 13:23:23,312 - DEBUG - 检查span: 2025-03-05 10:14:35
2025-05-24 13:23:23,312 - DEBUG - 提取到时间: 2025-03-05 10:14:35
2025-05-24 13:23:23,312 - DEBUG - 提取到内容: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！...
2025-05-24 13:23:23,312 - DEBUG - 处理div: class=[], text_preview=Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以r...
2025-05-24 13:23:23,312 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不...
2025-05-24 13:23:23,313 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:23:23,313 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:23:23,313 - DEBUG - 准备保存前一个评论: AkroStar Reply: - 2025-03-05 10:14:35
2025-05-24 13:23:23,313 - DEBUG - 检查是否跳过评论: timestamp=2025-03-05 10:14:35, ENABLE_INCREMENTAL_UPDATE=True, last_update_time=2025-05-24 13:01:31
2025-05-24 13:23:23,315 - DEBUG - 时间比较: comment_time=2025-03-05 10:14:35, last_update_time=2025-05-24 13:01:31, should_skip=True
2025-05-24 13:23:23,315 - DEBUG - 跳过评论（增量更新）: AkroStar Reply: - 2025-03-05 10:14:35
2025-05-24 13:23:23,315 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来...
2025-05-24 13:23:23,315 - DEBUG - 找到内容div: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...
2025-05-24 13:23:23,316 - DEBUG - 找到 2 个span元素
2025-05-24 13:23:23,317 - DEBUG - 检查span: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时无法保证每根线的delay完全一致，因此做出这样的修改后可能会导致读数据错误；b. 同样不建议使用一个rank的训练结果给两个rank使用，原理同上；3. 问题3：a. 贵司可以参考IO Retention流程中需要保存的寄存器值，如果该寄存器值被保存，则意味着热复位后需要将该值重新写入PHY中；4. 问题4：a. 从PHY工作的角度来看，只有DevInit是必须要做的初始化配置，其他训练步骤都是为了保证PHY和颗粒之前可以正常通信，因此不建议关闭WrDQ1D和MxRdlat训练，如果关闭该训练可能会导致写数据错误和不同Dbyte之间的读数据不对齐；谢谢！
2025-05-24 13:23:23,317 - DEBUG - 检查span: 2024-12-10 18:42:23
2025-05-24 13:23:23,317 - DEBUG - 提取到时间: 2024-12-10 18:42:23
2025-05-24 13:23:23,317 - DEBUG - 提取到内容: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...
2025-05-24 13:23:23,317 - DEBUG - 处理div: class=[], text_preview=Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来...
2025-05-24 13:23:23,318 - DEBUG - 处理div: class=[], text_preview=wf w:HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:23:23,318 - DEBUG - 处理div: class=[], text_preview=wf w:HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:23:23,318 - DEBUG - 处理div: class=['col-md-2'], text_preview=wf w:...
2025-05-24 13:23:23,318 - DEBUG - 找到来源div: wf w:
2025-05-24 13:23:23,318 - DEBUG - 准备保存前一个评论: AkroStar Reply: - 2024-12-10 18:42:23
2025-05-24 13:23:23,318 - DEBUG - 检查是否跳过评论: timestamp=2024-12-10 18:42:23, ENABLE_INCREMENTAL_UPDATE=True, last_update_time=2025-05-24 13:01:31
2025-05-24 13:23:23,319 - DEBUG - 时间比较: comment_time=2024-12-10 18:42:23, last_update_time=2025-05-24 13:01:31, should_skip=True
2025-05-24 13:23:23,319 - DEBUG - 跳过评论（增量更新）: AkroStar Reply: - 2024-12-10 18:42:23
2025-05-24 13:23:23,319 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:23:23,319 - DEBUG - 找到内容div: HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:23:23,320 - DEBUG - 找到 2 个span元素
2025-05-24 13:23:23,320 - DEBUG - 检查span: HI,Aden:有任何进展，可以先同步给我们吗?
2025-05-24 13:23:23,320 - DEBUG - 检查span: 2024-12-10 16:16:43
2025-05-24 13:23:23,320 - DEBUG - 提取到时间: 2024-12-10 16:16:43
2025-05-24 13:23:23,320 - DEBUG - 提取到内容: HI,Aden:有任何进展，可以先同步给我们吗?...
2025-05-24 13:23:23,320 - DEBUG - 处理div: class=[], text_preview=HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:23:23,320 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi NetForward Colleagues，已收到贵司问题，我将...
2025-05-24 13:23:23,321 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:23:23,321 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:23:23,321 - DEBUG - 准备保存前一个评论: wf w: - 2024-12-10 16:16:43
2025-05-24 13:23:23,321 - DEBUG - 检查是否跳过评论: timestamp=2024-12-10 16:16:43, ENABLE_INCREMENTAL_UPDATE=True, last_update_time=2025-05-24 13:01:31
2025-05-24 13:23:23,321 - DEBUG - 时间比较: comment_time=2024-12-10 16:16:43, last_update_time=2025-05-24 13:01:31, should_skip=True
2025-05-24 13:23:23,321 - DEBUG - 跳过评论（增量更新）: wf w: - 2024-12-10 16:16:43
2025-05-24 13:23:23,321 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-...
2025-05-24 13:23:23,322 - DEBUG - 找到内容div: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-12-05 20:39:03...
2025-05-24 13:23:23,322 - DEBUG - 找到 2 个span元素
2025-05-24 13:23:23,322 - DEBUG - 检查span: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！
2025-05-24 13:23:23,323 - DEBUG - 检查span: 2024-12-05 20:39:03
2025-05-24 13:23:23,323 - DEBUG - 提取到时间: 2024-12-05 20:39:03
2025-05-24 13:23:23,323 - DEBUG - 提取到内容: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！...
2025-05-24 13:23:23,323 - DEBUG - 处理div: class=[], text_preview=Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-...
2025-05-24 13:23:23,323 - DEBUG - 准备保存最后一个评论: AkroStar Reply: - 2024-12-05 20:39:03
2025-05-24 13:23:23,323 - DEBUG - 检查是否跳过评论: timestamp=2024-12-05 20:39:03, ENABLE_INCREMENTAL_UPDATE=True, last_update_time=2025-05-24 13:01:31
2025-05-24 13:23:23,323 - DEBUG - 时间比较: comment_time=2024-12-05 20:39:03, last_update_time=2025-05-24 13:01:31, should_skip=True
2025-05-24 13:23:23,324 - DEBUG - 跳过评论（增量更新）: AkroStar Reply: - 2024-12-05 20:39:03
2025-05-24 13:23:23,324 - INFO - 工单 5766 提取到 0 条评论
2025-05-24 13:23:23,325 - INFO - 已禁用代理
2025-05-24 13:23:23,325 - DEBUG - 方法1: 找到 0 个akro-comment容器
2025-05-24 13:23:23,325 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:23:23,325 - DEBUG - 找到Reply Content区域
2025-05-24 13:23:23,326 - DEBUG - 找到 6 个相关div
2025-05-24 13:23:23,326 - DEBUG - 处理div: class=[], text_preview=测试用户A这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:23:23,326 - DEBUG - 处理div: class=['col-md-2'], text_preview=测试用户A...
2025-05-24 13:23:23,326 - DEBUG - 找到来源div: 测试用户A
2025-05-24 13:23:23,326 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:23:23,326 - DEBUG - 找到内容div: 这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:23:23,327 - DEBUG - 找到 1 个span元素
2025-05-24 13:23:23,327 - DEBUG - 检查span: 2025-01-01 12:00:00
2025-05-24 13:23:23,327 - DEBUG - 提取到时间: 2025-01-01 12:00:00
2025-05-24 13:23:23,327 - DEBUG - 提取到内容: 这是第一条测试评论内容，包含一些详细的描述信息。...
2025-05-24 13:23:23,327 - DEBUG - 处理div: class=[], text_preview=测试用户B这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:23:23,327 - DEBUG - 处理div: class=['col-md-2'], text_preview=测试用户B...
2025-05-24 13:23:23,328 - DEBUG - 找到来源div: 测试用户B
2025-05-24 13:23:23,328 - DEBUG - 准备保存前一个评论: 测试用户A - 2025-01-01 12:00:00
2025-05-24 13:23:23,328 - DEBUG - 检查是否跳过评论: timestamp=2025-01-01 12:00:00, ENABLE_INCREMENTAL_UPDATE=True, last_update_time=2025-05-24 13:01:31
2025-05-24 13:23:23,328 - DEBUG - 时间比较: comment_time=2025-01-01 12:00:00, last_update_time=2025-05-24 13:01:31, should_skip=True
2025-05-24 13:23:23,328 - DEBUG - 跳过评论（增量更新）: 测试用户A - 2025-01-01 12:00:00
2025-05-24 13:23:23,328 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:23:23,329 - DEBUG - 找到内容div: 这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:23:23,329 - DEBUG - 找到 1 个span元素
2025-05-24 13:23:23,329 - DEBUG - 检查span: 2025-01-01 15:30:00
2025-05-24 13:23:23,329 - DEBUG - 提取到时间: 2025-01-01 15:30:00
2025-05-24 13:23:23,329 - DEBUG - 提取到内容: 这是第二条测试评论，回复了前面的问题。...
2025-05-24 13:23:23,330 - DEBUG - 准备保存最后一个评论: 测试用户B - 2025-01-01 15:30:00
2025-05-24 13:23:23,330 - DEBUG - 检查是否跳过评论: timestamp=2025-01-01 15:30:00, ENABLE_INCREMENTAL_UPDATE=True, last_update_time=2025-05-24 13:01:31
2025-05-24 13:23:23,330 - DEBUG - 时间比较: comment_time=2025-01-01 15:30:00, last_update_time=2025-05-24 13:01:31, should_skip=True
2025-05-24 13:23:23,330 - DEBUG - 跳过评论（增量更新）: 测试用户B - 2025-01-01 15:30:00
2025-05-24 13:23:23,330 - INFO - 工单 123 提取到 0 条评论
2025-05-24 13:24:11,974 - INFO - 已禁用代理
2025-05-24 13:24:11,976 - DEBUG - 方法1: 找到 1 个akro-comment容器
2025-05-24 13:24:11,976 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:24:11,977 - DEBUG - 找到Reply Content区域
2025-05-24 13:24:11,977 - DEBUG - 找到 17 个相关div
2025-05-24 13:24:11,977 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该c...
2025-05-24 13:24:11,977 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:24:11,978 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:24:11,978 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以r...
2025-05-24 13:24:11,978 - DEBUG - 找到内容div: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！2025-03-05 10:14:35...
2025-05-24 13:24:11,979 - DEBUG - 找到 2 个span元素
2025-05-24 13:24:11,979 - DEBUG - 检查span: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！
2025-05-24 13:24:11,979 - DEBUG - 检查span: 2025-03-05 10:14:35
2025-05-24 13:24:11,979 - DEBUG - 提取到时间: 2025-03-05 10:14:35
2025-05-24 13:24:11,979 - DEBUG - 提取到内容: Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！...
2025-05-24 13:24:11,979 - DEBUG - 处理div: class=[], text_preview=Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以r...
2025-05-24 13:24:11,980 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不...
2025-05-24 13:24:11,980 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:24:11,980 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:24:11,980 - DEBUG - 准备保存前一个评论: AkroStar Reply: - 2025-03-05 10:14:35
2025-05-24 13:24:11,980 - DEBUG - 添加评论: AkroStar Reply: - 2025-03-05 10:14:35
2025-05-24 13:24:11,980 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来...
2025-05-24 13:24:11,980 - DEBUG - 找到内容div: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...
2025-05-24 13:24:11,981 - DEBUG - 找到 2 个span元素
2025-05-24 13:24:11,981 - DEBUG - 检查span: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时无法保证每根线的delay完全一致，因此做出这样的修改后可能会导致读数据错误；b. 同样不建议使用一个rank的训练结果给两个rank使用，原理同上；3. 问题3：a. 贵司可以参考IO Retention流程中需要保存的寄存器值，如果该寄存器值被保存，则意味着热复位后需要将该值重新写入PHY中；4. 问题4：a. 从PHY工作的角度来看，只有DevInit是必须要做的初始化配置，其他训练步骤都是为了保证PHY和颗粒之前可以正常通信，因此不建议关闭WrDQ1D和MxRdlat训练，如果关闭该训练可能会导致写数据错误和不同Dbyte之间的读数据不对齐；谢谢！
2025-05-24 13:24:11,982 - DEBUG - 检查span: 2024-12-10 18:42:23
2025-05-24 13:24:11,982 - DEBUG - 提取到时间: 2024-12-10 18:42:23
2025-05-24 13:24:11,982 - DEBUG - 提取到内容: Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时...
2025-05-24 13:24:11,982 - DEBUG - 处理div: class=[], text_preview=Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来...
2025-05-24 13:24:11,982 - DEBUG - 处理div: class=[], text_preview=wf w:HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:24:11,982 - DEBUG - 处理div: class=[], text_preview=wf w:HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:24:11,983 - DEBUG - 处理div: class=['col-md-2'], text_preview=wf w:...
2025-05-24 13:24:11,983 - DEBUG - 找到来源div: wf w:
2025-05-24 13:24:11,983 - DEBUG - 准备保存前一个评论: AkroStar Reply: - 2024-12-10 18:42:23
2025-05-24 13:24:11,983 - DEBUG - 添加评论: AkroStar Reply: - 2024-12-10 18:42:23
2025-05-24 13:24:11,983 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:24:11,983 - DEBUG - 找到内容div: HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:24:11,984 - DEBUG - 找到 2 个span元素
2025-05-24 13:24:11,984 - DEBUG - 检查span: HI,Aden:有任何进展，可以先同步给我们吗?
2025-05-24 13:24:11,984 - DEBUG - 检查span: 2024-12-10 16:16:43
2025-05-24 13:24:11,984 - DEBUG - 提取到时间: 2024-12-10 16:16:43
2025-05-24 13:24:11,984 - DEBUG - 提取到内容: HI,Aden:有任何进展，可以先同步给我们吗?...
2025-05-24 13:24:11,984 - DEBUG - 处理div: class=[], text_preview=HI,Aden:有任何进展，可以先同步给我们吗?2024-12-10 16:16:43...
2025-05-24 13:24:11,985 - DEBUG - 处理div: class=[], text_preview=AkroStar Reply:Hi NetForward Colleagues，已收到贵司问题，我将...
2025-05-24 13:24:11,985 - DEBUG - 处理div: class=['col-md-2'], text_preview=AkroStar Reply:...
2025-05-24 13:24:11,985 - DEBUG - 找到来源div: AkroStar Reply:
2025-05-24 13:24:11,985 - DEBUG - 准备保存前一个评论: wf w: - 2024-12-10 16:16:43
2025-05-24 13:24:11,985 - DEBUG - 添加评论: wf w: - 2024-12-10 16:16:43
2025-05-24 13:24:11,986 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-...
2025-05-24 13:24:11,986 - DEBUG - 找到内容div: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-12-05 20:39:03...
2025-05-24 13:24:11,986 - DEBUG - 找到 2 个span元素
2025-05-24 13:24:11,987 - DEBUG - 检查span: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！
2025-05-24 13:24:11,987 - DEBUG - 检查span: 2024-12-05 20:39:03
2025-05-24 13:24:11,987 - DEBUG - 提取到时间: 2024-12-05 20:39:03
2025-05-24 13:24:11,987 - DEBUG - 提取到内容: Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！...
2025-05-24 13:24:11,987 - DEBUG - 处理div: class=[], text_preview=Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！2024-...
2025-05-24 13:24:11,987 - DEBUG - 准备保存最后一个评论: AkroStar Reply: - 2024-12-05 20:39:03
2025-05-24 13:24:11,987 - DEBUG - 添加最后一个评论: AkroStar Reply: - 2024-12-05 20:39:03
2025-05-24 13:24:11,988 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:24:11,990 - INFO - 已禁用代理
2025-05-24 13:24:11,991 - DEBUG - 方法1: 找到 0 个akro-comment容器
2025-05-24 13:24:11,991 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:24:11,991 - DEBUG - 找到Reply Content区域
2025-05-24 13:24:11,991 - DEBUG - 找到 6 个相关div
2025-05-24 13:24:11,991 - DEBUG - 处理div: class=[], text_preview=测试用户A这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:24:11,991 - DEBUG - 处理div: class=['col-md-2'], text_preview=测试用户A...
2025-05-24 13:24:11,992 - DEBUG - 找到来源div: 测试用户A
2025-05-24 13:24:11,992 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:24:11,992 - DEBUG - 找到内容div: 这是第一条测试评论内容，包含一些详细的描述信息。2025-01-01 12:00:00...
2025-05-24 13:24:11,992 - DEBUG - 找到 1 个span元素
2025-05-24 13:24:11,992 - DEBUG - 检查span: 2025-01-01 12:00:00
2025-05-24 13:24:11,992 - DEBUG - 提取到时间: 2025-01-01 12:00:00
2025-05-24 13:24:11,993 - DEBUG - 提取到内容: 这是第一条测试评论内容，包含一些详细的描述信息。...
2025-05-24 13:24:11,993 - DEBUG - 处理div: class=[], text_preview=测试用户B这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:24:11,993 - DEBUG - 处理div: class=['col-md-2'], text_preview=测试用户B...
2025-05-24 13:24:11,993 - DEBUG - 找到来源div: 测试用户B
2025-05-24 13:24:11,993 - DEBUG - 准备保存前一个评论: 测试用户A - 2025-01-01 12:00:00
2025-05-24 13:24:11,993 - DEBUG - 添加评论: 测试用户A - 2025-01-01 12:00:00
2025-05-24 13:24:11,993 - DEBUG - 处理div: class=['col-md-10', 'well'], text_preview=这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:24:11,993 - DEBUG - 找到内容div: 这是第二条测试评论，回复了前面的问题。2025-01-01 15:30:00...
2025-05-24 13:24:11,994 - DEBUG - 找到 1 个span元素
2025-05-24 13:24:11,994 - DEBUG - 检查span: 2025-01-01 15:30:00
2025-05-24 13:24:11,994 - DEBUG - 提取到时间: 2025-01-01 15:30:00
2025-05-24 13:24:11,994 - DEBUG - 提取到内容: 这是第二条测试评论，回复了前面的问题。...
2025-05-24 13:24:11,994 - DEBUG - 准备保存最后一个评论: 测试用户B - 2025-01-01 15:30:00
2025-05-24 13:24:11,994 - DEBUG - 添加最后一个评论: 测试用户B - 2025-01-01 15:30:00
2025-05-24 13:24:11,995 - INFO - 工单 123 提取到 2 条评论
2025-05-24 13:24:24,305 - INFO - 已禁用代理
2025-05-24 13:24:24,306 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 13:24:24,310 - INFO - 已禁用代理
2025-05-24 13:24:24,311 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:24:24,311 - INFO - WebDriver初始化成功
2025-05-24 13:24:24,313 - INFO - 已禁用代理
2025-05-24 13:24:24,314 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 13:24:24,315 - INFO - 已禁用代理
2025-05-24 13:24:24,317 - INFO - 已禁用代理
2025-05-24 13:24:24,317 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpymzanjvf\\last_update.json'
2025-05-24 13:24:24,318 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 13:24:24,319 - INFO - WebDriver初始化成功
2025-05-24 13:24:24,321 - INFO - 已禁用代理
2025-05-24 13:24:24,321 - INFO - 工单 123 提取到 1 条评论
2025-05-24 13:24:24,334 - INFO - 已禁用代理
2025-05-24 13:24:24,338 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:24:24,340 - INFO - 已禁用代理
2025-05-24 13:24:24,340 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 13:24:24,573 - INFO - 已禁用代理
2025-05-24 13:24:24,575 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpoa7xhcur\test.json
2025-05-24 13:24:24,578 - INFO - 已禁用代理
2025-05-24 13:24:24,579 - INFO - Session headers和cookies已更新
2025-05-24 13:24:59,748 - INFO - 已禁用代理
2025-05-24 13:24:59,749 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 13:24:59,752 - INFO - 已禁用代理
2025-05-24 13:24:59,753 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:24:59,753 - INFO - WebDriver初始化成功
2025-05-24 13:24:59,755 - INFO - 已禁用代理
2025-05-24 13:24:59,755 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 13:24:59,757 - INFO - 已禁用代理
2025-05-24 13:24:59,759 - INFO - 已禁用代理
2025-05-24 13:24:59,759 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp39st1nkb\\last_update.json'
2025-05-24 13:24:59,759 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 13:24:59,760 - INFO - WebDriver初始化成功
2025-05-24 13:24:59,762 - INFO - 已禁用代理
2025-05-24 13:24:59,762 - INFO - 工单 123 提取到 1 条评论
2025-05-24 13:24:59,776 - INFO - 已禁用代理
2025-05-24 13:24:59,781 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:24:59,783 - INFO - 已禁用代理
2025-05-24 13:24:59,784 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 13:24:59,937 - INFO - 已禁用代理
2025-05-24 13:24:59,939 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp96hsmucj\test.json
2025-05-24 13:24:59,943 - INFO - 已禁用代理
2025-05-24 13:24:59,943 - INFO - Session headers和cookies已更新
2025-05-24 13:25:47,333 - INFO - 已禁用代理
2025-05-24 13:25:47,335 - DEBUG - 方法1: 找到 1 个akro-comment容器
2025-05-24 13:25:47,335 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:25:47,336 - DEBUG - 找到Reply Content区域
2025-05-24 13:25:47,336 - DEBUG - 找到 17 个相关div
2025-05-24 13:25:47,336 - DEBUG - 添加评论: AkroStar Reply: - 2025-03-05 10:14:35
2025-05-24 13:25:47,337 - DEBUG - 添加评论: AkroStar Reply: - 2024-12-10 18:42:23
2025-05-24 13:25:47,338 - DEBUG - 添加评论: wf w: - 2024-12-10 16:16:43
2025-05-24 13:25:47,339 - DEBUG - 添加最后一个评论: AkroStar Reply: - 2024-12-05 20:39:03
2025-05-24 13:25:47,339 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:25:47,341 - INFO - 已禁用代理
2025-05-24 13:25:47,342 - DEBUG - 方法1: 找到 0 个akro-comment容器
2025-05-24 13:25:47,342 - DEBUG - 方法1未提取到评论，尝试方法2
2025-05-24 13:25:47,342 - DEBUG - 找到Reply Content区域
2025-05-24 13:25:47,342 - DEBUG - 找到 6 个相关div
2025-05-24 13:25:47,343 - DEBUG - 添加评论: 测试用户A - 2025-01-01 12:00:00
2025-05-24 13:25:47,343 - DEBUG - 添加最后一个评论: 测试用户B - 2025-01-01 15:30:00
2025-05-24 13:25:47,343 - INFO - 工单 123 提取到 2 条评论
2025-05-24 13:30:24,505 - INFO - 已禁用代理
2025-05-24 13:30:24,510 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:30:24,522 - INFO - 已禁用代理
2025-05-24 13:30:24,523 - INFO - 工单 5766 没有附件
2025-05-24 13:30:24,525 - INFO - 数据已保存到: test_data\demo_case_detail.json
2025-05-24 13:30:24,525 - INFO - 数据已保存到: test_data\demo_comments.json
2025-05-24 13:30:24,528 - INFO - 数据已保存到: test_data\demo_attachments.json
2025-05-24 13:30:24,528 - INFO - 数据已保存到: test_data\demo_case_summary.json
2025-05-24 13:31:33,536 - INFO - 已禁用代理
2025-05-24 13:31:33,536 - INFO - 工单 7514 提取到 1 个附件
2025-05-24 13:31:33,549 - INFO - 已禁用代理
2025-05-24 13:31:33,550 - INFO - 工单 5766 没有附件
2025-05-24 13:31:33,555 - INFO - 已禁用代理
2025-05-24 13:31:33,555 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 13:31:33,557 - INFO - WebDriver初始化成功
2025-05-24 13:31:33,559 - INFO - 已禁用代理
2025-05-24 13:31:33,559 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 13:31:33,571 - INFO - 已禁用代理
2025-05-24 13:31:33,575 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:31:33,578 - INFO - 已禁用代理
2025-05-24 13:31:33,580 - INFO - 已禁用代理
2025-05-24 13:31:33,580 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmpkqizjysn\\last_update.json'
2025-05-24 13:31:33,581 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-24 13:31:33,581 - INFO - WebDriver初始化成功
2025-05-24 13:31:33,583 - INFO - 已禁用代理
2025-05-24 13:31:33,583 - INFO - 工单 123 提取到 1 条评论
2025-05-24 13:31:33,595 - INFO - 已禁用代理
2025-05-24 13:31:33,599 - INFO - 工单 5766 提取到 4 条评论
2025-05-24 13:31:33,601 - INFO - 已禁用代理
2025-05-24 13:31:33,601 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-24 13:31:33,798 - INFO - 已禁用代理
2025-05-24 13:31:33,799 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpeclo3bjs\test.json
2025-05-24 13:31:33,803 - INFO - 已禁用代理
2025-05-24 13:31:33,803 - INFO - Session headers和cookies已更新
2025-05-24 14:23:48,790 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgl5nzasf\temp_20250524_142348\cases_list_page1_status5_20250524_142348.json
2025-05-24 14:23:48,793 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgl5nzasf\temp_20250524_142348\case_detail_123_20250524_142348.json
2025-05-24 14:23:48,794 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgl5nzasf\temp_20250524_142348\api_request_142348_794156.json
2025-05-24 14:23:48,830 - INFO - 原始数据已打包保存: C:\Users\<USER>\AppData\Local\Temp\tmpgl5nzasf\raw_data_20250524_142348.zip
2025-05-24 14:23:48,836 - INFO - 已禁用代理
2025-05-24 14:23:48,837 - INFO - 已禁用代理
2025-05-24 14:32:02,287 - INFO - 设置采集最新 10 条工单
2025-05-24 14:32:02,287 - INFO - 设置采集Open和Closed两种状态的工单
2025-05-24 14:32:02,287 - INFO - ============================================================
2025-05-24 14:32:02,287 - INFO - AkroCare工单采集程序启动
2025-05-24 14:32:02,287 - INFO - 登录邮箱: <EMAIL>
2025-05-24 14:32:02,287 - INFO - ChromeDriver: 自定义路径
2025-05-24 14:32:02,288 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:32:02,288 - INFO - API请求方式: requests
2025-05-24 14:32:02,288 - INFO - 代理设置: 禁用
2025-05-24 14:32:02,288 - INFO - 增量更新: 禁用
2025-05-24 14:32:02,288 - INFO - 采集模式: 最新 10 条工单
2025-05-24 14:32:02,288 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-24 14:32:02,288 - INFO - 原始数据: 禁用
2025-05-24 14:32:02,289 - INFO - 开始时间: 2025-05-24 14:32:02
2025-05-24 14:32:02,289 - INFO - ============================================================
2025-05-24 14:32:02,289 - INFO - 已禁用代理
2025-05-24 14:32:02,290 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:32:03,476 - INFO - WebDriver初始化成功
2025-05-24 14:32:03,477 - INFO - 开始登录...
2025-05-24 14:32:17,115 - INFO - 登录成功
2025-05-24 14:32:17,128 - INFO - Session headers和cookies已更新
2025-05-24 14:32:17,128 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 14:32:17,128 - INFO - 导航到工单页面...
2025-05-24 14:32:20,267 - INFO - Session headers和cookies已更新
2025-05-24 14:32:20,267 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 14:32:20,268 - INFO - 开始采集工单数据...
2025-05-24 14:32:20,268 - INFO - 将采集两种状态的工单: Open(4), Closed(5)
2025-05-24 14:32:20,268 - INFO - 开始采集状态为 4 的工单...
2025-05-24 14:32:20,268 - INFO - 正在获取状态 4 第 1 页工单数据...
2025-05-24 14:32:20,561 - INFO - 状态 4 第 1 页获取到 5 条有效工单
2025-05-24 14:32:20,562 - INFO - 状态 4 采集完成，共 0 条工单
2025-05-24 14:32:20,562 - INFO - 开始采集状态为 5 的工单...
2025-05-24 14:32:20,562 - INFO - 正在获取状态 5 第 1 页工单数据...
2025-05-24 14:32:20,914 - INFO - 达到采集限制，停止采集
2025-05-24 14:32:20,914 - INFO - 正在处理第 1/10 个工单: 7514
2025-05-24 14:32:20,914 - INFO - 正在获取工单 7514 的详细信息...
2025-05-24 14:32:20,915 - INFO - 项目名称: N0013
2025-05-24 14:32:20,915 - INFO - 工单标题: MP32 指标说明
2025-05-24 14:32:24,659 - INFO - 工单 7514 提取到 2 条评论
2025-05-24 14:32:24,660 - INFO - 工单 7514 没有附件
2025-05-24 14:32:24,661 - INFO - 工单 7514 详细信息采集完成
2025-05-24 14:32:26,675 - INFO - 正在处理第 2/10 个工单: 6867
2025-05-24 14:32:26,675 - INFO - 正在获取工单 6867 的详细信息...
2025-05-24 14:32:26,675 - INFO - 项目名称: N0013
2025-05-24 14:32:26,675 - INFO - 工单标题: serdes PI 仿真问题交流
2025-05-24 14:32:30,099 - INFO - 工单 6867 提取到 7 条评论
2025-05-24 14:32:30,102 - INFO - 工单 6867 没有附件
2025-05-24 14:32:30,104 - INFO - 工单 6867 没有附件或图片，跳过目录创建
2025-05-24 14:32:30,104 - INFO - 工单 6867 详细信息采集完成
2025-05-24 14:32:32,107 - INFO - 正在处理第 3/10 个工单: 5829
2025-05-24 14:32:32,107 - INFO - 正在获取工单 5829 的详细信息...
2025-05-24 14:32:32,107 - INFO - 项目名称: N0013
2025-05-24 14:32:32,107 - INFO - 工单标题: MP32光口能力评估
2025-05-24 14:32:35,621 - INFO - 工单 5829 提取到 9 条评论
2025-05-24 14:32:35,623 - INFO - 工单 5829 没有附件
2025-05-24 14:32:35,623 - INFO - 工单 5829 没有附件或图片，跳过目录创建
2025-05-24 14:32:35,624 - INFO - 工单 5829 详细信息采集完成
2025-05-24 14:32:37,639 - INFO - 正在处理第 4/10 个工单: 4776
2025-05-24 14:32:37,639 - INFO - 正在获取工单 4776 的详细信息...
2025-05-24 14:32:37,639 - INFO - 项目名称: N0013
2025-05-24 14:32:37,639 - INFO - 工单标题: ddr 固件 安全性疑问
2025-05-24 14:32:41,150 - INFO - 工单 4776 提取到 6 条评论
2025-05-24 14:32:41,151 - INFO - 工单 4776 没有附件
2025-05-24 14:32:41,152 - INFO - 工单 4776 没有附件或图片，跳过目录创建
2025-05-24 14:32:41,153 - INFO - 工单 4776 详细信息采集完成
2025-05-24 14:32:43,165 - INFO - 正在处理第 5/10 个工单: 4283
2025-05-24 14:32:43,165 - INFO - 正在获取工单 4283 的详细信息...
2025-05-24 14:32:43,165 - INFO - 项目名称: N0013
2025-05-24 14:32:43,165 - INFO - 工单标题: MP20 16Gbps CPM模型
2025-05-24 14:32:47,016 - INFO - 工单 4283 提取到 31 条评论
2025-05-24 14:32:47,019 - INFO - 工单 4283 提取到 0 个附件
2025-05-24 14:32:47,021 - INFO - 工单 4283 详细信息采集完成
2025-05-24 14:32:49,021 - INFO - 正在处理第 6/10 个工单: 7410
2025-05-24 14:32:49,021 - INFO - 正在获取工单 7410 的详细信息...
2025-05-24 14:32:49,021 - INFO - 项目名称: N0013
2025-05-24 14:32:49,021 - INFO - 工单标题: 想确认下提供ddr_phy的固件是不是最终版再也不会更新了
2025-05-24 14:32:52,208 - INFO - 工单 7410 提取到 3 条评论
2025-05-24 14:32:52,209 - INFO - 工单 7410 没有附件
2025-05-24 14:32:52,210 - INFO - 工单 7410 没有附件或图片，跳过目录创建
2025-05-24 14:32:52,210 - INFO - 工单 7410 详细信息采集完成
2025-05-24 14:32:54,211 - INFO - 正在处理第 7/10 个工单: 7263
2025-05-24 14:32:54,211 - INFO - 正在获取工单 7263 的详细信息...
2025-05-24 14:32:54,212 - INFO - 项目名称: N0013
2025-05-24 14:32:54,212 - INFO - 工单标题: DDRPHY half DQ板级设计
2025-05-24 14:32:57,473 - INFO - 工单 7263 提取到 8 条评论
2025-05-24 14:32:57,474 - INFO - 工单 7263 没有附件
2025-05-24 14:32:57,475 - INFO - 工单 7263 没有附件或图片，跳过目录创建
2025-05-24 14:32:57,475 - INFO - 工单 7263 详细信息采集完成
2025-05-24 14:32:59,485 - INFO - 正在处理第 8/10 个工单: 7246
2025-05-24 14:32:59,485 - INFO - 正在获取工单 7246 的详细信息...
2025-05-24 14:32:59,485 - INFO - 项目名称: N0013
2025-05-24 14:32:59,485 - INFO - 工单标题: Aks_mp20_pipe_interface.xlsx中，描述信号pipe_ln{0,1,2,3}_perst/pipe_ln{0,1,2,3}_rst/pipe_phy_rst为低有效，而从rtl代码和eda验证来看，应是高复位，请确认正确的复位行为
2025-05-24 14:33:02,684 - INFO - 工单 7246 提取到 3 条评论
2025-05-24 14:33:02,685 - INFO - 工单 7246 没有附件
2025-05-24 14:33:02,685 - INFO - 工单 7246 没有附件或图片，跳过目录创建
2025-05-24 14:33:02,686 - INFO - 工单 7246 详细信息采集完成
2025-05-24 14:33:04,693 - INFO - 正在处理第 9/10 个工单: 7051
2025-05-24 14:33:04,693 - INFO - 正在获取工单 7051 的详细信息...
2025-05-24 14:33:04,693 - INFO - 项目名称: N0013
2025-05-24 14:33:04,693 - INFO - 工单标题: serdes  refclk clkbuffer问题
2025-05-24 14:33:07,885 - INFO - 工单 7051 提取到 2 条评论
2025-05-24 14:33:07,886 - INFO - 工单 7051 没有附件
2025-05-24 14:33:07,887 - INFO - 工单 7051 没有附件或图片，跳过目录创建
2025-05-24 14:33:07,887 - INFO - 工单 7051 详细信息采集完成
2025-05-24 14:33:09,900 - INFO - 正在处理第 10/10 个工单: 6921
2025-05-24 14:33:09,900 - INFO - 正在获取工单 6921 的详细信息...
2025-05-24 14:33:09,900 - INFO - 项目名称: N0013
2025-05-24 14:33:09,900 - INFO - 工单标题: 是否有ddr_phy的测试板提供？
2025-05-24 14:33:13,141 - INFO - 工单 6921 提取到 3 条评论
2025-05-24 14:33:13,143 - INFO - 工单 6921 没有附件
2025-05-24 14:33:13,144 - INFO - 工单 6921 没有附件或图片，跳过目录创建
2025-05-24 14:33:13,144 - INFO - 工单 6921 详细信息采集完成
2025-05-24 14:33:15,150 - INFO - 数据已保存到: data\last_update.json
2025-05-24 14:33:15,152 - INFO - 数据已保存到: data\cases_data.json
2025-05-24 14:33:15,152 - INFO - 已保存 10 条工单数据
2025-05-24 14:33:15,152 - INFO - ============================================================
2025-05-24 14:33:15,152 - INFO - 采集完成！共采集 10 条工单
2025-05-24 14:33:15,152 - INFO - 数据保存位置: data\cases_data.json
2025-05-24 14:33:15,153 - INFO - 附件保存位置: data\attachments
2025-05-24 14:33:15,153 - INFO - 结束时间: 2025-05-24 14:33:15
2025-05-24 14:33:15,153 - INFO - ============================================================
2025-05-24 14:33:17,260 - INFO - 资源已释放
2025-05-24 14:35:42,022 - INFO - 设置指定工单ID: [5766, 7514]
2025-05-24 14:35:42,022 - INFO - 启用原始数据保存
2025-05-24 14:35:42,023 - INFO - 启用原始数据打包模式
2025-05-24 14:35:42,023 - INFO - ============================================================
2025-05-24 14:35:42,023 - INFO - AkroCare工单采集程序启动
2025-05-24 14:35:42,023 - INFO - 登录邮箱: <EMAIL>
2025-05-24 14:35:42,023 - INFO - ChromeDriver: 自定义路径
2025-05-24 14:35:42,023 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:35:42,023 - INFO - API请求方式: requests
2025-05-24 14:35:42,024 - INFO - 代理设置: 禁用
2025-05-24 14:35:42,024 - INFO - 增量更新: 启用
2025-05-24 14:35:42,024 - INFO - 采集模式: 指定工单ID - [5766, 7514]
2025-05-24 14:35:42,024 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-24 14:35:42,024 - INFO - 原始数据: 启用 - 打包模式
2025-05-24 14:35:42,024 - INFO - 开始时间: 2025-05-24 14:35:42
2025-05-24 14:35:42,024 - INFO - ============================================================
2025-05-24 14:35:42,025 - INFO - 已禁用代理
2025-05-24 14:35:42,031 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:35:43,212 - INFO - WebDriver初始化成功
2025-05-24 14:35:43,212 - INFO - 开始登录...
2025-05-24 14:35:56,905 - INFO - 登录成功
2025-05-24 14:35:56,918 - INFO - Session headers和cookies已更新
2025-05-24 14:35:56,918 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 14:35:56,918 - INFO - 导航到工单页面...
2025-05-24 14:36:00,041 - INFO - Session headers和cookies已更新
2025-05-24 14:36:00,041 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 14:36:00,042 - INFO - 开始采集工单数据...
2025-05-24 14:36:00,042 - INFO - 采集指定工单ID: [5766, 7514]
2025-05-24 14:36:00,042 - INFO - 获取指定工单 5766 的信息...
2025-05-24 14:36:00,042 - WARNING - 指定工单ID 5766 的获取功能待实现
2025-05-24 14:36:00,042 - INFO - 获取指定工单 7514 的信息...
2025-05-24 14:36:00,042 - WARNING - 指定工单ID 7514 的获取功能待实现
2025-05-24 14:36:00,042 - WARNING - 没有获取到工单概要信息
2025-05-24 14:36:00,042 - WARNING - 没有采集到工单数据
2025-05-24 14:36:02,162 - INFO - 资源已释放
2025-05-24 14:46:07,961 - INFO - 已禁用代理
2025-05-24 14:46:07,962 - INFO - 指定工单ID模式，跳过概要信息获取
2025-05-24 14:47:31,432 - INFO - 设置指定工单ID: [5766, 7514]
2025-05-24 14:47:31,432 - INFO - 启用原始数据保存
2025-05-24 14:47:31,433 - INFO - 启用原始数据打包模式
2025-05-24 14:47:31,433 - INFO - ============================================================
2025-05-24 14:47:31,433 - INFO - AkroCare工单采集程序启动
2025-05-24 14:47:31,433 - INFO - 登录邮箱: <EMAIL>
2025-05-24 14:47:31,433 - INFO - ChromeDriver: 自定义路径
2025-05-24 14:47:31,433 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:47:31,433 - INFO - API请求方式: requests
2025-05-24 14:47:31,434 - INFO - 代理设置: 禁用
2025-05-24 14:47:31,434 - INFO - 增量更新: 启用
2025-05-24 14:47:31,434 - INFO - 采集模式: 指定工单ID - [5766, 7514]
2025-05-24 14:47:31,434 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-24 14:47:31,434 - INFO - 原始数据: 启用 - 打包模式
2025-05-24 14:47:31,434 - INFO - 开始时间: 2025-05-24 14:47:31
2025-05-24 14:47:31,434 - INFO - ============================================================
2025-05-24 14:47:31,435 - INFO - 已禁用代理
2025-05-24 14:47:31,436 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:47:32,611 - INFO - WebDriver初始化成功
2025-05-24 14:47:32,611 - INFO - 开始登录...
2025-05-24 14:47:46,436 - INFO - 登录成功
2025-05-24 14:47:46,454 - INFO - Session headers和cookies已更新
2025-05-24 14:47:46,454 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 14:47:46,454 - INFO - 导航到工单页面...
2025-05-24 14:47:49,598 - INFO - Session headers和cookies已更新
2025-05-24 14:47:49,598 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 14:47:49,598 - INFO - 开始采集工单数据...
2025-05-24 14:47:49,598 - INFO - 指定工单ID采集模式，共 2 个工单
2025-05-24 14:47:49,599 - INFO - 正在处理第 1/2 个指定工单: 5766
2025-05-24 14:47:49,599 - INFO - ================================================================================
2025-05-24 14:47:49,599 - INFO - 正在获取工单 5766 的详细信息...
2025-05-24 14:47:49,599 - INFO - 产品线: UNKNOWN
2025-05-24 14:47:49,599 - INFO - 工单标题: 指定工单采集
2025-05-24 14:47:49,599 - INFO - --------------------------------------------------------------------------------
2025-05-24 14:47:53,200 - ERROR - 获取工单 5766 详细信息失败: 'RawDataManager' object has no attribute 'temp_dir'
2025-05-24 14:47:53,201 - INFO - ================================================================================
2025-05-24 14:47:55,207 - INFO - 正在处理第 2/2 个指定工单: 7514
2025-05-24 14:47:55,207 - INFO - ================================================================================
2025-05-24 14:47:55,207 - INFO - 正在获取工单 7514 的详细信息...
2025-05-24 14:47:55,207 - INFO - 产品线: UNKNOWN
2025-05-24 14:47:55,208 - INFO - 工单标题: 指定工单采集
2025-05-24 14:47:55,208 - INFO - --------------------------------------------------------------------------------
2025-05-24 14:47:58,456 - ERROR - 获取工单 7514 详细信息失败: 'RawDataManager' object has no attribute 'temp_dir'
2025-05-24 14:47:58,456 - INFO - ================================================================================
2025-05-24 14:48:00,463 - INFO - 数据已保存到: data\last_update.json
2025-05-24 14:48:00,463 - WARNING - 没有采集到工单数据
2025-05-24 14:48:02,593 - INFO - 资源已释放
2025-05-24 14:52:17,768 - INFO - 已禁用代理
2025-05-24 14:52:26,847 - INFO - 设置指定工单ID: [5766, 7514]
2025-05-24 14:52:26,847 - INFO - 启用原始数据保存
2025-05-24 14:52:26,847 - INFO - 启用原始数据打包模式
2025-05-24 14:52:26,848 - INFO - ============================================================
2025-05-24 14:52:26,848 - INFO - AkroCare工单采集程序启动
2025-05-24 14:52:26,848 - INFO - 登录邮箱: <EMAIL>
2025-05-24 14:52:26,848 - INFO - ChromeDriver: 自定义路径
2025-05-24 14:52:26,848 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:52:26,848 - INFO - API请求方式: requests
2025-05-24 14:52:26,848 - INFO - 代理设置: 禁用
2025-05-24 14:52:26,848 - INFO - 增量更新: 启用
2025-05-24 14:52:26,849 - INFO - 采集模式: 指定工单ID - [5766, 7514]
2025-05-24 14:52:26,849 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-24 14:52:26,849 - INFO - 原始数据: 启用 - 打包模式
2025-05-24 14:52:26,849 - INFO - 开始时间: 2025-05-24 14:52:26
2025-05-24 14:52:26,849 - INFO - ============================================================
2025-05-24 14:52:26,849 - INFO - 已禁用代理
2025-05-24 14:52:26,850 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 14:52:28,025 - INFO - WebDriver初始化成功
2025-05-24 14:52:28,025 - INFO - 开始登录...
2025-05-24 14:52:42,056 - INFO - 登录成功
2025-05-24 14:52:42,069 - INFO - Session headers和cookies已更新
2025-05-24 14:52:42,069 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 14:52:42,069 - INFO - 导航到工单页面...
2025-05-24 14:52:45,200 - INFO - Session headers和cookies已更新
2025-05-24 14:52:45,200 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 14:52:45,200 - INFO - 开始采集工单数据...
2025-05-24 14:52:45,200 - INFO - 指定工单ID模式，从工单列表中筛选指定ID: [5766, 7514]
2025-05-24 14:52:45,201 - INFO - 开始从工单列表中查找指定工单ID: [5766, 7514]
2025-05-24 14:52:45,201 - INFO - 将在两种状态中搜索: Open(4), Closed(5)
2025-05-24 14:52:45,201 - INFO - 在状态 4 中搜索指定工单...
2025-05-24 14:52:45,201 - INFO - 搜索状态 4 第 1 页...
2025-05-24 14:52:45,204 - ERROR - Requests POST请求失败: 'RawDataManager' object has no attribute 'temp_dir'
2025-05-24 14:52:45,204 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-24 14:52:45,204 - ERROR - 从工单列表获取指定工单失败: 'RawDataManager' object has no attribute 'temp_dir'
2025-05-24 14:52:45,204 - WARNING - 没有获取到工单概要信息
2025-05-24 14:52:45,205 - WARNING - 没有采集到工单数据
2025-05-24 14:52:47,331 - INFO - 资源已释放
2025-05-24 14:58:13,343 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\cases_list\cases_list_page1_status5_20250524_145813.json
2025-05-24 14:58:13,344 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\case_details\case_detail_123_20250524_145813.json
2025-05-24 14:58:13,346 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\temp_20250524_145813\cases_list_page1_status5_20250524_145813.json
2025-05-24 14:58:13,347 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\temp_20250524_145813\case_detail_123_20250524_145813.json
2025-05-24 14:58:13,348 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\temp_20250524_145813\attachments_123_20250524_145813.json
2025-05-24 14:58:13,348 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\temp_20250524_145813\api_request_145813_348430.json
2025-05-24 14:58:13,389 - INFO - 原始数据已打包保存: C:\Users\<USER>\AppData\Local\Temp\tmp3aebgerg\raw_data_20250524_145813.zip
2025-05-24 15:04:29,801 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpls616swu\temp_20250524_150429\cases_list_page1_status5_20250524_150429.json
2025-05-24 15:04:29,810 - INFO - 原始数据已打包保存: C:\Users\<USER>\AppData\Local\Temp\tmpls616swu\raw_data_20250524_150429.zip
2025-05-24 15:20:26,468 - INFO - 设置指定工单ID: [5766, 7514]
2025-05-24 15:20:26,468 - INFO - 启用原始数据保存
2025-05-24 15:20:26,468 - INFO - 启用原始数据打包模式
2025-05-24 15:20:26,468 - INFO - ============================================================
2025-05-24 15:20:26,469 - INFO - AkroCare工单采集程序启动
2025-05-24 15:20:26,469 - INFO - 登录邮箱: <EMAIL>
2025-05-24 15:20:26,469 - INFO - ChromeDriver: 自定义路径
2025-05-24 15:20:26,469 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 15:20:26,469 - INFO - API请求方式: requests
2025-05-24 15:20:26,469 - INFO - 代理设置: 禁用
2025-05-24 15:20:26,469 - INFO - 增量更新: 启用
2025-05-24 15:20:26,469 - INFO - 采集模式: 指定工单ID - [5766, 7514]
2025-05-24 15:20:26,469 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-24 15:20:26,469 - INFO - 原始数据: 启用 - 打包模式
2025-05-24 15:20:26,469 - INFO - 开始时间: 2025-05-24 15:20:26
2025-05-24 15:20:26,470 - INFO - ============================================================
2025-05-24 15:20:26,470 - INFO - 已禁用代理
2025-05-24 15:20:26,471 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-24 15:20:27,652 - INFO - WebDriver初始化成功
2025-05-24 15:20:27,652 - INFO - 开始登录...
2025-05-24 15:20:41,428 - INFO - 登录成功
2025-05-24 15:20:41,442 - INFO - Session headers和cookies已更新
2025-05-24 15:20:41,442 - INFO - 登录成功，已保存 2 个cookies
2025-05-24 15:20:41,442 - INFO - 导航到工单页面...
2025-05-24 15:20:44,579 - INFO - Session headers和cookies已更新
2025-05-24 15:20:44,579 - INFO - 成功导航到工单页面，session信息已更新
2025-05-24 15:20:44,579 - INFO - 开始采集工单数据...
2025-05-24 15:20:44,579 - INFO - 指定工单ID模式，从工单列表中筛选指定ID: [5766, 7514]
2025-05-24 15:20:44,579 - INFO - 开始从工单列表中查找指定工单ID: [5766, 7514]
2025-05-24 15:20:44,579 - INFO - 将在两种状态中搜索: Open(4), Closed(5)
2025-05-24 15:20:44,580 - INFO - 在状态 4 中搜索指定工单...
2025-05-24 15:20:44,580 - INFO - 搜索状态 4 第 1 页...
2025-05-24 15:20:44,583 - INFO - 数据已保存到: raw_data\temp_20250524_152044\api_request_152044_583417.json
2025-05-24 15:20:45,008 - INFO - 数据已保存到: raw_data\temp_20250524_152044\cases_list_page1_status4_20250524_152044.json
2025-05-24 15:20:45,009 - INFO - 找到指定工单 7514: MP32 指标说明
2025-05-24 15:20:45,009 - INFO - 在状态 5 中搜索指定工单...
2025-05-24 15:20:45,009 - INFO - 搜索状态 5 第 1 页...
2025-05-24 15:20:45,013 - INFO - 数据已保存到: raw_data\temp_20250524_152044\api_request_152045_013295.json
2025-05-24 15:20:45,485 - INFO - 数据已保存到: raw_data\temp_20250524_152044\cases_list_page1_status5_20250524_152044.json
2025-05-24 15:20:45,485 - INFO - 找到指定工单 5766: ddr phy 手册寄存器疑问汇总
2025-05-24 15:20:46,495 - INFO - 从工单列表中找到 2 个指定工单
2025-05-24 15:20:46,495 - INFO - 正在处理第 1/2 个工单: 7514
2025-05-24 15:20:46,495 - INFO - ================================================================================
2025-05-24 15:20:46,495 - INFO - 正在获取工单 7514 的详细信息...
2025-05-24 15:20:46,496 - INFO - 产品线: MP32
2025-05-24 15:20:46,496 - INFO - 工单标题: MP32 指标说明
2025-05-24 15:20:46,496 - INFO - --------------------------------------------------------------------------------
2025-05-24 15:20:50,396 - INFO - 工单 7514 提取到 0 条评论
2025-05-24 15:20:50,398 - INFO - 工单 7514 没有附件
2025-05-24 15:20:50,399 - INFO - 数据已保存到: raw_data\temp_20250524_152044\case_detail_7514_20250524_152044.json
2025-05-24 15:20:50,399 - INFO - 工单 7514 详细信息采集完成
2025-05-24 15:20:50,400 - INFO - ================================================================================
2025-05-24 15:20:52,404 - INFO - 正在处理第 2/2 个工单: 5766
2025-05-24 15:20:52,404 - INFO - ================================================================================
2025-05-24 15:20:52,404 - INFO - 正在获取工单 5766 的详细信息...
2025-05-24 15:20:52,404 - INFO - 产品线: DDR4/3
2025-05-24 15:20:52,405 - INFO - 工单标题: ddr phy 手册寄存器疑问汇总
2025-05-24 15:20:52,405 - INFO - --------------------------------------------------------------------------------
2025-05-24 15:20:56,016 - INFO - 工单 5766 提取到 0 条评论
2025-05-24 15:20:56,017 - INFO - 工单 5766 没有附件
2025-05-24 15:20:56,017 - INFO - 工单 5766 没有附件或图片，跳过目录创建
2025-05-24 15:20:56,018 - INFO - 数据已保存到: raw_data\temp_20250524_152044\case_detail_5766_20250524_152044.json
2025-05-24 15:20:56,018 - INFO - 工单 5766 详细信息采集完成
2025-05-24 15:20:56,018 - INFO - ================================================================================
2025-05-24 15:20:58,026 - INFO - 数据已保存到: data\last_update.json
2025-05-24 15:20:58,136 - INFO - 原始数据已打包保存: raw_data\raw_data_20250524_152044.zip
2025-05-24 15:20:58,139 - INFO - 数据已保存到: data\cases_data.json
2025-05-24 15:20:58,139 - INFO - 已保存 2 条工单数据
2025-05-24 15:20:58,140 - INFO - ============================================================
2025-05-24 15:20:58,140 - INFO - 采集完成！共采集 2 条工单
2025-05-24 15:20:58,140 - INFO - 数据保存位置: data\cases_data.json
2025-05-24 15:20:58,140 - INFO - 附件保存位置: data\attachments
2025-05-24 15:20:58,140 - INFO - 结束时间: 2025-05-24 15:20:58
2025-05-24 15:20:58,140 - INFO - ============================================================
2025-05-24 15:21:00,244 - INFO - 资源已释放
2025-05-29 22:45:58,959 - INFO - 开始创建全量模式数据包...
2025-05-29 22:45:58,963 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558\metadata.json
2025-05-29 22:45:58,964 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558\patch_content.json
2025-05-29 22:45:58,965 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558
2025-05-29 22:45:58,973 - INFO - 开始创建全量模式数据包...
2025-05-29 22:45:58,974 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558\metadata.json
2025-05-29 22:45:58,975 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558\patch_content.json
2025-05-29 22:45:58,997 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558.zip
2025-05-29 22:45:58,998 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpl6dmsbpg\full_20250529_224558.zip
2025-05-29 22:45:59,013 - INFO - 开始创建全量模式数据包...
2025-05-29 22:45:59,014 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\full_20250529_224559\metadata.json
2025-05-29 22:45:59,015 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\full_20250529_224559\patch_content.json
2025-05-29 22:45:59,033 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\full_20250529_224559.zip
2025-05-29 22:45:59,034 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\full_20250529_224559.zip
2025-05-29 22:45:59,035 - INFO - 开始创建增量模式Patch...
2025-05-29 22:45:59,045 - INFO - 更新工单: 1002 - 基础工单2-更新
2025-05-29 22:45:59,045 - INFO - 新增工单: 1003 - 新增工单3
2025-05-29 22:45:59,046 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\patches\patch_20250529_224559\metadata.json
2025-05-29 22:45:59,047 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\patches\patch_20250529_224559\patch_content.json
2025-05-29 22:45:59,066 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\patches\patch_20250529_224559.zip
2025-05-29 22:45:59,068 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\tmpgcyqdf1r\patches\patch_20250529_224559.zip
2025-05-29 22:45:59,080 - INFO - 开始创建全量模式数据包...
2025-05-29 22:45:59,081 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\full_20250529_224559\metadata.json
2025-05-29 22:45:59,082 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\full_20250529_224559\patch_content.json
2025-05-29 22:45:59,101 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\full_20250529_224559.zip
2025-05-29 22:45:59,102 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\full_20250529_224559.zip
2025-05-29 22:45:59,102 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\test_patch\metadata.json
2025-05-29 22:45:59,103 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\test_patch\patch_content.json
2025-05-29 22:45:59,120 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\test_patch.zip
2025-05-29 22:45:59,132 - INFO - 开始应用Patch到空目录...
2025-05-29 22:45:59,132 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\test_patch.zip
2025-05-29 22:45:59,132 - INFO - 添加新增工单: 2003
2025-05-29 22:45:59,133 - INFO - 添加新增工单: 2004
2025-05-29 22:45:59,133 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\empty_result.json
2025-05-29 22:45:59,133 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\empty_result.json
2025-05-29 22:45:59,143 - INFO - 开始应用Patch到现有数据...
2025-05-29 22:45:59,153 - INFO - 加载基础数据: 2 个工单
2025-05-29 22:45:59,153 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\test_patch.zip
2025-05-29 22:45:59,155 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\merge_result.json
2025-05-29 22:45:59,155 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpbaybiiqn\merge_result.json
2025-05-29 23:00:11,646 - INFO - 开始生成完整测试数据集...
2025-05-29 23:00:11,646 - INFO - 生成基础数据集...
2025-05-29 23:00:11,647 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:00:11,759 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:00:11,761 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:00:11,762 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:00:11,764 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:00:11,765 - INFO - 数据已保存到: test_data_complete\base_dataset.json
2025-05-29 23:00:11,765 - INFO - 基础数据集已保存: test_data_complete\base_dataset.json
2025-05-29 23:00:11,765 - INFO - 生成增量数据集...
2025-05-29 23:00:11,766 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:00:11,767 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:00:11,769 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:00:11,771 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:00:11,772 - INFO - 数据已保存到: test_data_complete\incremental_dataset.json
2025-05-29 23:00:11,772 - INFO - 增量数据集已保存: test_data_complete\incremental_dataset.json
2025-05-29 23:00:11,773 - INFO - 数据已保存到: test_data_complete\attachment_manifest.json
2025-05-29 23:00:11,773 - INFO - 附件清单已保存: test_data_complete\attachment_manifest.json
2025-05-29 23:00:11,774 - INFO - 数据已保存到: test_data_complete\test_data_summary.json
2025-05-29 23:00:11,774 - INFO - 测试数据摘要已保存: test_data_complete\test_data_summary.json
2025-05-29 23:00:24,294 - INFO - ================================================================================
2025-05-29 23:00:24,295 - INFO - 开始运行综合测试套件
2025-05-29 23:00:24,295 - INFO - ================================================================================
2025-05-29 23:00:24,295 - INFO - 开始测试数据生成...
2025-05-29 23:00:24,295 - INFO - 开始生成完整测试数据集...
2025-05-29 23:00:24,295 - INFO - 生成基础数据集...
2025-05-29 23:00:24,296 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:00:24,343 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:00:24,346 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:00:24,347 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:00:24,348 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:00:24,349 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:00:24,349 - INFO - 基础数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:00:24,350 - INFO - 生成增量数据集...
2025-05-29 23:00:24,351 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:00:24,351 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:00:24,353 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:00:24,355 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:00:24,356 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:00:24,356 - INFO - 增量数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:00:24,358 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:00:24,358 - INFO - 附件清单已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:00:24,358 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:00:24,358 - INFO - 测试数据摘要已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:00:24,359 - INFO - 数据生成测试: ✅ 通过
2025-05-29 23:00:24,359 - INFO -   详情: 生成5个基础工单，5个增量工单，16个附件
2025-05-29 23:00:24,377 - INFO - 开始测试附件处理...
2025-05-29 23:00:24,378 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj
2025-05-29 23:00:24,379 - INFO - 测试数据包含9个附件，其中3个图片
2025-05-29 23:00:24,379 - INFO - 开始创建全量模式数据包...
2025-05-29 23:00:24,380 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\metadata.json
2025-05-29 23:00:24,382 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\patch_content.json
2025-05-29 23:00:24,451 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,453 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,463 - INFO - ZIP包中包含9个附件文件
2025-05-29 23:00:24,463 - INFO - 附件分布在5个工单目录中
2025-05-29 23:00:24,463 - INFO - 附件处理测试: ✅ 通过
2025-05-29 23:00:24,463 - INFO -   详情: 处理9个附件（3个图片），ZIP包含9个文件
2025-05-29 23:00:24,463 - INFO - 开始测试图片内容检测...
2025-05-29 23:00:24,464 - INFO - 发现3个工单描述包含图片
2025-05-29 23:00:24,464 - INFO - 发现3条评论包含图片
2025-05-29 23:00:24,464 - INFO - 图片内容检测测试: ✅ 通过
2025-05-29 23:00:24,464 - INFO -   详情: 工单描述图片: 3, 评论图片: 3
2025-05-29 23:00:24,464 - INFO - 开始测试全量模式...
2025-05-29 23:00:24,464 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_e3xntgu4
2025-05-29 23:00:24,465 - INFO - 开始创建全量模式数据包...
2025-05-29 23:00:24,465 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\metadata.json
2025-05-29 23:00:24,467 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\patch_content.json
2025-05-29 23:00:24,475 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024
2025-05-29 23:00:24,476 - INFO - 全量模式包含9个附件文件
2025-05-29 23:00:24,476 - INFO - 开始创建全量模式数据包...
2025-05-29 23:00:24,477 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\metadata.json
2025-05-29 23:00:24,478 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\patch_content.json
2025-05-29 23:00:24,509 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,511 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,520 - INFO - 全量模式测试: ✅ 通过
2025-05-29 23:00:24,520 - INFO -   详情: 目录模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024, ZIP模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,520 - INFO - 开始测试增量模式...
2025-05-29 23:00:24,521 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6kap31_t
2025-05-29 23:00:24,521 - INFO - 开始创建全量模式数据包...
2025-05-29 23:00:24,522 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\metadata.json
2025-05-29 23:00:24,523 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\patch_content.json
2025-05-29 23:00:24,553 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,555 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,555 - INFO - 开始创建增量模式Patch...
2025-05-29 23:00:24,565 - INFO - 更新工单: 1002 - 更新后的工单1002
2025-05-29 23:00:24,565 - INFO - 新增工单: 1006 - 基础测试工单1006
2025-05-29 23:00:24,565 - INFO - 新增工单: 1007 - 包含图片的工单1007
2025-05-29 23:00:24,565 - INFO - 新增工单: 1008 - 复杂工单1008
2025-05-29 23:00:24,566 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\patches\patch_20250529_230024\metadata.json
2025-05-29 23:00:24,567 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\patches\patch_20250529_230024\patch_content.json
2025-05-29 23:00:24,626 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\patches\patch_20250529_230024.zip
2025-05-29 23:00:24,628 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\patches\patch_20250529_230024.zip
2025-05-29 23:00:24,637 - INFO - 检测到3个新增工单: [1006, 1007, 1008]
2025-05-29 23:00:24,638 - INFO - 检测到1个更新工单: [1002]
2025-05-29 23:00:24,638 - INFO - 增量模式测试: ✅ 通过
2025-05-29 23:00:24,638 - INFO -   详情: 基础版本: 20250529_230024, 新增: 3, 更新: 1
2025-05-29 23:00:24,638 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a
2025-05-29 23:00:24,639 - INFO - 开始创建全量模式数据包...
2025-05-29 23:00:24,640 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\metadata.json
2025-05-29 23:00:24,641 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024\patch_content.json
2025-05-29 23:00:24,674 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,677 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\full_20250529_230024.zip
2025-05-29 23:00:24,677 - INFO - 开始测试Patch应用...
2025-05-29 23:00:24,678 - INFO - 解析Patch: patch_20250529_230024
2025-05-29 23:00:24,678 - INFO -   模式: incremental
2025-05-29 23:00:24,678 - INFO -   新增工单: 3
2025-05-29 23:00:24,678 - INFO -   更新工单: 1
2025-05-29 23:00:24,678 - INFO - 开始应用Patch到空目录...
2025-05-29 23:00:24,679 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\patches\patch_20250529_230024.zip
2025-05-29 23:00:24,679 - INFO - 添加新增工单: 1006
2025-05-29 23:00:24,679 - INFO - 添加新增工单: 1007
2025-05-29 23:00:24,679 - INFO - 添加新增工单: 1008
2025-05-29 23:00:24,680 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\empty_result.json
2025-05-29 23:00:24,681 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\empty_result.json
2025-05-29 23:00:24,691 - INFO - 空目录应用提取了3个新增工单
2025-05-29 23:00:24,692 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:00:24,702 - INFO - 加载基础数据: 5 个工单
2025-05-29 23:00:24,702 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6362_wvj\patches\patch_20250529_230024.zip
2025-05-29 23:00:24,705 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\merge_result.json
2025-05-29 23:00:24,705 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\merge_result.json
2025-05-29 23:00:24,715 - INFO - 数据合并后共有8个工单
2025-05-29 23:00:24,715 - INFO - 开始创建合并Patch...
2025-05-29 23:00:24,716 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\repacked.zip_temp\metadata.json
2025-05-29 23:00:24,718 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\repacked.zip_temp\patch_content.json
2025-05-29 23:00:24,734 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\repacked.zip
2025-05-29 23:00:24,735 - INFO - 合并Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_3ao_7n7a\repacked.zip
2025-05-29 23:00:24,735 - INFO - Patch应用测试: ✅ 通过
2025-05-29 23:00:24,735 - INFO -   详情: 解析1个Patch, 空目录提取3个工单, 合并后8个工单
2025-05-29 23:00:24,739 - INFO - 数据已保存到: comprehensive_test_report.json
2025-05-29 23:02:48,837 - INFO - ================================================================================
2025-05-29 23:02:48,837 - INFO - 运行快速测试套件
2025-05-29 23:02:48,837 - INFO - ================================================================================
2025-05-29 23:02:48,837 - INFO - 开始运行: 数据模式基础测试
2025-05-29 23:02:48,838 - INFO - 脚本: test_data_modes.py
2025-05-29 23:02:49,037 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,037 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249\metadata.json
2025-05-29 23:02:49,038 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,040 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249
2025-05-29 23:02:49,046 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,047 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249\metadata.json
2025-05-29 23:02:49,048 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,064 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249.zip
2025-05-29 23:02:49,065 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpm7cpswtq\full_20250529_230249.zip
2025-05-29 23:02:49,076 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,077 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\full_20250529_230249\metadata.json
2025-05-29 23:02:49,077 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,093 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\full_20250529_230249.zip
2025-05-29 23:02:49,094 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\full_20250529_230249.zip
2025-05-29 23:02:49,094 - INFO - 开始创建增量模式Patch...
2025-05-29 23:02:49,103 - INFO - 更新工单: 1002 - 基础工单2-更新
2025-05-29 23:02:49,103 - INFO - 新增工单: 1003 - 新增工单3
2025-05-29 23:02:49,103 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\patches\patch_20250529_230249\metadata.json
2025-05-29 23:02:49,104 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\patches\patch_20250529_230249\patch_content.json
2025-05-29 23:02:49,119 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\patches\patch_20250529_230249.zip
2025-05-29 23:02:49,120 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\tmpr9faijt9\patches\patch_20250529_230249.zip
2025-05-29 23:02:49,130 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,131 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\full_20250529_230249\metadata.json
2025-05-29 23:02:49,132 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,146 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\full_20250529_230249.zip
2025-05-29 23:02:49,147 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\full_20250529_230249.zip
2025-05-29 23:02:49,148 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\test_patch\metadata.json
2025-05-29 23:02:49,149 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\test_patch\patch_content.json
2025-05-29 23:02:49,162 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\test_patch.zip
2025-05-29 23:02:49,171 - INFO - 开始应用Patch到空目录...
2025-05-29 23:02:49,172 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\test_patch.zip
2025-05-29 23:02:49,172 - INFO - 添加新增工单: 2003
2025-05-29 23:02:49,172 - INFO - 添加新增工单: 2004
2025-05-29 23:02:49,173 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\empty_result.json
2025-05-29 23:02:49,173 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\empty_result.json
2025-05-29 23:02:49,181 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:02:49,190 - INFO - 加载基础数据: 2 个工单
2025-05-29 23:02:49,190 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\test_patch.zip
2025-05-29 23:02:49,191 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\merge_result.json
2025-05-29 23:02:49,191 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp6ry5tf1f\merge_result.json
2025-05-29 23:02:49,213 - INFO - ✅ 数据模式基础测试 - 成功 (0.37s)
2025-05-29 23:02:49,214 - INFO - 开始运行: 综合功能测试
2025-05-29 23:02:49,214 - INFO - 脚本: comprehensive_test_suite.py
2025-05-29 23:02:49,403 - INFO - ================================================================================
2025-05-29 23:02:49,404 - INFO - 开始运行综合测试套件
2025-05-29 23:02:49,404 - INFO - ================================================================================
2025-05-29 23:02:49,404 - INFO - 开始测试数据生成...
2025-05-29 23:02:49,404 - INFO - 开始生成完整测试数据集...
2025-05-29 23:02:49,404 - INFO - 生成基础数据集...
2025-05-29 23:02:49,405 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:02:49,447 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:02:49,449 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:02:49,449 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:02:49,451 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:02:49,452 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:02:49,452 - INFO - 基础数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:02:49,452 - INFO - 生成增量数据集...
2025-05-29 23:02:49,452 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:02:49,453 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:02:49,455 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:02:49,457 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:02:49,457 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:02:49,457 - INFO - 增量数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:02:49,459 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:02:49,459 - INFO - 附件清单已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:02:49,459 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:02:49,459 - INFO - 测试数据摘要已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:02:49,459 - INFO - 数据生成测试: ✅ 通过
2025-05-29 23:02:49,459 - INFO -   详情: 生成5个基础工单，5个增量工单，16个附件
2025-05-29 23:02:49,476 - INFO - 开始测试附件处理...
2025-05-29 23:02:49,477 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_
2025-05-29 23:02:49,477 - INFO - 测试数据包含9个附件，其中3个图片
2025-05-29 23:02:49,477 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,478 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\metadata.json
2025-05-29 23:02:49,479 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,541 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,544 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,553 - INFO - ZIP包中包含9个附件文件
2025-05-29 23:02:49,553 - INFO - 附件分布在5个工单目录中
2025-05-29 23:02:49,553 - INFO - 附件处理测试: ✅ 通过
2025-05-29 23:02:49,553 - INFO -   详情: 处理9个附件（3个图片），ZIP包含9个文件
2025-05-29 23:02:49,553 - INFO - 开始测试图片内容检测...
2025-05-29 23:02:49,553 - INFO - 发现3个工单描述包含图片
2025-05-29 23:02:49,553 - INFO - 发现3条评论包含图片
2025-05-29 23:02:49,553 - INFO - 图片内容检测测试: ✅ 通过
2025-05-29 23:02:49,553 - INFO -   详情: 工单描述图片: 3, 评论图片: 3
2025-05-29 23:02:49,553 - INFO - 开始测试全量模式...
2025-05-29 23:02:49,554 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_cjjzp8wn
2025-05-29 23:02:49,554 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,554 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\metadata.json
2025-05-29 23:02:49,556 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,564 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249
2025-05-29 23:02:49,564 - INFO - 全量模式包含9个附件文件
2025-05-29 23:02:49,564 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,565 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\metadata.json
2025-05-29 23:02:49,567 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,597 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,599 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,610 - INFO - 全量模式测试: ✅ 通过
2025-05-29 23:02:49,610 - INFO -   详情: 目录模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249, ZIP模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,610 - INFO - 开始测试增量模式...
2025-05-29 23:02:49,610 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_oosvjdnj
2025-05-29 23:02:49,610 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,611 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\metadata.json
2025-05-29 23:02:49,613 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,643 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,646 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,646 - INFO - 开始创建增量模式Patch...
2025-05-29 23:02:49,655 - INFO - 更新工单: 1002 - 更新后的工单1002
2025-05-29 23:02:49,656 - INFO - 新增工单: 1006 - 基础测试工单1006
2025-05-29 23:02:49,656 - INFO - 新增工单: 1007 - 包含图片的工单1007
2025-05-29 23:02:49,656 - INFO - 新增工单: 1008 - 复杂工单1008
2025-05-29 23:02:49,656 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\patches\patch_20250529_230249\metadata.json
2025-05-29 23:02:49,658 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\patches\patch_20250529_230249\patch_content.json
2025-05-29 23:02:49,716 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\patches\patch_20250529_230249.zip
2025-05-29 23:02:49,718 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\patches\patch_20250529_230249.zip
2025-05-29 23:02:49,727 - INFO - 检测到3个新增工单: [1006, 1007, 1008]
2025-05-29 23:02:49,727 - INFO - 检测到1个更新工单: [1002]
2025-05-29 23:02:49,727 - INFO - 增量模式测试: ✅ 通过
2025-05-29 23:02:49,728 - INFO -   详情: 基础版本: 20250529_230249, 新增: 3, 更新: 1
2025-05-29 23:02:49,728 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov
2025-05-29 23:02:49,728 - INFO - 开始创建全量模式数据包...
2025-05-29 23:02:49,729 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\metadata.json
2025-05-29 23:02:49,730 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249\patch_content.json
2025-05-29 23:02:49,762 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,764 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\full_20250529_230249.zip
2025-05-29 23:02:49,764 - INFO - 开始测试Patch应用...
2025-05-29 23:02:49,765 - INFO - 解析Patch: patch_20250529_230249
2025-05-29 23:02:49,765 - INFO -   模式: incremental
2025-05-29 23:02:49,765 - INFO -   新增工单: 3
2025-05-29 23:02:49,765 - INFO -   更新工单: 1
2025-05-29 23:02:49,765 - INFO - 开始应用Patch到空目录...
2025-05-29 23:02:49,765 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\patches\patch_20250529_230249.zip
2025-05-29 23:02:49,766 - INFO - 添加新增工单: 1006
2025-05-29 23:02:49,766 - INFO - 添加新增工单: 1007
2025-05-29 23:02:49,766 - INFO - 添加新增工单: 1008
2025-05-29 23:02:49,767 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\empty_result.json
2025-05-29 23:02:49,767 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\empty_result.json
2025-05-29 23:02:49,776 - INFO - 空目录应用提取了3个新增工单
2025-05-29 23:02:49,776 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:02:49,784 - INFO - 加载基础数据: 5 个工单
2025-05-29 23:02:49,784 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_0tk4s5g_\patches\patch_20250529_230249.zip
2025-05-29 23:02:49,786 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\merge_result.json
2025-05-29 23:02:49,786 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\merge_result.json
2025-05-29 23:02:49,795 - INFO - 数据合并后共有8个工单
2025-05-29 23:02:49,795 - INFO - 开始创建合并Patch...
2025-05-29 23:02:49,797 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\repacked.zip_temp\metadata.json
2025-05-29 23:02:49,799 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\repacked.zip_temp\patch_content.json
2025-05-29 23:02:49,813 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\repacked.zip
2025-05-29 23:02:49,814 - INFO - 合并Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_qtdp7xov\repacked.zip
2025-05-29 23:02:49,814 - INFO - Patch应用测试: ✅ 通过
2025-05-29 23:02:49,814 - INFO -   详情: 解析1个Patch, 空目录提取3个工单, 合并后8个工单
2025-05-29 23:02:49,817 - INFO - 数据已保存到: comprehensive_test_report.json
2025-05-29 23:02:49,834 - INFO - ✅ 综合功能测试 - 成功 (0.62s)
2025-05-29 23:02:49,834 - INFO - 
快速测试结果: 2/2 通过
2025-05-29 23:04:50,862 - INFO - ================================================================================
2025-05-29 23:04:50,862 - INFO - 运行快速测试套件
2025-05-29 23:04:50,862 - INFO - ================================================================================
2025-05-29 23:04:50,862 - INFO - 开始运行: 数据模式基础测试
2025-05-29 23:04:50,863 - INFO - 脚本: test_data_modes.py
2025-05-29 23:04:51,053 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,054 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451\metadata.json
2025-05-29 23:04:51,055 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,056 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451
2025-05-29 23:04:51,062 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,062 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451\metadata.json
2025-05-29 23:04:51,063 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,079 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451.zip
2025-05-29 23:04:51,081 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmp3q9inx6r\full_20250529_230451.zip
2025-05-29 23:04:51,091 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,092 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\full_20250529_230451\metadata.json
2025-05-29 23:04:51,092 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,107 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\full_20250529_230451.zip
2025-05-29 23:04:51,108 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\full_20250529_230451.zip
2025-05-29 23:04:51,108 - INFO - 开始创建增量模式Patch...
2025-05-29 23:04:51,116 - INFO - 更新工单: 1002 - 基础工单2-更新
2025-05-29 23:04:51,116 - INFO - 新增工单: 1003 - 新增工单3
2025-05-29 23:04:51,117 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\patches\patch_20250529_230451\metadata.json
2025-05-29 23:04:51,118 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\patches\patch_20250529_230451\patch_content.json
2025-05-29 23:04:51,133 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\patches\patch_20250529_230451.zip
2025-05-29 23:04:51,134 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\tmpb0ijg078\patches\patch_20250529_230451.zip
2025-05-29 23:04:51,145 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,145 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\full_20250529_230451\metadata.json
2025-05-29 23:04:51,146 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,162 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\full_20250529_230451.zip
2025-05-29 23:04:51,163 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\full_20250529_230451.zip
2025-05-29 23:04:51,163 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\test_patch\metadata.json
2025-05-29 23:04:51,164 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\test_patch\patch_content.json
2025-05-29 23:04:51,178 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\test_patch.zip
2025-05-29 23:04:51,186 - INFO - 开始应用Patch到空目录...
2025-05-29 23:04:51,186 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\test_patch.zip
2025-05-29 23:04:51,187 - INFO - 添加新增工单: 2003
2025-05-29 23:04:51,187 - INFO - 添加新增工单: 2004
2025-05-29 23:04:51,187 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\empty_result.json
2025-05-29 23:04:51,188 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\empty_result.json
2025-05-29 23:04:51,195 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:04:51,202 - INFO - 加载基础数据: 2 个工单
2025-05-29 23:04:51,202 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\test_patch.zip
2025-05-29 23:04:51,203 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\merge_result.json
2025-05-29 23:04:51,203 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpar1mrp5s\merge_result.json
2025-05-29 23:04:51,226 - INFO - ✅ 数据模式基础测试 - 成功 (0.36s)
2025-05-29 23:04:51,226 - INFO - 开始运行: 综合功能测试
2025-05-29 23:04:51,226 - INFO - 脚本: comprehensive_test_suite.py
2025-05-29 23:04:51,418 - INFO - ================================================================================
2025-05-29 23:04:51,418 - INFO - 开始运行综合测试套件
2025-05-29 23:04:51,418 - INFO - ================================================================================
2025-05-29 23:04:51,418 - INFO - 开始测试数据生成...
2025-05-29 23:04:51,418 - INFO - 开始生成完整测试数据集...
2025-05-29 23:04:51,418 - INFO - 生成基础数据集...
2025-05-29 23:04:51,419 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:04:51,461 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:04:51,463 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:04:51,464 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:04:51,465 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:04:51,466 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:04:51,466 - INFO - 基础数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:04:51,466 - INFO - 生成增量数据集...
2025-05-29 23:04:51,467 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:04:51,467 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:04:51,469 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:04:51,471 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:04:51,471 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:04:51,471 - INFO - 增量数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:04:51,473 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:04:51,473 - INFO - 附件清单已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:04:51,473 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:04:51,473 - INFO - 测试数据摘要已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:04:51,473 - INFO - 数据生成测试: ✅ 通过
2025-05-29 23:04:51,473 - INFO -   详情: 生成5个基础工单，5个增量工单，16个附件
2025-05-29 23:04:51,489 - INFO - 开始测试附件处理...
2025-05-29 23:04:51,490 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8
2025-05-29 23:04:51,491 - INFO - 测试数据包含9个附件，其中3个图片
2025-05-29 23:04:51,491 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,491 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\metadata.json
2025-05-29 23:04:51,493 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,556 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,558 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,567 - INFO - ZIP包中包含9个附件文件
2025-05-29 23:04:51,567 - INFO - 附件分布在5个工单目录中
2025-05-29 23:04:51,568 - INFO - 附件处理测试: ✅ 通过
2025-05-29 23:04:51,568 - INFO -   详情: 处理9个附件（3个图片），ZIP包含9个文件
2025-05-29 23:04:51,568 - INFO - 开始测试图片内容检测...
2025-05-29 23:04:51,568 - INFO - 发现3个工单描述包含图片
2025-05-29 23:04:51,568 - INFO - 发现3条评论包含图片
2025-05-29 23:04:51,568 - INFO - 图片内容检测测试: ✅ 通过
2025-05-29 23:04:51,568 - INFO -   详情: 工单描述图片: 3, 评论图片: 3
2025-05-29 23:04:51,568 - INFO - 开始测试全量模式...
2025-05-29 23:04:51,568 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_tqtbg_6b
2025-05-29 23:04:51,568 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,569 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\metadata.json
2025-05-29 23:04:51,570 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,578 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451
2025-05-29 23:04:51,579 - INFO - 全量模式包含9个附件文件
2025-05-29 23:04:51,579 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,579 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\metadata.json
2025-05-29 23:04:51,581 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,609 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,611 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,621 - INFO - 全量模式测试: ✅ 通过
2025-05-29 23:04:51,621 - INFO -   详情: 目录模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451, ZIP模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,621 - INFO - 开始测试增量模式...
2025-05-29 23:04:51,622 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_8c2hm4wa
2025-05-29 23:04:51,622 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,622 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\metadata.json
2025-05-29 23:04:51,624 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,654 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,656 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,656 - INFO - 开始创建增量模式Patch...
2025-05-29 23:04:51,665 - INFO - 更新工单: 1002 - 更新后的工单1002
2025-05-29 23:04:51,665 - INFO - 新增工单: 1006 - 基础测试工单1006
2025-05-29 23:04:51,665 - INFO - 新增工单: 1007 - 包含图片的工单1007
2025-05-29 23:04:51,665 - INFO - 新增工单: 1008 - 复杂工单1008
2025-05-29 23:04:51,666 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\patches\patch_20250529_230451\metadata.json
2025-05-29 23:04:51,667 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\patches\patch_20250529_230451\patch_content.json
2025-05-29 23:04:51,723 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\patches\patch_20250529_230451.zip
2025-05-29 23:04:51,726 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\patches\patch_20250529_230451.zip
2025-05-29 23:04:51,734 - INFO - 检测到3个新增工单: [1006, 1007, 1008]
2025-05-29 23:04:51,734 - INFO - 检测到1个更新工单: [1002]
2025-05-29 23:04:51,734 - INFO - 增量模式测试: ✅ 通过
2025-05-29 23:04:51,734 - INFO -   详情: 基础版本: 20250529_230451, 新增: 3, 更新: 1
2025-05-29 23:04:51,734 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz
2025-05-29 23:04:51,735 - INFO - 开始创建全量模式数据包...
2025-05-29 23:04:51,735 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\metadata.json
2025-05-29 23:04:51,737 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451\patch_content.json
2025-05-29 23:04:51,767 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,770 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\full_20250529_230451.zip
2025-05-29 23:04:51,770 - INFO - 开始测试Patch应用...
2025-05-29 23:04:51,770 - INFO - 解析Patch: patch_20250529_230451
2025-05-29 23:04:51,770 - INFO -   模式: incremental
2025-05-29 23:04:51,771 - INFO -   新增工单: 3
2025-05-29 23:04:51,771 - INFO -   更新工单: 1
2025-05-29 23:04:51,771 - INFO - 开始应用Patch到空目录...
2025-05-29 23:04:51,771 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\patches\patch_20250529_230451.zip
2025-05-29 23:04:51,771 - INFO - 添加新增工单: 1006
2025-05-29 23:04:51,771 - INFO - 添加新增工单: 1007
2025-05-29 23:04:51,771 - INFO - 添加新增工单: 1008
2025-05-29 23:04:51,772 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\empty_result.json
2025-05-29 23:04:51,772 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\empty_result.json
2025-05-29 23:04:51,781 - INFO - 空目录应用提取了3个新增工单
2025-05-29 23:04:51,781 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:04:51,789 - INFO - 加载基础数据: 5 个工单
2025-05-29 23:04:51,790 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_6icyeko8\patches\patch_20250529_230451.zip
2025-05-29 23:04:51,791 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\merge_result.json
2025-05-29 23:04:51,791 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\merge_result.json
2025-05-29 23:04:51,800 - INFO - 数据合并后共有8个工单
2025-05-29 23:04:51,800 - INFO - 开始创建合并Patch...
2025-05-29 23:04:51,802 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\repacked.zip_temp\metadata.json
2025-05-29 23:04:51,804 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\repacked.zip_temp\patch_content.json
2025-05-29 23:04:51,819 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\repacked.zip
2025-05-29 23:04:51,820 - INFO - 合并Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_ycbgknaz\repacked.zip
2025-05-29 23:04:51,820 - INFO - Patch应用测试: ✅ 通过
2025-05-29 23:04:51,820 - INFO -   详情: 解析1个Patch, 空目录提取3个工单, 合并后8个工单
2025-05-29 23:04:51,822 - INFO - 数据已保存到: comprehensive_test_report.json
2025-05-29 23:04:51,843 - INFO - ✅ 综合功能测试 - 成功 (0.62s)
2025-05-29 23:04:51,843 - INFO - 
快速测试结果: 2/2 通过
2025-05-29 23:05:05,933 - INFO - ================================================================================
2025-05-29 23:05:05,933 - INFO - 开始运行完整测试套件
2025-05-29 23:05:05,933 - INFO - ================================================================================
2025-05-29 23:05:05,934 - INFO - 开始运行: 测试数据生成器
2025-05-29 23:05:05,934 - INFO - 脚本: test_data_generator.py
2025-05-29 23:05:06,116 - INFO - 开始生成完整测试数据集...
2025-05-29 23:05:06,117 - INFO - 生成基础数据集...
2025-05-29 23:05:06,117 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:05:06,158 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:05:06,160 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:05:06,160 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:05:06,162 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:05:06,163 - INFO - 数据已保存到: test_data_complete\base_dataset.json
2025-05-29 23:05:06,163 - INFO - 基础数据集已保存: test_data_complete\base_dataset.json
2025-05-29 23:05:06,163 - INFO - 生成增量数据集...
2025-05-29 23:05:06,164 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:05:06,164 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:05:06,166 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:05:06,168 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:05:06,169 - INFO - 数据已保存到: test_data_complete\incremental_dataset.json
2025-05-29 23:05:06,169 - INFO - 增量数据集已保存: test_data_complete\incremental_dataset.json
2025-05-29 23:05:06,170 - INFO - 数据已保存到: test_data_complete\attachment_manifest.json
2025-05-29 23:05:06,170 - INFO - 附件清单已保存: test_data_complete\attachment_manifest.json
2025-05-29 23:05:06,170 - INFO - 数据已保存到: test_data_complete\test_data_summary.json
2025-05-29 23:05:06,171 - INFO - 测试数据摘要已保存: test_data_complete\test_data_summary.json
2025-05-29 23:05:06,186 - INFO - ✅ 测试数据生成器 - 成功 (0.25s)
2025-05-29 23:05:06,186 - INFO - ------------------------------------------------------------
2025-05-29 23:05:06,187 - INFO - 开始运行: 数据模式基础测试
2025-05-29 23:05:06,187 - INFO - 脚本: test_data_modes.py
2025-05-29 23:05:06,375 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,375 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506\metadata.json
2025-05-29 23:05:06,376 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506\patch_content.json
2025-05-29 23:05:06,379 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506
2025-05-29 23:05:06,386 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,387 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506\metadata.json
2025-05-29 23:05:06,387 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506\patch_content.json
2025-05-29 23:05:06,403 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506.zip
2025-05-29 23:05:06,404 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmphzs15jg9\full_20250529_230506.zip
2025-05-29 23:05:06,414 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,415 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\full_20250529_230506\metadata.json
2025-05-29 23:05:06,416 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\full_20250529_230506\patch_content.json
2025-05-29 23:05:06,432 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\full_20250529_230506.zip
2025-05-29 23:05:06,433 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\full_20250529_230506.zip
2025-05-29 23:05:06,433 - INFO - 开始创建增量模式Patch...
2025-05-29 23:05:06,442 - INFO - 更新工单: 1002 - 基础工单2-更新
2025-05-29 23:05:06,442 - INFO - 新增工单: 1003 - 新增工单3
2025-05-29 23:05:06,443 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\patches\patch_20250529_230506\metadata.json
2025-05-29 23:05:06,444 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\patches\patch_20250529_230506\patch_content.json
2025-05-29 23:05:06,460 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\patches\patch_20250529_230506.zip
2025-05-29 23:05:06,461 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\tmpm8n9vswb\patches\patch_20250529_230506.zip
2025-05-29 23:05:06,472 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,472 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\full_20250529_230506\metadata.json
2025-05-29 23:05:06,473 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\full_20250529_230506\patch_content.json
2025-05-29 23:05:06,489 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\full_20250529_230506.zip
2025-05-29 23:05:06,490 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\full_20250529_230506.zip
2025-05-29 23:05:06,491 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\test_patch\metadata.json
2025-05-29 23:05:06,492 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\test_patch\patch_content.json
2025-05-29 23:05:06,507 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\test_patch.zip
2025-05-29 23:05:06,517 - INFO - 开始应用Patch到空目录...
2025-05-29 23:05:06,517 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\test_patch.zip
2025-05-29 23:05:06,517 - INFO - 添加新增工单: 2003
2025-05-29 23:05:06,517 - INFO - 添加新增工单: 2004
2025-05-29 23:05:06,518 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\empty_result.json
2025-05-29 23:05:06,518 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\empty_result.json
2025-05-29 23:05:06,527 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:05:06,539 - INFO - 加载基础数据: 2 个工单
2025-05-29 23:05:06,539 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\test_patch.zip
2025-05-29 23:05:06,540 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\merge_result.json
2025-05-29 23:05:06,540 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp_p4u8_yp\merge_result.json
2025-05-29 23:05:06,571 - INFO - ✅ 数据模式基础测试 - 成功 (0.38s)
2025-05-29 23:05:06,571 - INFO - ------------------------------------------------------------
2025-05-29 23:05:06,572 - INFO - 开始运行: 综合测试套件
2025-05-29 23:05:06,572 - INFO - 脚本: comprehensive_test_suite.py
2025-05-29 23:05:06,764 - INFO - ================================================================================
2025-05-29 23:05:06,764 - INFO - 开始运行综合测试套件
2025-05-29 23:05:06,764 - INFO - ================================================================================
2025-05-29 23:05:06,764 - INFO - 开始测试数据生成...
2025-05-29 23:05:06,764 - INFO - 开始生成完整测试数据集...
2025-05-29 23:05:06,765 - INFO - 生成基础数据集...
2025-05-29 23:05:06,765 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:05:06,806 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:05:06,808 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:05:06,809 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:05:06,810 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:05:06,811 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:05:06,811 - INFO - 基础数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\base_dataset.json
2025-05-29 23:05:06,811 - INFO - 生成增量数据集...
2025-05-29 23:05:06,812 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:05:06,812 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:05:06,814 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:05:06,816 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:05:06,817 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:05:06,817 - INFO - 增量数据集已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\incremental_dataset.json
2025-05-29 23:05:06,818 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:05:06,818 - INFO - 附件清单已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\attachment_manifest.json
2025-05-29 23:05:06,818 - INFO - 数据已保存到: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:05:06,818 - INFO - 测试数据摘要已保存: C:\Users\<USER>\Documents\augment-projects\selenium-aks\test_data_complete\test_data_summary.json
2025-05-29 23:05:06,819 - INFO - 数据生成测试: ✅ 通过
2025-05-29 23:05:06,819 - INFO -   详情: 生成5个基础工单，5个增量工单，16个附件
2025-05-29 23:05:06,837 - INFO - 开始测试附件处理...
2025-05-29 23:05:06,838 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs
2025-05-29 23:05:06,860 - INFO - 测试数据包含9个附件，其中3个图片
2025-05-29 23:05:06,860 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,861 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\metadata.json
2025-05-29 23:05:06,862 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\patch_content.json
2025-05-29 23:05:06,939 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:06,942 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:06,953 - INFO - ZIP包中包含9个附件文件
2025-05-29 23:05:06,953 - INFO - 附件分布在5个工单目录中
2025-05-29 23:05:06,953 - INFO - 附件处理测试: ✅ 通过
2025-05-29 23:05:06,953 - INFO -   详情: 处理9个附件（3个图片），ZIP包含9个文件
2025-05-29 23:05:06,954 - INFO - 开始测试图片内容检测...
2025-05-29 23:05:06,954 - INFO - 发现3个工单描述包含图片
2025-05-29 23:05:06,954 - INFO - 发现3条评论包含图片
2025-05-29 23:05:06,954 - INFO - 图片内容检测测试: ✅ 通过
2025-05-29 23:05:06,954 - INFO -   详情: 工单描述图片: 3, 评论图片: 3
2025-05-29 23:05:06,954 - INFO - 开始测试全量模式...
2025-05-29 23:05:06,954 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_oyyu75mr
2025-05-29 23:05:06,954 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,955 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\metadata.json
2025-05-29 23:05:06,956 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\patch_content.json
2025-05-29 23:05:06,966 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506
2025-05-29 23:05:06,967 - INFO - 全量模式包含9个附件文件
2025-05-29 23:05:06,967 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:06,968 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\metadata.json
2025-05-29 23:05:06,969 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\patch_content.json
2025-05-29 23:05:07,001 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,003 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,012 - INFO - 全量模式测试: ✅ 通过
2025-05-29 23:05:07,013 - INFO -   详情: 目录模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506, ZIP模式: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,013 - INFO - 开始测试增量模式...
2025-05-29 23:05:07,013 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_rb13mc6u
2025-05-29 23:05:07,013 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:07,014 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\metadata.json
2025-05-29 23:05:07,016 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\patch_content.json
2025-05-29 23:05:07,050 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,053 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,053 - INFO - 开始创建增量模式Patch...
2025-05-29 23:05:07,063 - INFO - 更新工单: 1002 - 更新后的工单1002
2025-05-29 23:05:07,063 - INFO - 新增工单: 1006 - 基础测试工单1006
2025-05-29 23:05:07,063 - INFO - 新增工单: 1007 - 包含图片的工单1007
2025-05-29 23:05:07,063 - INFO - 新增工单: 1008 - 复杂工单1008
2025-05-29 23:05:07,064 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\patches\patch_20250529_230506\metadata.json
2025-05-29 23:05:07,066 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\patches\patch_20250529_230506\patch_content.json
2025-05-29 23:05:07,131 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\patches\patch_20250529_230506.zip
2025-05-29 23:05:07,134 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\patches\patch_20250529_230506.zip
2025-05-29 23:05:07,143 - INFO - 检测到3个新增工单: [1006, 1007, 1008]
2025-05-29 23:05:07,143 - INFO - 检测到1个更新工单: [1002]
2025-05-29 23:05:07,143 - INFO - 增量模式测试: ✅ 通过
2025-05-29 23:05:07,143 - INFO -   详情: 基础版本: 20250529_230506, 新增: 3, 更新: 1
2025-05-29 23:05:07,144 - INFO - 测试环境设置完成: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31
2025-05-29 23:05:07,144 - INFO - 开始创建全量模式数据包...
2025-05-29 23:05:07,145 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\metadata.json
2025-05-29 23:05:07,146 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506\patch_content.json
2025-05-29 23:05:07,183 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,185 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\full_20250529_230506.zip
2025-05-29 23:05:07,185 - INFO - 开始测试Patch应用...
2025-05-29 23:05:07,186 - INFO - 解析Patch: patch_20250529_230506
2025-05-29 23:05:07,186 - INFO -   模式: incremental
2025-05-29 23:05:07,186 - INFO -   新增工单: 3
2025-05-29 23:05:07,186 - INFO -   更新工单: 1
2025-05-29 23:05:07,186 - INFO - 开始应用Patch到空目录...
2025-05-29 23:05:07,186 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\patches\patch_20250529_230506.zip
2025-05-29 23:05:07,186 - INFO - 添加新增工单: 1006
2025-05-29 23:05:07,186 - INFO - 添加新增工单: 1007
2025-05-29 23:05:07,187 - INFO - 添加新增工单: 1008
2025-05-29 23:05:07,187 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\empty_result.json
2025-05-29 23:05:07,187 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\empty_result.json
2025-05-29 23:05:07,196 - INFO - 空目录应用提取了3个新增工单
2025-05-29 23:05:07,196 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:05:07,205 - INFO - 加载基础数据: 5 个工单
2025-05-29 23:05:07,205 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_1fies3bs\patches\patch_20250529_230506.zip
2025-05-29 23:05:07,207 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\merge_result.json
2025-05-29 23:05:07,207 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\merge_result.json
2025-05-29 23:05:07,216 - INFO - 数据合并后共有8个工单
2025-05-29 23:05:07,216 - INFO - 开始创建合并Patch...
2025-05-29 23:05:07,218 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\repacked.zip_temp\metadata.json
2025-05-29 23:05:07,220 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\repacked.zip_temp\patch_content.json
2025-05-29 23:05:07,236 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\repacked.zip
2025-05-29 23:05:07,236 - INFO - 合并Patch已创建: C:\Users\<USER>\AppData\Local\Temp\comprehensive_test_lywcyf31\repacked.zip
2025-05-29 23:05:07,237 - INFO - Patch应用测试: ✅ 通过
2025-05-29 23:05:07,237 - INFO -   详情: 解析1个Patch, 空目录提取3个工单, 合并后8个工单
2025-05-29 23:05:07,239 - INFO - 数据已保存到: comprehensive_test_report.json
2025-05-29 23:05:07,257 - INFO - ✅ 综合测试套件 - 成功 (0.68s)
2025-05-29 23:05:07,257 - INFO - ------------------------------------------------------------
2025-05-29 23:05:07,258 - INFO - 开始运行: 采集器基础测试
2025-05-29 23:05:07,258 - INFO - 脚本: test_collector.py
2025-05-29 23:05:07,720 - INFO - 已禁用代理
2025-05-29 23:05:07,721 - INFO - 工单 7514 提取到 1 个附件
2025-05-29 23:05:07,765 - INFO - 已禁用代理
2025-05-29 23:05:07,766 - INFO - 工单 5766 没有附件
2025-05-29 23:05:07,777 - INFO - 已禁用代理
2025-05-29 23:05:07,778 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:05:07,778 - INFO - WebDriver初始化成功
2025-05-29 23:05:07,780 - INFO - 已禁用代理
2025-05-29 23:05:07,780 - INFO - 工单 7514 提取到 2 条评论
2025-05-29 23:05:07,798 - INFO - 已禁用代理
2025-05-29 23:05:07,802 - INFO - 工单 5766 提取到 4 条评论
2025-05-29 23:05:07,812 - INFO - 已禁用代理
2025-05-29 23:05:07,814 - INFO - 已禁用代理
2025-05-29 23:05:07,814 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4wofeou7\\last_update.json'
2025-05-29 23:05:07,814 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-29 23:05:07,815 - INFO - WebDriver初始化成功
2025-05-29 23:05:07,816 - INFO - 已禁用代理
2025-05-29 23:05:07,816 - INFO - 工单 123 提取到 1 条评论
2025-05-29 23:05:07,828 - INFO - 已禁用代理
2025-05-29 23:05:07,831 - INFO - 工单 5766 提取到 4 条评论
2025-05-29 23:05:07,832 - INFO - 已禁用代理
2025-05-29 23:05:07,833 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-29 23:05:07,872 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-29 23:05:07,872 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-29 23:05:07,874 - INFO - 已禁用代理
2025-05-29 23:05:07,875 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpcdx8m0jp\test.json
2025-05-29 23:05:07,885 - INFO - 已禁用代理
2025-05-29 23:05:07,885 - INFO - Session headers和cookies已更新
2025-05-29 23:05:07,910 - ERROR - ❌ 采集器基础测试 - 失败 (0.65s)
2025-05-29 23:05:07,910 - ERROR - 错误输出: test_attachment_extraction (__main__.TestCaseCollector)
测试附件提取 ... 2025-05-29 23:05:07,720 - INFO - 已禁用代理
2025-05-29 23:05:07,721 - INFO - 工单 7514 提取到 1 个附件
ok
test_attachment_extraction_with_real_data (__main__.TestCaseCollector)
使用真实数据测试附件提取 ... 2025-05-29 23:05:07,765 - INFO - 已禁用代理
2025-05-29 23:05:07,766 - INFO - 工单 5766 没有附件
ok
test_case_detail_to_dict (__main__.TestCaseCollector)
测试工单详情转换为字典 ... ok
test_case_summary_creation (__main__.TestCaseCollector)
测试工单概要创建 ... ok
test_case_summary_with_real_data (__main__.TestCaseCollector)
使用真实数据测试工单概要 ... ok
test_collector_initialization (__main__.TestCaseCollector)
测试采集器初始化 ... 2025-05-29 23:05:07,777 - INFO - 已禁用代理
2025-05-29 23:05:07,778 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:05:07,778 - INFO - WebDriver初始化成功
ok
test_comment_extraction (__main__.TestCaseCollector)
测试评论提取 ... 2025-05-29 23:05:07,780 - INFO - 已禁用代理
2025-05-29 23:05:07,780 - INFO - 工单 7514 提取到 2 条评论
ok
test_comment_extraction_with_real_data (__main__.TestCaseCollector)
使用真实数据测试评论提取 ... 2025-05-29 23:05:07,798 - INFO - 已禁用代理
2025-05-29 23:05:07,802 - INFO - 工单 5766 提取到 4 条评论
ok
test_complete_case_detail_with_real_data (__main__.TestCaseCollector)
使用真实数据测试完整工单详情 ... ok
test_csrf_token_extraction (__main__.TestCaseCollector)
测试CSRF token提取功能 ... 2025-05-29 23:05:07,812 - INFO - 已禁用代理
ok
test_custom_driver_path (__main__.TestCaseCollector)
测试自定义ChromeDriver路径 ... 2025-05-29 23:05:07,814 - INFO - 已禁用代理
2025-05-29 23:05:07,814 - ERROR - 加载JSON文件失败: [Errno 2] No such file or directory: 'C:\\Users\\<USER>\\AppData\\Local\\Temp\\tmp4wofeou7\\last_update.json'
2025-05-29 23:05:07,814 - INFO - 使用指定的ChromeDriver路径: /custom/path/chromedriver
2025-05-29 23:05:07,815 - INFO - WebDriver初始化成功
ok
test_extract_comments_fallback_method (__main__.TestCaseCollector)
测试评论提取的降级方法 ... 2025-05-29 23:05:07,816 - INFO - 已禁用代理
2025-05-29 23:05:07,816 - INFO - 工单 123 提取到 1 条评论
ok
test_extract_comments_from_demo_html (__main__.TestCaseCollector)
测试从demo-content.html提取评论 ... 2025-05-29 23:05:07,828 - INFO - 已禁用代理
2025-05-29 23:05:07,831 - INFO - 工单 5766 提取到 4 条评论
ok
test_get_cases_via_requests_structure (__main__.TestCaseCollector)
测试requests方法的基本结构 ... 2025-05-29 23:05:07,832 - INFO - 已禁用代理
2025-05-29 23:05:07,833 - WARNING - 同步cookies失败: 'NoneType' object has no attribute 'get_cookies'
2025-05-29 23:05:07,872 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-29 23:05:07,872 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
FAIL
test_get_cases_via_selenium (__main__.TestCaseCollector)
测试通过Selenium获取工单数据 ... 2025-05-29 23:05:07,874 - INFO - 已禁用代理
ok
test_json_operations (__main__.TestCaseCollector)
测试JSON操作 ... 2025-05-29 23:05:07,875 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpcdx8m0jp\test.json
ok
test_safe_filename (__main__.TestCaseCollector)
测试安全文件名生成 ... ok
test_session_headers_update (__main__.TestCaseCollector)
测试session headers更新功能 ... 2025-05-29 23:05:07,885 - INFO - 已禁用代理
2025-05-29 23:05:07,885 - INFO - Session headers和cookies已更新
ok
test_versioned_filename (__main__.TestCaseCollector)
测试版本化文件名生成 ... ok

======================================================================
FAIL: test_get_cases_via_requests_structure (__main__.TestCaseCollector)
测试requests方法的基本结构
----------------------------------------------------------------------
Traceback (most recent call last):
  File "test_collector.py", line 256, in test_get_cases_via_requests_structure
    self.assertIn('code', result)
AssertionError: 'code' not found in {}

----------------------------------------------------------------------
Ran 19 tests in 0.167s

FAILED (failures=1)

2025-05-29 23:05:07,913 - INFO - ------------------------------------------------------------
2025-05-29 23:05:07,913 - INFO - 开始运行: 新功能测试
2025-05-29 23:05:07,913 - INFO - 脚本: test_new_features.py
2025-05-29 23:05:08,236 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp1l9q0eby\temp_20250529_230508\cases_list_page1_status5_20250529_230508.json
2025-05-29 23:05:08,237 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp1l9q0eby\temp_20250529_230508\case_detail_123_20250529_230508.json
2025-05-29 23:05:08,238 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp1l9q0eby\temp_20250529_230508\api_request_230508_237172.json
2025-05-29 23:05:08,268 - INFO - 原始数据已打包保存: C:\Users\<USER>\AppData\Local\Temp\tmp1l9q0eby\raw_data_20250529_230508.zip
2025-05-29 23:05:08,270 - INFO - 已禁用代理
2025-05-29 23:05:08,277 - INFO - 已禁用代理
2025-05-29 23:05:08,309 - INFO - ✅ 新功能测试 - 成功 (0.40s)
2025-05-29 23:05:08,309 - INFO - ------------------------------------------------------------
2025-05-29 23:05:08,309 - INFO - ================================================================================
2025-05-29 23:05:08,309 - INFO - 测试执行完成
2025-05-29 23:05:08,310 - INFO - ================================================================================
2025-05-29 23:05:08,310 - INFO - 总测试数: 5
2025-05-29 23:05:08,310 - INFO - 通过测试: 4
2025-05-29 23:05:08,310 - INFO - 失败测试: 1
2025-05-29 23:05:08,310 - INFO - 必需测试失败: 0
2025-05-29 23:05:08,310 - INFO - 总执行时间: 0:00:02.376414
2025-05-29 23:05:08,311 - INFO - 
详细测试结果:
2025-05-29 23:05:08,311 - INFO - ✅ 测试数据生成器 (0.25s)
2025-05-29 23:05:08,311 - INFO - ✅ 数据模式基础测试 (0.38s)
2025-05-29 23:05:08,311 - INFO - ✅ 综合测试套件 (0.68s)
2025-05-29 23:05:08,311 - INFO - ❌ 采集器基础测试 (0.65s)
2025-05-29 23:05:08,311 - INFO -    错误: test_attachment_extraction (__main__.TestCaseCollector)
测试附件提取 ... 2025-05-29 23:05:07,720 - INFO - 已禁用代理
2025-05-29 23:05:07,721 - INFO - 工单 7514 提取到 1 个附件
ok
test_attachment_extraction_with_real_dat...
2025-05-29 23:05:08,311 - INFO - ✅ 新功能测试 (0.40s)
2025-05-29 23:05:08,313 - INFO - 数据已保存到: all_tests_report.json
2025-05-29 23:05:08,313 - INFO - 
📄 详细测试报告已保存: all_tests_report.json
2025-05-29 23:05:08,313 - INFO - 
🎉 所有必需测试通过！系统功能正常
2025-05-29 23:07:21,548 - INFO - 开始生成完整测试数据集...
2025-05-29 23:07:21,548 - INFO - 生成基础数据集...
2025-05-29 23:07:21,549 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:07:21,593 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:07:21,596 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:07:21,596 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:07:21,598 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:07:21,599 - INFO - 数据已保存到: test_data_complete\base_dataset.json
2025-05-29 23:07:21,599 - INFO - 基础数据集已保存: test_data_complete\base_dataset.json
2025-05-29 23:07:21,599 - INFO - 生成增量数据集...
2025-05-29 23:07:21,600 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:07:21,601 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:07:21,603 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:07:21,605 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:07:21,606 - INFO - 数据已保存到: test_data_complete\incremental_dataset.json
2025-05-29 23:07:21,606 - INFO - 增量数据集已保存: test_data_complete\incremental_dataset.json
2025-05-29 23:07:21,607 - INFO - 数据已保存到: test_data_complete\attachment_manifest.json
2025-05-29 23:07:21,607 - INFO - 附件清单已保存: test_data_complete\attachment_manifest.json
2025-05-29 23:07:21,608 - INFO - 数据已保存到: test_data_complete\test_data_summary.json
2025-05-29 23:07:21,608 - INFO - 测试数据摘要已保存: test_data_complete\test_data_summary.json
2025-05-29 23:07:37,801 - INFO - 开始生成完整测试数据集...
2025-05-29 23:07:37,801 - INFO - 生成基础数据集...
2025-05-29 23:07:37,802 - INFO - 生成基础工单: 1001 - basic
2025-05-29 23:07:37,845 - INFO - 生成基础工单: 1002 - with_images
2025-05-29 23:07:37,849 - INFO - 生成基础工单: 1003 - complex
2025-05-29 23:07:37,849 - INFO - 生成基础工单: 1004 - basic
2025-05-29 23:07:37,851 - INFO - 生成基础工单: 1005 - with_images
2025-05-29 23:07:37,852 - INFO - 数据已保存到: test_data_complete\base_dataset.json
2025-05-29 23:07:37,852 - INFO - 基础数据集已保存: test_data_complete\base_dataset.json
2025-05-29 23:07:37,852 - INFO - 生成增量数据集...
2025-05-29 23:07:37,853 - INFO - 生成更新工单: 1002 - updated
2025-05-29 23:07:37,854 - INFO - 生成新增工单: 1006 - basic
2025-05-29 23:07:37,856 - INFO - 生成新增工单: 1007 - with_images
2025-05-29 23:07:37,858 - INFO - 生成新增工单: 1008 - complex
2025-05-29 23:07:37,859 - INFO - 数据已保存到: test_data_complete\incremental_dataset.json
2025-05-29 23:07:37,859 - INFO - 增量数据集已保存: test_data_complete\incremental_dataset.json
2025-05-29 23:07:37,861 - INFO - 数据已保存到: test_data_complete\attachment_manifest.json
2025-05-29 23:07:37,861 - INFO - 附件清单已保存: test_data_complete\attachment_manifest.json
2025-05-29 23:07:37,862 - INFO - 数据已保存到: test_data_complete\test_data_summary.json
2025-05-29 23:07:37,863 - INFO - 测试数据摘要已保存: test_data_complete\test_data_summary.json
2025-05-29 23:12:47,552 - INFO - 开始创建全量模式数据包...
2025-05-29 23:12:47,553 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247\metadata.json
2025-05-29 23:12:47,554 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247\patch_content.json
2025-05-29 23:12:47,555 - INFO - 全量模式数据目录已创建: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247
2025-05-29 23:12:47,562 - INFO - 开始创建全量模式数据包...
2025-05-29 23:12:47,563 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247\metadata.json
2025-05-29 23:12:47,564 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247\patch_content.json
2025-05-29 23:12:47,581 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247.zip
2025-05-29 23:12:47,582 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmp7vv3xcct\full_20250529_231247.zip
2025-05-29 23:12:47,594 - INFO - 开始创建全量模式数据包...
2025-05-29 23:12:47,595 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\full_20250529_231247\metadata.json
2025-05-29 23:12:47,596 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\full_20250529_231247\patch_content.json
2025-05-29 23:12:47,612 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\full_20250529_231247.zip
2025-05-29 23:12:47,613 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\full_20250529_231247.zip
2025-05-29 23:12:47,614 - INFO - 开始创建增量模式Patch...
2025-05-29 23:12:47,623 - INFO - 更新工单: 1002 - 基础工单2-更新
2025-05-29 23:12:47,623 - INFO - 新增工单: 1003 - 新增工单3
2025-05-29 23:12:47,624 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\patches\patch_20250529_231247\metadata.json
2025-05-29 23:12:47,625 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\patches\patch_20250529_231247\patch_content.json
2025-05-29 23:12:47,642 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\patches\patch_20250529_231247.zip
2025-05-29 23:12:47,643 - INFO - 增量Patch已创建: C:\Users\<USER>\AppData\Local\Temp\tmpk81t9wi9\patches\patch_20250529_231247.zip
2025-05-29 23:12:47,655 - INFO - 开始创建全量模式数据包...
2025-05-29 23:12:47,656 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\full_20250529_231247\metadata.json
2025-05-29 23:12:47,657 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\full_20250529_231247\patch_content.json
2025-05-29 23:12:47,672 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\full_20250529_231247.zip
2025-05-29 23:12:47,673 - INFO - 全量模式数据包已创建: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\full_20250529_231247.zip
2025-05-29 23:12:47,674 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\test_patch\metadata.json
2025-05-29 23:12:47,675 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\test_patch\patch_content.json
2025-05-29 23:12:47,689 - INFO - ZIP包创建成功: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\test_patch.zip
2025-05-29 23:12:47,699 - INFO - 开始应用Patch到空目录...
2025-05-29 23:12:47,699 - INFO - 处理Patch: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\test_patch.zip
2025-05-29 23:12:47,700 - INFO - 添加新增工单: 2003
2025-05-29 23:12:47,700 - INFO - 添加新增工单: 2004
2025-05-29 23:12:47,701 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\empty_result.json
2025-05-29 23:12:47,701 - INFO - 新增工单数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\empty_result.json
2025-05-29 23:12:47,710 - INFO - 开始应用Patch到现有数据...
2025-05-29 23:12:47,718 - INFO - 加载基础数据: 2 个工单
2025-05-29 23:12:47,719 - INFO - 应用Patch: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\test_patch.zip
2025-05-29 23:12:47,720 - INFO - 数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\merge_result.json
2025-05-29 23:12:47,720 - INFO - 合并数据已保存到: C:\Users\<USER>\AppData\Local\Temp\tmpos8mwrph\merge_result.json
2025-05-29 23:21:11,785 - INFO - 设置指定工单ID: [5766, 7514]
2025-05-29 23:21:11,785 - INFO - 启用全量模式
2025-05-29 23:21:11,786 - INFO - 启用ZIP打包
2025-05-29 23:21:11,786 - INFO - ============================================================
2025-05-29 23:21:11,786 - INFO - AkroCare工单采集程序启动
2025-05-29 23:21:11,786 - INFO - 登录邮箱: <EMAIL>
2025-05-29 23:21:11,786 - INFO - ChromeDriver: 自定义路径
2025-05-29 23:21:11,786 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:21:11,786 - INFO - API请求方式: requests
2025-05-29 23:21:11,786 - INFO - 代理设置: 禁用
2025-05-29 23:21:11,787 - INFO - 增量更新: 启用
2025-05-29 23:21:11,787 - INFO - 采集模式: 指定工单ID - [5766, 7514]
2025-05-29 23:21:11,787 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-29 23:21:11,787 - INFO - 原始数据: 禁用
2025-05-29 23:21:11,787 - INFO - 数据模式: 全量模式 - ZIP打包
2025-05-29 23:21:11,787 - INFO - 开始时间: 2025-05-29 23:21:11
2025-05-29 23:21:11,787 - INFO - ============================================================
2025-05-29 23:21:11,788 - INFO - 已禁用代理
2025-05-29 23:21:11,788 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:21:13,100 - INFO - WebDriver初始化成功
2025-05-29 23:21:13,100 - INFO - 开始登录...
2025-05-29 23:21:28,996 - INFO - 登录成功
2025-05-29 23:21:29,009 - INFO - Session headers和cookies已更新
2025-05-29 23:21:29,010 - INFO - 登录成功，已保存 2 个cookies
2025-05-29 23:21:29,010 - INFO - 导航到工单页面...
2025-05-29 23:21:32,186 - INFO - Session headers和cookies已更新
2025-05-29 23:21:32,187 - INFO - 成功导航到工单页面，session信息已更新
2025-05-29 23:21:32,187 - INFO - 开始采集工单数据...
2025-05-29 23:21:32,187 - INFO - 指定工单ID模式，从工单列表中筛选指定ID: [5766, 7514]
2025-05-29 23:21:32,187 - INFO - 开始从工单列表中查找指定工单ID: [5766, 7514]
2025-05-29 23:21:32,187 - INFO - 将在两种状态中搜索: Open(4), Closed(5)
2025-05-29 23:21:32,187 - INFO - 在状态 4 中搜索指定工单...
2025-05-29 23:21:32,188 - INFO - 搜索状态 4 第 1 页...
2025-05-29 23:21:32,205 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-29 23:21:32,205 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-29 23:21:32,205 - ERROR - API请求失败：无响应数据
2025-05-29 23:21:32,205 - INFO - 尝试使用Selenium方式重新请求...
2025-05-29 23:22:02,218 - ERROR - Selenium POST请求失败: Message: script timeout
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF65F90CF45+75717]
	GetHandleVerifier [0x00007FF65F90CFA0+75808]
	(No symbol) [0x00007FF65F6D8DCC]
	(No symbol) [0x00007FF65F780C69]
	(No symbol) [0x00007FF65F75737A]
	(No symbol) [0x00007FF65F77F39C]
	(No symbol) [0x00007FF65F757153]
	(No symbol) [0x00007FF65F720421]
	(No symbol) [0x00007FF65F7211B3]
	GetHandleVerifier [0x00007FF65FC0D71D+3223453]
	GetHandleVerifier [0x00007FF65FC07CC2+3200322]
	GetHandleVerifier [0x00007FF65FC25AF3+3322739]
	GetHandleVerifier [0x00007FF65F926A1A+180890]
	GetHandleVerifier [0x00007FF65F92E11F+211359]
	GetHandleVerifier [0x00007FF65F915294+109332]
	GetHandleVerifier [0x00007FF65F915442+109762]
	GetHandleVerifier [0x00007FF65F8FBA59+4825]
	BaseThreadInitThunk [0x00007FFB55787374+20]
	RtlUserThreadStart [0x00007FFB576FCC91+33]

2025-05-29 23:22:02,219 - INFO - 在状态 5 中搜索指定工单...
2025-05-29 23:22:02,219 - INFO - 搜索状态 5 第 1 页...
2025-05-29 23:22:02,232 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-29 23:22:02,233 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-29 23:22:02,233 - ERROR - API请求失败：无响应数据
2025-05-29 23:22:02,233 - INFO - 尝试使用Selenium方式重新请求...
2025-05-29 23:22:32,251 - ERROR - Selenium POST请求失败: Message: script timeout
  (Session info: chrome=136.0.7103.114)
Stacktrace:
	GetHandleVerifier [0x00007FF65F90CF45+75717]
	GetHandleVerifier [0x00007FF65F90CFA0+75808]
	(No symbol) [0x00007FF65F6D8DCC]
	(No symbol) [0x00007FF65F780C69]
	(No symbol) [0x00007FF65F75737A]
	(No symbol) [0x00007FF65F77F39C]
	(No symbol) [0x00007FF65F757153]
	(No symbol) [0x00007FF65F720421]
	(No symbol) [0x00007FF65F7211B3]
	GetHandleVerifier [0x00007FF65FC0D71D+3223453]
	GetHandleVerifier [0x00007FF65FC07CC2+3200322]
	GetHandleVerifier [0x00007FF65FC25AF3+3322739]
	GetHandleVerifier [0x00007FF65F926A1A+180890]
	GetHandleVerifier [0x00007FF65F92E11F+211359]
	GetHandleVerifier [0x00007FF65F915294+109332]
	GetHandleVerifier [0x00007FF65F915442+109762]
	GetHandleVerifier [0x00007FF65F8FBA59+4825]
	BaseThreadInitThunk [0x00007FFB55787374+20]
	RtlUserThreadStart [0x00007FFB576FCC91+33]

2025-05-29 23:22:32,252 - WARNING - 未找到以下指定工单ID: [7514, 5766]
2025-05-29 23:22:32,252 - INFO - 从工单列表中找到 0 个指定工单
2025-05-29 23:22:32,252 - WARNING - 没有获取到工单概要信息
2025-05-29 23:22:32,253 - WARNING - 没有采集到工单数据
2025-05-29 23:22:34,377 - INFO - 资源已释放
2025-05-29 23:23:01,891 - INFO - 设置采集最新 10 条工单
2025-05-29 23:23:01,891 - INFO - 启用全量模式
2025-05-29 23:23:01,891 - INFO - 启用ZIP打包
2025-05-29 23:23:01,892 - INFO - ============================================================
2025-05-29 23:23:01,892 - INFO - AkroCare工单采集程序启动
2025-05-29 23:23:01,892 - INFO - 登录邮箱: <EMAIL>
2025-05-29 23:23:01,892 - INFO - ChromeDriver: 自定义路径
2025-05-29 23:23:01,892 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:23:01,892 - INFO - API请求方式: requests
2025-05-29 23:23:01,892 - INFO - 代理设置: 禁用
2025-05-29 23:23:01,892 - INFO - 增量更新: 禁用
2025-05-29 23:23:01,893 - INFO - 采集模式: 最新 10 条工单
2025-05-29 23:23:01,893 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-29 23:23:01,893 - INFO - 原始数据: 禁用
2025-05-29 23:23:01,893 - INFO - 数据模式: 全量模式 - ZIP打包
2025-05-29 23:23:01,893 - INFO - 开始时间: 2025-05-29 23:23:01
2025-05-29 23:23:01,893 - INFO - ============================================================
2025-05-29 23:23:01,893 - INFO - 已禁用代理
2025-05-29 23:23:01,894 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:23:03,017 - INFO - WebDriver初始化成功
2025-05-29 23:23:03,017 - INFO - 开始登录...
2025-05-29 23:23:17,490 - INFO - 登录成功
2025-05-29 23:23:17,503 - INFO - Session headers和cookies已更新
2025-05-29 23:23:17,503 - INFO - 登录成功，已保存 2 个cookies
2025-05-29 23:23:17,504 - INFO - 导航到工单页面...
2025-05-29 23:23:20,655 - INFO - Session headers和cookies已更新
2025-05-29 23:23:20,656 - INFO - 成功导航到工单页面，session信息已更新
2025-05-29 23:23:20,657 - INFO - 开始采集工单数据...
2025-05-29 23:23:20,657 - INFO - 将采集两种状态的工单: Open(4), Closed(5)
2025-05-29 23:23:20,658 - INFO - 开始采集状态为 4 的工单...
2025-05-29 23:23:20,658 - INFO - 正在获取状态 4 第 1 页工单数据...
2025-05-29 23:23:20,723 - ERROR - Requests POST请求失败: HTTPSConnectionPool(host='akrocare.akrostar-tech.com', port=443): Max retries exceeded with url: /api/get_user_cases (Caused by ProxyError('Unable to connect to proxy', OSError(0, 'Error')))
2025-05-29 23:23:20,724 - ERROR - 请求URL: https://akrocare.akrostar-tech.com/api/get_user_cases
2025-05-29 23:23:20,724 - ERROR - API请求失败：无响应数据
2025-05-29 23:23:20,724 - INFO - 尝试使用Selenium方式重新请求...
2025-05-29 23:23:34,821 - INFO - 用户中断程序
2025-05-29 23:23:39,244 - INFO - 设置采集最新 10 条工单
2025-05-29 23:23:39,245 - INFO - 启用全量模式
2025-05-29 23:23:39,245 - INFO - 启用ZIP打包
2025-05-29 23:23:39,245 - INFO - ============================================================
2025-05-29 23:23:39,245 - INFO - AkroCare工单采集程序启动
2025-05-29 23:23:39,245 - INFO - 登录邮箱: <EMAIL>
2025-05-29 23:23:39,245 - INFO - ChromeDriver: 自定义路径
2025-05-29 23:23:39,246 - INFO - Driver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:23:39,246 - INFO - API请求方式: requests
2025-05-29 23:23:39,246 - INFO - 代理设置: 禁用
2025-05-29 23:23:39,246 - INFO - 增量更新: 禁用
2025-05-29 23:23:39,246 - INFO - 采集模式: 最新 10 条工单
2025-05-29 23:23:39,246 - INFO - 工单状态: Open(4) + Closed(5)
2025-05-29 23:23:39,246 - INFO - 原始数据: 禁用
2025-05-29 23:23:39,246 - INFO - 数据模式: 全量模式 - ZIP打包
2025-05-29 23:23:39,247 - INFO - 开始时间: 2025-05-29 23:23:39
2025-05-29 23:23:39,247 - INFO - ============================================================
2025-05-29 23:23:39,247 - INFO - 已禁用代理
2025-05-29 23:23:39,248 - INFO - 使用指定的ChromeDriver路径: C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe
2025-05-29 23:23:40,423 - INFO - WebDriver初始化成功
2025-05-29 23:23:40,423 - INFO - 开始登录...
2025-05-29 23:23:54,082 - INFO - 登录成功
2025-05-29 23:23:54,094 - INFO - Session headers和cookies已更新
2025-05-29 23:23:54,095 - INFO - 登录成功，已保存 2 个cookies
2025-05-29 23:23:54,095 - INFO - 导航到工单页面...
2025-05-29 23:23:57,225 - INFO - Session headers和cookies已更新
2025-05-29 23:23:57,225 - INFO - 成功导航到工单页面，session信息已更新
2025-05-29 23:23:57,226 - INFO - 开始采集工单数据...
2025-05-29 23:23:57,226 - INFO - 将采集两种状态的工单: Open(4), Closed(5)
2025-05-29 23:23:57,226 - INFO - 开始采集状态为 4 的工单...
2025-05-29 23:23:57,227 - INFO - 正在获取状态 4 第 1 页工单数据...
2025-05-29 23:23:57,603 - INFO - 达到采集限制，停止采集
2025-05-29 23:23:57,603 - INFO - 正在处理第 1/10 个工单: 7693
2025-05-29 23:23:57,603 - INFO - ================================================================================
2025-05-29 23:23:57,603 - INFO - 正在获取工单 7693 的详细信息...
2025-05-29 23:23:57,603 - INFO - 产品线: MP20
2025-05-29 23:23:57,603 - INFO - 工单标题: MP20 PMA CLAMP与core数字区域是否可以共用的问题
2025-05-29 23:23:57,604 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:01,904 - INFO - 工单 7693 提取到 1 条评论
2025-05-29 23:24:01,905 - INFO - 工单 7693 没有附件
2025-05-29 23:24:03,120 - INFO - 文件下载成功: data\attachments\7693\images\1748424926165245.png
2025-05-29 23:24:03,122 - INFO - 图片下载成功: 1748424926165245.png
2025-05-29 23:24:03,122 - INFO - 工单 7693 详细信息采集完成
2025-05-29 23:24:03,122 - INFO - ================================================================================
2025-05-29 23:24:05,136 - INFO - 正在处理第 2/10 个工单: 7688
2025-05-29 23:24:05,136 - INFO - ================================================================================
2025-05-29 23:24:05,136 - INFO - 正在获取工单 7688 的详细信息...
2025-05-29 23:24:05,136 - INFO - 产品线: MP32
2025-05-29 23:24:05,136 - INFO - 工单标题: MP32 连续校准 流程
2025-05-29 23:24:05,136 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:08,354 - INFO - 工单 7688 提取到 1 条评论
2025-05-29 23:24:08,355 - INFO - 工单 7688 没有附件
2025-05-29 23:24:08,355 - INFO - 工单 7688 没有附件或图片，跳过目录创建
2025-05-29 23:24:08,356 - INFO - 工单 7688 详细信息采集完成
2025-05-29 23:24:08,356 - INFO - ================================================================================
2025-05-29 23:24:10,360 - INFO - 正在处理第 3/10 个工单: 7687
2025-05-29 23:24:10,361 - INFO - ================================================================================
2025-05-29 23:24:10,361 - INFO - 正在获取工单 7687 的详细信息...
2025-05-29 23:24:10,362 - INFO - 产品线: MP32
2025-05-29 23:24:10,362 - INFO - 工单标题: MP32眼图扫描工具
2025-05-29 23:24:10,363 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:13,586 - INFO - 工单 7687 提取到 1 条评论
2025-05-29 23:24:13,588 - INFO - 工单 7687 没有附件
2025-05-29 23:24:13,603 - INFO - 工单 7687 没有附件或图片，跳过目录创建
2025-05-29 23:24:13,603 - INFO - 工单 7687 详细信息采集完成
2025-05-29 23:24:13,603 - INFO - ================================================================================
2025-05-29 23:24:15,608 - INFO - 正在处理第 4/10 个工单: 7647
2025-05-29 23:24:15,608 - INFO - ================================================================================
2025-05-29 23:24:15,608 - INFO - 正在获取工单 7647 的详细信息...
2025-05-29 23:24:15,608 - INFO - 产品线: MP20
2025-05-29 23:24:15,608 - INFO - 工单标题: mp20可靠性测试报告
2025-05-29 23:24:15,609 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:18,811 - INFO - 工单 7647 提取到 3 条评论
2025-05-29 23:24:18,812 - INFO - 工单 7647 没有附件
2025-05-29 23:24:18,812 - INFO - 工单 7647 没有附件或图片，跳过目录创建
2025-05-29 23:24:18,813 - INFO - 工单 7647 详细信息采集完成
2025-05-29 23:24:18,813 - INFO - ================================================================================
2025-05-29 23:24:20,827 - INFO - 正在处理第 5/10 个工单: 7514
2025-05-29 23:24:20,827 - INFO - ================================================================================
2025-05-29 23:24:20,827 - INFO - 正在获取工单 7514 的详细信息...
2025-05-29 23:24:20,827 - INFO - 产品线: MP32
2025-05-29 23:24:20,828 - INFO - 工单标题: MP32 指标说明
2025-05-29 23:24:20,828 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:24,124 - INFO - 工单 7514 提取到 2 条评论
2025-05-29 23:24:24,126 - INFO - 工单 7514 没有附件
2025-05-29 23:24:24,129 - INFO - 工单 7514 详细信息采集完成
2025-05-29 23:24:24,130 - INFO - ================================================================================
2025-05-29 23:24:26,138 - INFO - 正在处理第 6/10 个工单: 6867
2025-05-29 23:24:26,138 - INFO - ================================================================================
2025-05-29 23:24:26,138 - INFO - 正在获取工单 6867 的详细信息...
2025-05-29 23:24:26,138 - INFO - 产品线: MP32
2025-05-29 23:24:26,139 - INFO - 工单标题: serdes PI 仿真问题交流
2025-05-29 23:24:26,139 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:29,423 - INFO - 工单 6867 提取到 7 条评论
2025-05-29 23:24:29,425 - INFO - 工单 6867 没有附件
2025-05-29 23:24:29,425 - INFO - 工单 6867 没有附件或图片，跳过目录创建
2025-05-29 23:24:29,426 - INFO - 工单 6867 详细信息采集完成
2025-05-29 23:24:29,426 - INFO - ================================================================================
2025-05-29 23:24:31,438 - INFO - 正在处理第 7/10 个工单: 5829
2025-05-29 23:24:31,438 - INFO - ================================================================================
2025-05-29 23:24:31,438 - INFO - 正在获取工单 5829 的详细信息...
2025-05-29 23:24:31,438 - INFO - 产品线: MP32
2025-05-29 23:24:31,439 - INFO - 工单标题: MP32光口能力评估
2025-05-29 23:24:31,439 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:34,669 - INFO - 工单 5829 提取到 10 条评论
2025-05-29 23:24:34,670 - INFO - 工单 5829 提取到 0 个附件
2025-05-29 23:24:34,671 - INFO - 工单 5829 没有附件或图片，跳过目录创建
2025-05-29 23:24:34,672 - INFO - 工单 5829 详细信息采集完成
2025-05-29 23:24:34,672 - INFO - ================================================================================
2025-05-29 23:24:36,679 - INFO - 正在处理第 8/10 个工单: 5741
2025-05-29 23:24:36,679 - INFO - ================================================================================
2025-05-29 23:24:36,679 - INFO - 正在获取工单 5741 的详细信息...
2025-05-29 23:24:36,679 - INFO - 产品线: DDR4/3
2025-05-29 23:24:36,680 - INFO - 工单标题: 关于DDR的外边输入时钟Jitter指标仿真问题（01730706707追问）
2025-05-29 23:24:36,680 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:39,966 - INFO - 工单 5741 提取到 10 条评论
2025-05-29 23:24:39,968 - INFO - 工单 5741 没有附件
2025-05-29 23:24:39,969 - INFO - 工单 5741 没有附件或图片，跳过目录创建
2025-05-29 23:24:39,969 - INFO - 工单 5741 详细信息采集完成
2025-05-29 23:24:39,969 - INFO - ================================================================================
2025-05-29 23:24:41,975 - INFO - 正在处理第 9/10 个工单: 5704
2025-05-29 23:24:41,975 - INFO - ================================================================================
2025-05-29 23:24:41,975 - INFO - 正在获取工单 5704 的详细信息...
2025-05-29 23:24:41,975 - INFO - 产品线: MP32
2025-05-29 23:24:41,976 - INFO - 工单标题: MP32的EM仿真报告在IP的交付数据包里没有找到，请帮忙提供一份谢谢！
2025-05-29 23:24:41,976 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:45,187 - INFO - 工单 5704 提取到 7 条评论
2025-05-29 23:24:45,188 - INFO - 工单 5704 没有附件
2025-05-29 23:24:45,189 - INFO - 工单 5704 没有附件或图片，跳过目录创建
2025-05-29 23:24:45,189 - INFO - 工单 5704 详细信息采集完成
2025-05-29 23:24:45,189 - INFO - ================================================================================
2025-05-29 23:24:47,190 - INFO - 正在处理第 10/10 个工单: 5703
2025-05-29 23:24:47,190 - INFO - ================================================================================
2025-05-29 23:24:47,190 - INFO - 正在获取工单 5703 的详细信息...
2025-05-29 23:24:47,190 - INFO - 产品线: MP20
2025-05-29 23:24:47,191 - INFO - 工单标题: MP20的EM仿真报告在IP的交付数据包里没有找到，请帮忙提供一份谢谢！
2025-05-29 23:24:47,191 - INFO - --------------------------------------------------------------------------------
2025-05-29 23:24:50,516 - INFO - 工单 5703 提取到 8 条评论
2025-05-29 23:24:50,518 - INFO - 工单 5703 没有附件
2025-05-29 23:24:50,518 - INFO - 工单 5703 没有附件或图片，跳过目录创建
2025-05-29 23:24:50,518 - INFO - 工单 5703 详细信息采集完成
2025-05-29 23:24:50,519 - INFO - ================================================================================
2025-05-29 23:24:52,532 - INFO - 数据已保存到: data\last_update.json
2025-05-29 23:24:52,540 - INFO - 开始创建全量模式数据包...
2025-05-29 23:24:52,545 - INFO - 数据已保存到: data\full_20250529_232452\metadata.json
2025-05-29 23:24:52,557 - INFO - 数据已保存到: data\full_20250529_232452\patch_content.json
2025-05-29 23:24:52,598 - INFO - ZIP包创建成功: data\full_20250529_232452.zip
2025-05-29 23:24:52,600 - INFO - 全量模式数据包已创建: data\full_20250529_232452.zip
2025-05-29 23:24:52,600 - INFO - ============================================================
2025-05-29 23:24:52,600 - INFO - 全量模式采集完成！共采集 10 条工单
2025-05-29 23:24:52,601 - INFO - 数据包保存位置: data\full_20250529_232452.zip
2025-05-29 23:24:52,601 - INFO - 附件保存位置: data\attachments
2025-05-29 23:24:52,601 - INFO - 结束时间: 2025-05-29 23:24:52
2025-05-29 23:24:52,601 - INFO - ============================================================
2025-05-29 23:24:54,742 - INFO - 资源已释放
