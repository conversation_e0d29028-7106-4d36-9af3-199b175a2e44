#!/usr/bin/env python3
"""
验证测试数据集的完整性和正确性
"""
import os
import json
import sys
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils import logger


def validate_file_exists(file_path, description):
    """验证文件是否存在"""
    if os.path.exists(file_path):
        file_size = os.path.getsize(file_path)
        print(f"✅ {description}: {file_path} ({file_size} bytes)")
        return True
    else:
        print(f"❌ {description}: {file_path} (文件不存在)")
        return False


def validate_json_structure(file_path, expected_keys, description):
    """验证JSON文件结构"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if isinstance(data, list):
            if len(data) > 0:
                # 验证列表中第一个元素的键
                missing_keys = [key for key in expected_keys if key not in data[0]]
                if missing_keys:
                    print(f"❌ {description}: 缺少键 {missing_keys}")
                    return False
                else:
                    print(f"✅ {description}: 结构正确 (包含 {len(data)} 个项目)")
                    return True
            else:
                print(f"✅ {description}: 空列表 (正常)")
                return True
        else:
            # 验证字典的键
            missing_keys = [key for key in expected_keys if key not in data]
            if missing_keys:
                print(f"❌ {description}: 缺少键 {missing_keys}")
                return False
            else:
                print(f"✅ {description}: 结构正确")
                return True
                
    except json.JSONDecodeError as e:
        print(f"❌ {description}: JSON格式错误 - {e}")
        return False
    except Exception as e:
        print(f"❌ {description}: 读取失败 - {e}")
        return False


def validate_comments_data():
    """验证评论数据"""
    print("\n📝 验证评论数据:")
    file_path = "test_data/demo_comments.json"
    
    if not validate_file_exists(file_path, "评论数据文件"):
        return False
    
    expected_keys = ['source', 'content', 'timestamp']
    if not validate_json_structure(file_path, expected_keys, "评论数据结构"):
        return False
    
    # 验证具体内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            comments = json.load(f)
        
        print(f"   评论总数: {len(comments)}")
        
        for i, comment in enumerate(comments, 1):
            print(f"   评论{i}: {comment['source']} - {comment['timestamp']}")
            
            # 验证时间戳格式
            try:
                datetime.strptime(comment['timestamp'], '%Y-%m-%d %H:%M:%S')
                print(f"     ✅ 时间戳格式正确")
            except ValueError:
                print(f"     ❌ 时间戳格式错误: {comment['timestamp']}")
                return False
            
            # 验证内容不为空
            if not comment['content'].strip():
                print(f"     ❌ 评论内容为空")
                return False
            else:
                print(f"     ✅ 内容长度: {len(comment['content'])} 字符")
        
        return True
        
    except Exception as e:
        print(f"❌ 评论数据验证失败: {e}")
        return False


def validate_case_summary_data():
    """验证工单概要数据"""
    print("\n📋 验证工单概要数据:")
    file_path = "test_data/demo_case_summary.json"
    
    if not validate_file_exists(file_path, "工单概要文件"):
        return False
    
    expected_keys = [
        'id', 'case_number', 'subject', 'product_line', 'product_name',
        'project_name', 'created_at', 'updated_at', 'username', 'company_name'
    ]
    
    if not validate_json_structure(file_path, expected_keys, "工单概要结构"):
        return False
    
    # 验证具体内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            summary = json.load(f)
        
        print(f"   工单ID: {summary['id']}")
        print(f"   工单号: {summary['case_number']}")
        print(f"   主题: {summary['subject']}")
        print(f"   产品线: {summary['product_line']}")
        print(f"   用户: {summary['username']}")
        print(f"   公司: {summary['company_name']}")
        
        # 验证ID是数字
        if not isinstance(summary['id'], int):
            print(f"❌ 工单ID应为整数: {summary['id']}")
            return False
        
        # 验证时间格式
        for time_field in ['created_at', 'updated_at']:
            try:
                datetime.strptime(summary[time_field], '%Y-%m-%d %H:%M:%S')
                print(f"   ✅ {time_field}: {summary[time_field]}")
            except ValueError:
                print(f"   ❌ {time_field}格式错误: {summary[time_field]}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 工单概要验证失败: {e}")
        return False


def validate_attachments_data():
    """验证附件数据"""
    print("\n📎 验证附件数据:")
    file_path = "test_data/demo_attachments.json"
    
    if not validate_file_exists(file_path, "附件数据文件"):
        return False
    
    expected_keys = ['attachment_id', 'file_name', 'file_size', 'owner', 'last_modified', 'download_url']
    if not validate_json_structure(file_path, expected_keys, "附件数据结构"):
        return False
    
    # 验证具体内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            attachments = json.load(f)
        
        print(f"   附件总数: {len(attachments)}")
        
        if len(attachments) == 0:
            print("   ✅ 无附件 (符合demo数据)")
            return True
        
        for i, attachment in enumerate(attachments, 1):
            print(f"   附件{i}: {attachment['file_name']} ({attachment['file_size']})")
            print(f"     所有者: {attachment['owner']}")
            print(f"     修改时间: {attachment['last_modified']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 附件数据验证失败: {e}")
        return False


def validate_complete_case_data():
    """验证完整工单数据"""
    print("\n📄 验证完整工单数据:")
    file_path = "test_data/demo_case_detail.json"
    
    if not validate_file_exists(file_path, "完整工单数据文件"):
        return False
    
    expected_keys = ['summary', 'comments', 'attachments']
    if not validate_json_structure(file_path, expected_keys, "完整工单数据结构"):
        return False
    
    # 验证具体内容
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            case_data = json.load(f)
        
        summary = case_data['summary']
        comments = case_data['comments']
        attachments = case_data['attachments']
        
        print(f"   工单概要: ID {summary['id']} - {summary['subject']}")
        print(f"   评论数量: {len(comments)}")
        print(f"   附件数量: {len(attachments)}")
        
        # 验证数据一致性
        with open('test_data/demo_comments.json', 'r', encoding='utf-8') as f:
            standalone_comments = json.load(f)
        
        if len(comments) != len(standalone_comments):
            print(f"❌ 评论数量不一致: 完整数据{len(comments)} vs 独立数据{len(standalone_comments)}")
            return False
        
        print("   ✅ 数据一致性检查通过")
        return True
        
    except Exception as e:
        print(f"❌ 完整工单数据验证失败: {e}")
        return False


def validate_demo_html_file():
    """验证demo HTML文件"""
    print("\n🌐 验证demo HTML文件:")
    file_path = "demo-content.html"
    
    if not validate_file_exists(file_path, "demo HTML文件"):
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        print(f"   文件大小: {len(html_content)} 字符")
        
        # 检查关键HTML元素
        key_elements = [
            'Reply Content',
            'col-md-2',
            'col-md-10',
            'well',
            'Attachments'
        ]
        
        for element in key_elements:
            if element in html_content:
                print(f"   ✅ 包含关键元素: {element}")
            else:
                print(f"   ❌ 缺少关键元素: {element}")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ demo HTML文件验证失败: {e}")
        return False


def main():
    """主函数"""
    print("=" * 60)
    print("测试数据集验证程序")
    print("=" * 60)
    
    validation_results = []
    
    # 验证各个数据文件
    validation_results.append(validate_demo_html_file())
    validation_results.append(validate_comments_data())
    validation_results.append(validate_case_summary_data())
    validation_results.append(validate_attachments_data())
    validation_results.append(validate_complete_case_data())
    
    # 总结验证结果
    print("\n" + "=" * 60)
    print("验证结果总结")
    print("=" * 60)
    
    passed = sum(validation_results)
    total = len(validation_results)
    
    if passed == total:
        print(f"🎉 所有验证通过！({passed}/{total})")
        print("\n✅ 测试数据集完整且正确")
        print("✅ 可以安全地用于单元测试")
        print("✅ 数据结构符合预期")
        print("✅ 时间戳格式正确")
        print("✅ 数据一致性良好")
    else:
        print(f"❌ 部分验证失败 ({passed}/{total})")
        print("需要修复数据问题后重新验证")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
