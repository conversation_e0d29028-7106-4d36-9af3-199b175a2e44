"""
工具函数
"""
import os
import json
import time
import requests
from datetime import datetime
from typing import Dict, Any, Optional
from urllib.parse import urlparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('case_collector.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


def save_json(data: Any, file_path: str) -> None:
    """保存数据为JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"数据已保存到: {file_path}")
    except Exception as e:
        logger.error(f"保存JSON文件失败: {e}")
        raise


def load_json(file_path: str) -> Optional[Dict[str, Any]]:
    """加载JSON文件"""
    try:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return None
    except Exception as e:
        logger.error(f"加载JSON文件失败: {e}")
        return None


def download_file(url: str, local_path: str, cookies: Optional[Dict] = None,
                 headers: Optional[Dict] = None) -> bool:
    """下载文件到本地"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(local_path), exist_ok=True)

        # 根据配置决定是否禁用代理
        proxies = None
        try:
            import config
            if config.DISABLE_PROXY:
                proxies = {
                    'http': None,
                    'https': None
                }
        except:
            # 如果无法导入config，默认禁用代理
            proxies = {
                'http': None,
                'https': None
            }

        # 发起下载请求
        response = requests.get(
            url,
            cookies=cookies,
            headers=headers,
            stream=True,
            proxies=proxies,
            timeout=30
        )
        response.raise_for_status()

        # 写入文件
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        logger.info(f"文件下载成功: {local_path}")
        return True
    except Exception as e:
        logger.error(f"文件下载失败 {url}: {e}")
        return False


def get_safe_filename(filename: str) -> str:
    """获取安全的文件名（移除非法字符）"""
    import re
    # 移除或替换非法字符
    safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
    return safe_name


def generate_versioned_filename(base_path: str, filename: str, version: int = 1) -> str:
    """生成带版本号的文件名"""
    name, ext = os.path.splitext(filename)
    if version == 1:
        return os.path.join(base_path, filename)
    else:
        versioned_name = f"{name}_v{version}{ext}"
        return os.path.join(base_path, versioned_name)


def parse_datetime(date_string: str) -> datetime:
    """解析日期时间字符串"""
    try:
        # 尝试多种日期格式
        formats = [
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d',
            '%Y/%m/%d %H:%M:%S',
            '%Y/%m/%d'
        ]

        for fmt in formats:
            try:
                return datetime.strptime(date_string, fmt)
            except ValueError:
                continue

        # 如果都失败了，返回当前时间
        logger.warning(f"无法解析日期格式: {date_string}")
        return datetime.now()
    except Exception as e:
        logger.error(f"日期解析错误: {e}")
        return datetime.now()


def retry_on_failure(max_retries: int = 3, delay: float = 2.0):
    """重试装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries - 1:
                        logger.error(f"函数 {func.__name__} 执行失败，已重试 {max_retries} 次: {e}")
                        raise
                    else:
                        logger.warning(f"函数 {func.__name__} 执行失败，第 {attempt + 1} 次重试: {e}")
                        time.sleep(delay)
            return None
        return wrapper
    return decorator


def clean_html_content(html_content: str) -> str:
    """清理HTML内容，提取纯文本"""
    from bs4 import BeautifulSoup
    try:
        soup = BeautifulSoup(html_content, 'html.parser')
        return soup.get_text(strip=True)
    except Exception as e:
        logger.error(f"HTML内容清理失败: {e}")
        return html_content


def format_file_size(size_str: str) -> str:
    """格式化文件大小"""
    try:
        # 如果已经是格式化的字符串，直接返回
        if any(unit in size_str.upper() for unit in ['B', 'KB', 'MB', 'GB']):
            return size_str

        # 尝试转换为数字并格式化
        size = int(size_str)
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size < 1024:
                return f"{size:.1f} {unit}"
            size /= 1024
        return f"{size:.1f} TB"
    except:
        return size_str
