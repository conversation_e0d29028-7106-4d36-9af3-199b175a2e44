
<style>
    body{height: 100vh;}

    body, div, dl, dt, dd, ul, ol, li, h1, h2, h3, h4, h5, h6, pre, form, fieldset, input, p, blockquote, th, td {
        margin:0; padding:0;
        list-style: none;
    }
    body{background-color: #e3e3e3;margin: 0px;}
    #shuttle_box{width:800px;zoom: 1;margin: 10px 10px;}
    #shuttle_box:after{
        content: ".";
        clear: both;
        display: block;
        height: 0;
        overflow: hidden;
        visibility: hidden;
    }
    .shuttle_box_li{height: 540px;float: left;}
    .shuttle_box_near{width:300px;background-color:#ffffff;overflow-y: scroll;overflow-x:hidden;border-radius: 10px;}
    .shuttle_box_li_act{color:#ffffff !important;background-color: #1890ff !important;border-bottom: 1px solid #ffffff;transition: all .01s;}
    .shuttle_box_near::-webkit-scrollbar {/*滚动条整体样式*/
        width: 6px;     /*高宽分别对应横竖滚动条的尺寸*/
        height: 1px;
    }
    .shuttle_box_near::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
        border-radius: 20px;
        background-color: rgba(0,0,0,0.5);
    }
    .shuttle_box_near::-webkit-scrollbar-track {/*滚动条里面轨道*/
        background-color: rgba(0,0,0,0.2);
        border-radius: 20px;
    }
    .shuttle_box_near li{
        padding:8px;
        border-bottom: 1px solid #ffffff;
        background-color: #f4f4f4;
        cursor: pointer;
        transition: all .5s;
    }
    .shuttle_box_li_act:hover{opacity: 0.7;transition: all .01s;}
    #shuttle_box_mid{width:80px;text-align: center;}
    #shuttle_box_mid button{
        width: 50px;
        height:30px;
        display: block;
        margin:20px auto;
        line-height: 30px;
        color:white;
        cursor: pointer;
        background-color: #1890ff;
        border-radius: 5px;
        transition: all .5s;
        border:none;
    }
    #shuttle_box_mid button:hover{opacity: 0.7;transition: all .5s;}
    #shuttle_box_toRight{margin-top:225px !important;}

    /*.form-group {*/
    /*    display: inline-block; !* 使 .form-group 成为行内块元素，便于 vertical-align *!*/
    /*}*/

    /*.form-group label,*/
    /*.form-group div {*/
    /*    vertical-align: middle; !* 在垂直方向上对齐 *!*/
    /*}*/

    /*.form-group div p {*/
    /*    vertical-align: middle; !* 如果有输入框等内部元素，也需要设置 vertical-align *!*/
    /*}*/

</style>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF Token -->
    <meta name="csrf-token" content="****************************************">

    <link rel="Bookmark" href="/favicon.ico" >
    <link rel="Shortcut Icon" href="/favicon.ico" />
    <title>AkroStar</title>

    <!-- Styles -->


    <link href="https://akrocare.akrostar-tech.com/vendor/bootstrap/css/bootstrap.css" rel="stylesheet">

    <link href="https://akrocare.akrostar-tech.com/css/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link href="https://akrocare.akrostar-tech.com/css/bootstrap-datetimepicker.min.css" rel="stylesheet">
    <link href="https://akrocare.akrostar-tech.com/css/fileinput.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://akrocare.akrostar-tech.com/vendor/hui/lib/Hui-iconfont/1.0.8/iconfont.css" />
    <style>
        body {
            background-image: url('https://akrocare.akrostar-tech.com/images/bg.jpg');
        }

        input[type="search"] {
            border:2px solid #1D74FF;
            border-radius:20px;
        }
    </style>
        <link href="https://akrocare.akrostar-tech.com/css/cust/review.css" rel="stylesheet">
</head>
<body style="position: relative;padding-bottom: 0px;">
<div id="app">
            <nav class="navbar navbar-default navbar-static-top" style="position: fixed;top: 0;width: 100%;">
    
        <div class="container">
            <div class="navbar-header">

                <!-- Collapsed Hamburger -->
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#app-navbar-collapse" aria-expanded="false">
                    <span class="sr-only">Toggle Navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>

                <!-- Branding Image -->
                                    <div style="padding-top: 7.5px;padding-bottom: 7.5px;">
                        <a href="https://akrocare.akrostar-tech.com/home">
                            <img src="https://akrocare.akrostar-tech.com/images/icon.png" style="display: inline-block; vertical-align: middle;">
                        </a>
                    </div>
                



            </div>

            <div class="collapse navbar-collapse" id="app-navbar-collapse">
                <!-- Left Side Of Navbar -->
                <ul class="nav navbar-nav">
                    &nbsp;
                </ul>

                <!-- Right Side Of Navbar -->
                <ul class="nav navbar-nav navbar-right">
                    <!-- Authentication Links -->
                                            <li class="dropdown">
                            <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false" aria-haspopup="true" v-pre>
                                <span style="color:#ffffff;"> Netforward</span> <span class="caret"></span>
                            </a>

                            <ul class="dropdown-menu">
                                <li>
                                    <a href="https://akrocare.akrostar-tech.com/user/set_passwd" target="_blank">
                                        Setting
                                    </a>

                                    <a href="https://akrocare.akrostar-tech.com/logout"
                                       onclick="event.preventDefault();
                                                     document.getElementById('logout-form').submit();">
                                        Logout
                                    </a>

                                    <form id="logout-form" action="https://akrocare.akrostar-tech.com/logout" method="POST" style="display: none;">
                                        <input type="hidden" name="_token" value="****************************************">
                                    </form>
                                </li>
                            </ul>
                        </li>
                                    </ul>
            </div>
        </div>
    </nav>

        <div class="new-case" style="margin-top: 80px;">
        <div class="container">
            <div class="row">
                <div>
                    <div class="breadcrumb" style="padding: 0;background: none;margin-bottom: 10px;">
                        <ol class="breadcrumb" style="background: none;margin-bottom: 10px;">
                            <li><a href="https://akrocare.akrostar-tech.com/home">Home</a></li>
                            <li><a href="https://akrocare.akrostar-tech.com/case">Case</a></li>
                            <li class="active">Review Case</li>
                        </ol>
                    </div>
                </div>

                <div style="text-align: center;">
                    <h1 style="margin-top: 10px;">Review Case</h1>
                </div>

















































































                <div class="row">
                    <div class="table-responsive">
                        <table class="table" style="font-size: 14px;">
                            <tbody>
                                <tr>
                                    <th style="width:20%;text-align: right;border: none;">Case Number:</th>
                                    <td style="text-align: left;border: none;">01733361085</td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Subject:</th>
                                    <td style="text-align: left;">ddr phy 手册寄存器疑问汇总</td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Product Line:</th>
                                    <td style="text-align: left;">DDR4/3</td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Product Name:</th>
                                    <td style="text-align: left;">AKS DDR4/3 PHY SMIC12SFE</td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Project Name:</th>
                                    <td style="text-align: left;">N0013</td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Version:</th>
                                    <td style="text-align: left;">v1.02a</td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Issue Type:</th>
                                    <td style="text-align: left;">Product Usage</td>
                                </tr>

                                <tr>
                                    <th  style="text-align: right;border: none;">Issue Description:</th>
                                    <td style="text-align: left;">
                                        <div class="col-sm-10 well" style="border-top:none;">
                                            <div style="overflow:auto;width:100%;">
                                                <p>HI,Aden:</p><p>&nbsp;有几个寄存器的问题请帮忙解答以下。详细记录在附件的doc中。</p>
                                            </div>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Priority:</th>
                                    <td style="text-align: left;">
                                        Showstopper+
                                    </td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">CC:</th>
                                    <td style="text-align: left;">
                                        <p class="pull-left col-sm-10" style="padding-left: 0px;"><input type="text" class="form-control" name="notify" autocomplete="off" value="" id="notify" readonly></p>
                                        <p class="pull-right col-sm-2"><span class="btn btn-success" onclick="show_add_cc_view()">modify cc</span></p>
                                    </td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Create Time:</th>
                                    <td style="text-align: left;">
                                        2024-12-05 09:11:25
                                    </td>
                                </tr>

                                <tr>
                                    <th style="text-align: right;border: none;">Status:</th>
                                    <td style="text-align: left;border-bottom: 1px solid #ddd;">
                                        <span class="label label-success radius">Closed</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>


            </div>



            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <div class="col-md-12" style="text-align: left;">
                            <script id="container1" name="issue_description" type="text/plain"></script>
                        </div>
                    </div>
                    <div class="form-group pull-right">
                        <div class="col-md-12" style=";padding-top: 10px;width: 100%;">
                            <button class="btn btn-primary" onclick="add_comment()"><i class="bi-upload"></i>Add comment</button>
                        </div>
                    </div>
                </div>

                <div class="akro-comment" style="padding: 5px;">
                    <div class="col-md-12">
                        <h1 style="text-align: center;margin-bottom: 30px;">Reply Content</h1>
                                                                                            <div>
                                    <div class="col-md-2" style="border-top:none;text-align: center;">AkroStar Reply:</div>
                                    <div class="col-md-10 well" style="border-top:none;">
                                        <div style="overflow:auto;width:100%;">
                                        <span><p>Hi Yadong，</p><p>&nbsp;&nbsp;&nbsp;&nbsp;遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！<br/></p></span>
                                                                                <hr style="margin:2px 0px 2px 0px;">
                                        <span class="pull-right">2025-03-05 10:14:35</span>
                                        </div>
                                    </div>
                                </div>
                                                                                                                            <div>
                                    <div class="col-md-2" style="border-top:none;text-align: center;">AkroStar Reply:</div>
                                    <div class="col-md-10 well" style="border-top:none;">
                                        <div style="overflow:auto;width:100%;">
                                        <span><p>Hi Yadong，</p><p>&nbsp;&nbsp;&nbsp;&nbsp;1. 问题1我们任在确认中；<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;2. 问题2：<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时无法保证每根线的delay完全一致，因此做出这样的修改后可能会导致读数据错误；<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;b. 同样不建议使用一个rank的训练结果给两个rank使用，原理同上；<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;3. 问题3：<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;a. 贵司可以参考IO Retention流程中需要保存的寄存器值，如果该寄存器值被保存，则意味着热复位后需要将该值重新写入PHY中；<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;4. 问题4：<br/></p><p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;a. 从PHY工作的角度来看，只有DevInit是必须要做的初始化配置，其他训练步骤都是为了保证PHY和颗粒之前可以正常通信，因此不建议关闭WrDQ1D和MxRdlat训练，如果关闭该训练可能会导致写数据错误和不同Dbyte之间的读数据不对齐；<br/></p><p>谢谢！</p></span>
                                                                                <hr style="margin:2px 0px 2px 0px;">
                                        <span class="pull-right">2024-12-10 18:42:23</span>
                                        </div>
                                    </div>
                                </div>
                                                                                                                            <div>
                                    <div>
                                        <div class="col-md-2" style="border-top:none;text-align: center;">wf w:</div>
                                        <div class="col-md-10 well" style="border-top:none;">
                                            <div style="overflow:auto;width:100%;">
                                                 <span>
                                                    <p>HI,Aden:&nbsp;</p><p>有任何进展，可以先同步给我们吗?</p>
                                                 </span>
                                                                                                <hr style="margin:2px 0px 2px 0px;">
                                                <span class="pull-right">2024-12-10 16:16:43</span>
                                            </div>

                                        </div>
                                    </div>
                                </div>
                                                                                                                            <div>
                                    <div class="col-md-2" style="border-top:none;text-align: center;">AkroStar Reply:</div>
                                    <div class="col-md-10 well" style="border-top:none;">
                                        <div style="overflow:auto;width:100%;">
                                        <span><p>Hi NetForward Colleagues，</p><p>&nbsp;&nbsp;&nbsp;&nbsp;已收到贵司问题，我将尽快给出回复，谢谢！<br/></p></span>
                                                                                <hr style="margin:2px 0px 2px 0px;">
                                        <span class="pull-right">2024-12-05 20:39:03</span>
                                        </div>
                                    </div>
                                </div>
                                                                                </div>
                </div>

                <hr>
                <div class="akro-attach" style="padding: 5px;">
                    <div class="col-md-12">
                        <h1 style="text-align: center;margin-bottom: 30px;">Attachments:</h1>
                    </div>

                    <div class="col-md-12">
                        <div style="background: #f5f5f5;">
                            <p><a href="https://akrocare.akrostar-tech.com/case">Case</a>&nbsp;>&nbsp;<a href="https://akrocare.akrostar-tech.com/review/case/5766">01733361085</a></p>
                            <p><i class="fa fa-files-o"></i>&nbsp;&nbsp;Attachments</p>
                            <div style="border:1px solid #e3e3e3;padding: 10px;white-space:normal;word-break:break-all;overflow:hidden;">
                                <b>Information</b><br>
                                The maximum file supported for uploading is 10M. If it exceeds this size, it cannot be uploaded.
                            </div>

                            <div class="col-md-12" style="background: #f5f5f5;padding: 10px;">
                                <p class="col-md-offset-6 col-md-2 pull-right">
                                    <button id="upload_attach" class="button btn btn-primary" data-toggle="modal" data-target="#case_attach"><i class="fa fa-upload"></i>&nbsp;&nbsp;Upload Files</button>
                                </p>
                            </div>

                        </div>
                    </div>

                    <div class="col-md-12" style="margin-bottom: 100px;">
                        <table class="table table-condensed">
                            <thead>
                            <tr>
                                <th>File Name</th>
                                <th>File Size</th>
                                <th>Notes</th>
                                <th>Owner</th>
                                <th>Last Modified</th>
                                <th>Action</th>
                            </tr>
                            </thead>
                            <tbody>
                                                                    <tr>
                                        <td>DDRPHY手册疑问记录_20241204.docx</td>
                                        <td>0.94 MB</td>
                                        <td></td>
                                        <td>wf w</td>
                                        <td>2024-12-05 09:11:25</td>
                                        <td>

                                            <div class="btn-group">
                                                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="height: 25px;">
                                                    <span class="caret"></span>
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a href="https://akrocare.akrostar-tech.com/case/download/uploads/case/20241205/DDRPHY%E6%89%8B%E5%86%8C%E7%96%91%E9%97%AE%E8%AE%B0%E5%BD%95_20241204.docx/DDRPHY%E6%89%8B%E5%86%8C%E7%96%91%E9%97%AE%E8%AE%B0%E5%BD%95_20241204.docx">Download</a></li>
                                                                                                    </ul>
                                            </div>
                                        </td>
                                    </tr>
                                                            </tbody>
                        </table>
                    </div>

                </div>

            </div>

        </div>
    </div>



    <div class="modal fade" id="case_attach" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 800px;">
                <div class="modal-header">
                    <button type="button" class="close btn btn-default" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">
                        Add Attachments
                    </h4>
                </div>
                <div class="modal-body">
                    <div class="file-loading">
                        <input type="file" class="file" id="img_url1" name="attach_data[]" multiple>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="add_note" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">Add Notes</h4>
                </div>
                <div class="modal-body">
                    <input type="text" class="form-control" name="file_note" autocomplete="off">
                    <input type="hidden" class="form-control" name="id">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="submit_note()">Confirm</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="message" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">Tips</h4>
                </div>
                <div class="modal-body"><p id="msg"></p></div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal" onclick="close_tip()">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="add_cc" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="width: 740px;height: 740px;">
                <div class="modal-header">
                    <button type="button" class="close btn btn-default" data-dismiss="modal" aria-hidden="true">
                        &times;
                    </button>
                    <h4 class="modal-title" id="myModalLabel">Add CC List Members</h4>
                </div>
                <div class="modal-body">
                    <div>Select Groups/Members</div>
                    <ul id="shuttle_box">
                        <li class="shuttle_box_li shuttle_box_near">
                            <div style="text-align: center;">Available</div>
                            <ul id="shuttle_box_left">
                            </ul>
                        </li>
                        <li class="shuttle_box_li" id="shuttle_box_mid">
                            <button id="shuttle_box_toRight">>></button>
                            <button id="shuttle_box_toLeft"><<</button>
                        </li>
                        <li class="shuttle_box_li shuttle_box_near">
                            <div style="text-align: center;">Selected</div>
                            <ul id="shuttle_box_right">

                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="modal-footer">

                    <button type="button" class="btn btn-warning" onclick="edit_mem()">edit member</button>
                    <button type="button" class="btn btn-primary" onclick="add_mem()">add member</button>
                    <button type="button" class="btn btn-success" onclick="add_mems()">confirm</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="add_member" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">Add Members</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group" style="padding: 10px;margin-bottom: 25px;">
                        <label for="subject" class="col-sm-2 control-label">Name</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="contact" autocomplete="off" name="contact" placeholder="Name" required>
                        </div>
                    </div>
                    <div class="form-group" style="padding: 10px;margin-bottom: 25px;">
                        <label for="subject" class="col-sm-2 control-label">Email</label>
                        <div class="col-sm-10">
                            <input type="email" class="form-control" id="email" autocomplete="off" name="email" placeholder="Email" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="add_member()">Add</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="edit_member" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="myModalLabel">Edit Members</h4>
                </div>
                <div class="modal-body">
                    <div class="form-group" style="padding: 10px;margin-bottom: 25px;">
                        <label for="subject" class="col-sm-2 control-label">Name</label>
                        <div class="col-sm-10">
                            <input type="text" class="form-control" id="edit_member_contact" autocomplete="off" name="edit_member_contact" placeholder="Name" required>
                        </div>
                    </div>
                    <div class="form-group" style="padding: 10px;margin-bottom: 25px;">
                        <label for="subject" class="col-sm-2 control-label">Email</label>
                        <div class="col-sm-10">
                            <input type="email" class="form-control" id="edit_member_email" autocomplete="off" name="edit_member_email" placeholder="Email" required>
                            <input type="hidden" class="form-control" id="edit_member_id" autocomplete="off" name="edit_member_id" required>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" onclick="edit_member()">Edit</button>
                    <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </div>
    </div>


</div>

    <footer style="width: 100%;position: fixed;height: 60px;background-image: url('https://akrocare.akrostar-tech.com/images/bg.jpg');bottom: 0;">
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <p style="text-align: center;font-weight: bold;height: 30px;line-height: 30px;color: white;margin-bottom: 0px;">
                        版权所有 芯耀辉科技有限公司
                    </p>
                    <p style="text-align: center;font-weight: bold;height: 30px;line-height: 30px;color: white;">
                        Copyright&nbsp;©&nbsp;2020-2025 粤ICP备20059560号
                    </p>
                </div>
            </div>
        </div>
    </footer>






<!-- Scripts -->

<script type="text/javascript" src="https://akrocare.akrostar-tech.com/js/jquery.min.js"></script>
<script type="text/javascript" src="https://akrocare.akrostar-tech.com/vendor/bootstrap/js/bootstrap.js"></script>
<script type="text/javascript" src="https://akrocare.akrostar-tech.com/vendor/hui/lib/layer/2.4/layer.js"></script>
<script>
    $(function (){
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        function footerPosition(){
            $("footer").removeClass("fixed-bottom");
            var contentHeight = document.body.scrollHeight,//网页正文全文高度
                winHeight = window.innerHeight;//可视窗口高度，不包括浏览器顶部工具栏
            if(!(contentHeight > winHeight)){
                //当网页正文高度小于可视窗口高度时，为footer添加类fixed-bottom
                $("footer").addClass("fixed-bottom");
            } else {
                $("footer").removeClass("fixed-bottom");
            }
        }
        footerPosition();
        $(window).resize(footerPosition);


    })
</script>
    <script src="https://akrocare.akrostar-tech.com/js/fileinput.min.js"></script>

    <script type="text/javascript" src="https://akrocare.akrostar-tech.com/vendor/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" src="https://akrocare.akrostar-tech.com/vendor/ueditor/ueditor.all.min.js"> </script>
    <script type="text/javascript" src="https://akrocare.akrostar-tech.com/vendor/ueditor/lang/en/en.js"></script>


    <script>

        var ue = UE.getEditor('container1',{
            toolbars: [
                ['cleardoc','insertimage']
            ],
            // toolbars: [[//工具条
            //     'fullscreen', 'source', 'undo', 'redo', 'bold', 'italic',
            //     'underline','fontborder', 'backcolor', 'fontsize', 'fontfamily',
            //     'justifyleft', 'justifyright','justifycenter', 'justifyjustify',
            //     'strikethrough','superscript', 'subscript', 'removeformat',
            //     'formatmatch','autotypeset', 'blockquote', 'pasteplain', '|',
            //     'forecolor', 'backcolor','insertorderedlist', 'insertunorderedlist',
            //     'selectall', 'cleardoc', 'link', 'unlink','emotion', 'help'
            // ]],

            initialFrameHeight: 400,
            autoHeightEnabled: false,
            elementPathEnabled:false,
            wordCount:false,
            zIndex: 0
        });

        $(function (){
            $("#add_member").bind('hide.bs.modal',function(){
                $(".modal-backdrop").remove();
            })
            //
            $("#add_cc").bind('hide.bs.modal',function(){
                $(".modal-backdrop").remove();
            })

            $('#add_cc').on('show.bs.modal', function () {
                // 去刷新下可选列表
                $.ajax({
                    type: "POST",
                    url: "https://akrocare.akrostar-tech.com/case/get_member",
                    data: {},
                    dataType: "json",
                    success: function(data){
                        if(data.info.length > 0) {
                            $("#shuttle_box_left").empty()
                            $("#shuttle_box_right").empty()
                            //获取已经选中的值放到右边
                            var v = $('#notify').val();
                            var arr = v.split(';');
                            $.each(data.info,function(i,n)
                            {
                                if(arr.indexOf(n.email)!=-1) {
                                    $("#shuttle_box_right").append('<li>'+ n.email +'</li>')
                                } else {
                                    $("#shuttle_box_left").append('<li>'+ n.email +'</li>')
                                }
                            });
                        }
                    },
                    error: function (data) {

                    }
                })
            })
        })

        function show_add_cc_view()
        {
            $('#add_cc').modal('show')
        }

        function add_mem()
        {
            //$(".modal-backdrop").remove();
            $('#add_cc').modal('hide')
            //$(".modal-backdrop").remove();
            $('#add_member').modal('show');// modal关闭后，要展示的页面
            return 0
        }

        function edit_mem()
        {
            var selectedItems = $(".shuttle_box_li_act"); // 选中项

            if (selectedItems.length === 0) {
                layer.confirm('Please select a member!', {
                    offset:'45%',
                    title:'Information',
                    btn: ['close']
                });
                return;
            }

            if (selectedItems.length > 1) {
                layer.confirm('Only one member can be selected!', {
                    offset:'45%',
                    title:'Information',
                    btn: ['close']
                });
                return;
            }

            var selectedEmail = selectedItems.text(); // 获取选中项的文本

            $.ajax({
                type: "POST",
                url: "https://akrocare.akrostar-tech.com/api/get_edit_user_cc",
                data: {
                    uid:251,
                    email:selectedEmail
                },
                dataType: "json",
                success: function(data){
                    if(data.code===200 && typeof data !== "undefined" && typeof data.data !== "undefined" && typeof data.data.data !== "undefined" && typeof data.data.data.email !== "undefined" &&
                        typeof data.data.data.contact !== "undefined") {
                        $('#add_cc').modal('hide')
                        $('#edit_member_id').val(data.data.data.id)
                        $('#edit_member_contact').val(data.data.data.contact)
                        $('#edit_member_email').val(data.data.data.email)
                        $('#edit_member').modal('show');// modal关闭后，要展示的页面
                        return ;
                    } else {
                        layer.confirm('Page error,please refresh page!', {
                            offset:'45%',
                            title:'Information',
                            btn: ['close'],
                            btn1: function(index, layero){
                                location.reload();
                            }
                        });
                    }
                },
                error: function (data) {
                    layer.confirm('Page error,please refresh page!', {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            location.reload();
                        }
                    });
                }
            })
        }


        function edit_member()
        {
            let id = $('#edit_member_id').val()
            let contact = $('#edit_member_contact').val()
            let email = $('#edit_member_email').val()

            $.ajax({
                type: "POST",
                url: "https://akrocare.akrostar-tech.com/api/edit_user_cc",
                data: {
                    id:id,
                    contact:contact,
                    email:email,
                },
                dataType: "json",
                success: function(data){
                    layer.confirm(data.message, {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            layer.close(index);
                            $('#edit_member').modal('hide')
                            $('#add_cc').modal('show')
                        }
                    });
                },
                error: function (data) {
                    layer.confirm('Submit Error!', {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            location.reload();
                        }
                    });
                }
            })
        }

        function add_mems()
        {
            let member = $("#shuttle_box_right li");

            if (member.length > 0) {
                let emails = []; // 用于存储所有的 email

                // 遍历 member 并获取文本内容
                member.each(function() {
                    var email = $(this).text().trim();
                    if (email.length > 0) {
                        emails.push(email);
                    }
                });

                // 将 #notify 的值当作空值处理
                var existingEmails = [];  // 强制当作空值处理

                // 合并 & 去重
                var uniqueEmails = [...new Set(existingEmails.concat(emails))];
                // 设置 #notify 的值
                $('#notify').val(uniqueEmails.join(';'));
            }

            var notify = $('#notify').val()
            var case_id = '5766'

            $.ajax({
                type: "POST",
                url: "https://akrocare.akrostar-tech.com/case/modify_cc",
                data: {
                    notify:notify,
                    case_id:case_id
                },
                dataType: "json",
                success: function(data){
                    layer.confirm(data.message, {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            location.reload();
                        }
                    });
                },
                error: function (data) {
                    layer.confirm('Submit Error!', {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            location.reload();
                        }
                    });
                }
            })
        }

        function add_member()
        {
            var contact = $('#add_member input[name="contact"]').val()
            var email = $('#add_member input[name="email"]').val()
            email = $.trim(email)
            var myreg = /^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/;
            if(!myreg.test(email)){
                layer.msg('Email Invalid!',{title:'Tips',time:1500,icon:0,offset:'45%',})
                return false
            }
            var sear=new RegExp(';');
            if(sear.test(email)){
                layer.msg('Email Invalid!',{title:'Tips',time:1500,icon:0, offset:'45%',})
                return false
            }

            if(contact.length <=0 || email.length<=0) {
                layer.msg('Please Input Contact Or Email!',{title:'Tips',time:1500,icon:0,offset:'45%',})
                return false
            }
            $.ajax({
                type: "POST",
                url: "https://akrocare.akrostar-tech.com/case/add_member",
                data: {
                    contact:contact,
                    email:email
                },
                dataType: "json",
                success: function(data){
                    layer.confirm(data.message, {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            layer.close(index);
                            $('#add_member').modal('hide')
                            $('#add_cc').modal('show')
                        }
                    });
                },
                error: function (data) {
                    layer.confirm('Submit Error!', {
                        offset:'45%',
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            location.reload();
                        }
                    });
                }
            })
        }

        //穿梭框左侧选中
        $("#shuttle_box_left").on('click', 'li', function () {
            if ($(this).hasClass('shuttle_box_li_act')) {
                $(this).removeClass('shuttle_box_li_act');
            } else {
                $(this).addClass('shuttle_box_li_act');
            }
        });
        //穿梭框右侧选中
        $("#shuttle_box_right").on('click', 'li', function () {
            if ($(this).hasClass('shuttle_box_li_act')) {
                $(this).removeClass('shuttle_box_li_act');
            } else {
                $(this).addClass('shuttle_box_li_act');
            }
        });
        //向右移动
        $("#shuttle_box_toRight").click(function () {
            if ($("#shuttle_box_right li").length == 0) {
                $("#notify").val('')
            }
            if ($("#shuttle_box_left .shuttle_box_li_act").length == 0) return false;


            //将移动到右边的email添加到Notify的输入框中
            var member = $("#shuttle_box_left").find('.shuttle_box_li_act')
            $.each(member,function() {
                var email = $(this).text()
                if(email.length > 0) {
                    var v = $('#notify').val();
                    if(v.length > 0) {
                        $("#notify").val($("#notify").val() + ';' + email)
                    } else {
                        $("#notify").val(email)
                    }
                }
            });
            $("#shuttle_box_left").find('.shuttle_box_li_act').appendTo("#shuttle_box_right");
            $("#shuttle_box_right li").removeClass('shuttle_box_li_act');
        });
        //向左移动
        $("#shuttle_box_toLeft").click(function () {
            if ($("#shuttle_box_right li").length == 0) {
                $("#notify").val('')
            }
            if ($("#shuttle_box_right .shuttle_box_li_act").length == 0) return false;

            var member = $("#shuttle_box_right .shuttle_box_li_act");
            var v = $('#notify').val();
            var arr = v.split(';');
            $.each(member,function() {
                var email = $(this).text()
                if(email.length > 0) {
                    if(v.length > 0) {
                        if(arr.indexOf(email)!=-1) {
                            arr.splice(arr.indexOf(email),1);
                        }
                    }
                }
            });
            if(arr.length>=1){
                let email = '';
                for(var i=0;i<arr.length;i++){
                    email += arr[i] + ';'
                }
                var pos = email.lastIndexOf(';');
                email = email.substr(0,pos);
                $("#notify").val(email)
            } else {
                $("#notify").val('')
            }

            $("#shuttle_box_right .shuttle_box_li_act").appendTo("#shuttle_box_left");
            $("#shuttle_box_left li").removeClass('shuttle_box_li_act');
        });



        var $modal = $('#case_attach');


        function centerModals(e) {
            var formData = new FormData();
            $.ajax({
                type: "POST",
                url: "https://akrocare.akrostar-tech.com/api/case/check_case_license/5766",
                data: formData,
                dataType: "json",
                contentType:false,
                processData: false,
                async: false,
                layerIndex: -1,
                beforeSend: function () {
                    this.layerIndex = layer.load(0, {shade: [0.5, '#666c7f']});
                },
                complete: function () {
                    layer.close(this.layerIndex);
                },
                success: function(data){
                    if(data.code===504) {
                        layer.open({
                            offset:'45%',
                            title: 'warning'
                            ,content: data.message
                            ,icon:0,
                            btn: ['close'],
                        });
                        e.preventDefault(); // 阻止模态框弹出
                        return false
                    } else {
                        $('.modal').each(function(i) {
                            var $clone = $(this).clone().css('display', 'block').appendTo('body');
                            var top = Math.round(($clone.height() - $clone.find('.modal-content').height()) / 2);
                            top = top > 50 ? top : 0;
                            $clone.remove();
                            $(this).find('.modal-content').css("margin-top", top - 250);
                        });
                    }
                },
                error: function (data) {
                    layer.open({
                        title: 'warning'
                        ,offset:'45%'
                        ,content: "Page Error,Please Refresh Page!"
                        ,icon:0
                        ,btn: ['confirm']
                    });
                    return false
                }
            })
        }

        $modal.on('show.bs.modal', centerModals);
        // 在窗口大小改变的时候调用垂直居中方法
        $(window).on('resize', centerModals);



        $modal.on('hide.bs.modal', function () {
            location.reload();
        })

        $("#img_url1").fileinput({
            //language: 'zh',
            uploadUrl: "https://akrocare.akrostar-tech.com/case/upload_file/5766", //上传后台操作的方法
            uploadAsync: false, //设置上传同步异步 此为同步
            maxFileSize: 10240,
            allowedFileExtensions: ['rpt','log','txt','zip','jpg','jpeg','png','bmp','rar','7z','ppt','pptx','xlsx','xls','doc','docx','pdf'], //限制上传文件后缀
            showUpload: true, //是否显示上传按钮
            dropZoneEnabled: false,
            maxFileCount: 10,
            inputGroupClass: "input-group-sm",
            browseClass: "btn btn-primary", //按钮样式
            previewFileIcon: "<i class='glyphicon glyphicon-king'></i>",
            enctype: 'multipart/form-data',
            fileActionSettings:{
                showRemove: true, //移除
                showUpload: false, //上传
                showZoom: true, //放大
                showDrag: true,
            },
            uploadError: function(event, data, previewId, index) {
                var errorContainer = $("#errorContainer");
                errorContainer.html("Upload Error:" + data.jqXHR.responseText);
            }
        });

        function addNotes(id)
        {
            $('#add_note input[name="id"]').val(id)
            $('#add_note').modal('show')
            return 0
        }

        function deleteAttache(id)
        {
            layer.confirm('Are You Sure Delete This Attach?', {
                offset: '300px', //距离顶部的位置
                title:'Tips',
                btn: ['No','Yes'],
                btn2: function(index, layero){
                    $.ajax({
                        type: "POST",
                        url: "https://akrocare.akrostar-tech.com/case/delete_attach",
                        data: {
                            id:id
                        },
                        dataType: "json",
                        success: function(data){
                            layer.confirm(data.message, {
                                offset: '300px', //距离顶部的位置
                                title:'Information',
                                btn: ['close'],
                                btn1: function(index, layero){
                                    window.location.reload();
                                }
                            });
                        },
                        error: function(xhr, status, error) {
                            var statusCode = xhr.status; // 获取 HTTP 状态码
                            if (statusCode === 419) {
                                layer.open({
                                    title: 'warning'
                                    ,offset:'45%'
                                    ,content: "Page Expired,Please Refresh Page!"
                                    ,icon:0
                                    ,btn: ['confirm'],
                                });
                            } else {
                                layer.open({
                                    title: 'warning'
                                    ,offset:'45%'
                                    ,content: "Submit Error,Please Refresh Page!"
                                    ,icon:0
                                    ,btn: ['confirm']
                                });
                            }
                            return false
                        }
                    })
                }
            });
        }

        function submit_note()
        {
            var id = $('#add_note input[name="id"]').val()
            var note = $('#add_note input[name="file_note"]').val()
            if(note.length <=0) {
                layer.msg('Please Input Note!',{title:'Tips',time:1500,icon:0})
                return false
            }
            if(id<=0) {
                layer.msg('System Error!',{title:'Tips',time:1500,icon:0})
                return false
            }
            $.ajax({
                type: "POST",
                url: "https://akrocare.akrostar-tech.com/case/add_note",
                data: {
                    id:id,
                    note:note
                },
                dataType: "json",
                success: function(data){
                    $('#add_note').modal('hide')
                    layer.confirm(data.msg, {
                        offset: '300px', //距离顶部的位置
                        title:'Information',
                        btn: ['close'],
                        btn1: function(index, layero){
                            location.reload();
                        }
                    });
                    // $('#msg').empty();
                    // $('#msg').append(data.msg);
                    // $('#message').modal('show')
                },
                // error: function (data) {
                //     // $('#msg').empty();
                //     // $('#msg').append('Submit Error!');
                //     // $('#message').modal('show')
                //     layer.confirm('Submit Error!', {
                //         title:'Information',
                //         btn: ['close'],
                //         btn1: function(index, layero){
                //             location.reload();
                //         }
                //     });
                // }
                error: function(xhr, status, error) {
                    var statusCode = xhr.status; // 获取 HTTP 状态码
                    if (statusCode === 419) {
                        layer.open({
                            title: 'warning'
                            ,offset:'45%'
                            ,content: "Page Expired,Please Refresh Page!"
                            ,icon:0
                            ,btn: ['confirm'],
                        });
                    } else {
                        layer.open({
                            title: 'warning'
                            ,offset:'45%'
                            ,content: "Submit Error,Please Refresh Page!"
                            ,icon:0
                            ,btn: ['confirm']
                        });
                    }
                    return false
                }
            })
        }


        $('#add_note').on('hidden.bs.modal', function () {
            $('#add_note input[name="file_note"]').val('')
        })

        $('#message').on('hidden.bs.modal', function () {
            $('#message modal-body').empty();
        })

        function close_tip()
        {
            location.reload();
        }

        function add_comment()
        {
            //var reply_content = $('#reply_content').val();
            var reply_content = ue.getContent();
            //校验XSS
            // var arrs=["alert","eval","<script>","<\/script>","onblur","onload","onfocus","onerror","onclick","onMouseOver", "onMouseOut","onSelect","onChange","onSubmit","console","href","<img>","<\/img>","<iframe>","<\/iframe>","<video>","<\/video>", "<canvas>","<\/canvas>","<label>","<\/label>","<span>","<\/span>","document","location","javascript"];
            // for(i=0;i<arrs.length;i++) {
            //     if(reply_content.indexOf(arrs[i])!=-1){
            //         alert('Illegal content submission!');
            //         return false;
            //     }
            // }
            // let arr = [];
            // reply_content.split('\n').forEach(item=>arr.push(`<span>${item.trim()}</span>`));
            // reply_content = arr.join('<br>');

            // if(document.getElementById("reply_content").value=='') {
            //     layer.msg('Please Input Comment!',{title:'Tips',time:1500,icon:0})
            //     return false
            // }
            if(reply_content.length<=0) {
                layer.msg('Please Input Comment!',{title:'Tips',time:1500,icon:0,offset:'45%'})
                return false
            } else {
                var formData = new FormData();
                formData.append('reply_content', reply_content);
                layer.confirm('Are you sure add these comment?',{btn: ['Confirm', 'Cancel'],title:"Warning",icon:0,offset:'45%',},function(){
                    $.ajax({
                        type: "POST",
                        url: "https://akrocare.akrostar-tech.com/case/add_comment/5766",
                        data: formData,
                        dataType: "json",
                        contentType:false,
                        processData: false,
                        layerIndex: -1,
                        beforeSend: function () {
                            // this.layerIndex = layer.load(0, {shade: [0.5, '#666c7f']});
                            this.layerIndex = layer.load(0,{ offset: ['50%', "50%"], shade: [0.5, '#666c7f'] });
                            // var width = document.body.offsetWidth;
                            // var wdithpx = (width / 2 - 37 / 2);
                            // this.layerIndex = layer.load(1, { offset: ['50%', wdithpx], shade:false });
                        },
                        complete: function () {
                            layer.close(this.layerIndex);
                        },
                        
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                                
                        success: function(data){
                            // layer.confirm(data.msg, {
                            //     offset:'45%',
                            //     title:'Information',
                            //     icon:1,
                            //     btn: ['close'],
                            //     btn1: function(index, layero){
                            //         location.reload();
                            //     }
                            // });

                            if(data.code===200) {
                                layer.confirm(data.message, {
                                    offset:'45%',
                                    title:'Information',
                                    icon:1,
                                    btn: ['close'],
                                    btn1: function(index, layero){
                                        location.reload();
                                    }
                                });
                            } else {
                                layer.open({
                                    offset:'45%',
                                    title: 'warning'
                                    ,content: data.message
                                    ,icon:0,
                                    btn: ['close'],
                                });
                                return false
                            }

                            // $('#msg').empty();
                            // $('#msg').append(data.msg);
                            // $('#message').modal('show')
                        },
                        // error: function (data) {
                        //     // $('#msg').empty();
                        //     // $('#msg').append('Submit Error!');
                        //     // $('#message').modal('show')
                        //     layer.confirm('Submit Error!', {
                        //         title:'Information',
                        //         icon:0,
                        //         btn: ['close'],
                        //         btn1: function(index, layero){
                        //             location.reload();
                        //         }
                        //     });
                        // }
                        error: function(xhr, status, error) {
                            var statusCode = xhr.status; // 获取 HTTP 状态码
                            if (statusCode === 419) {
                                layer.open({
                                    title: 'warning'
                                    ,offset:'45%'
                                    ,content: "Page Expired,Please Refresh Page!"
                                    ,icon:0
                                    ,btn: ['confirm'],
                                });
                            } else {
                                layer.open({
                                    title: 'warning'
                                    ,offset:'45%'
                                    ,content: "Submit Error,Please Refresh Page!"
                                    ,icon:0
                                    ,btn: ['confirm']
                                });
                            }
                            return false
                        }
                    })
                });
            }
        }

        //刷新layer弹窗的水平居中位置
        function refreshArea(_formWin){
            var _formWinObj = $("#layui-layer"+_formWin);
            var w = _formWinObj.outerWidth();
            var h = _formWinObj.outerHeight();
            var l = ($(document).width()-w)/2;
            var t = ($(window).height()-h)/2;
            _formWinObj.css({
                "left":l,
                "top":t
            });
        }


    </script>
</body>
</html>
