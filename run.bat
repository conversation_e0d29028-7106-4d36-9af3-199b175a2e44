@echo off
echo ========================================
echo AkroCare工单采集程序
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.8+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖包...
pip show selenium >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 检查配置文件
if not exist config.py (
    echo 错误: 配置文件config.py不存在
    echo 请复制config.example.py为config.py并修改登录信息
    pause
    exit /b 1
)

echo.
echo 选择运行模式:
echo 1. 运行主程序（采集工单）
echo 2. 运行测试
echo 3. 运行演示程序
echo 4. 退出
echo.

set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" (
    echo 启动工单采集程序...
    python main.py
) else if "%choice%"=="2" (
    echo 运行测试...
    python test_collector.py
) else if "%choice%"=="3" (
    echo 启动演示程序...
    python demo.py
) else if "%choice%"=="4" (
    echo 退出程序
    exit /b 0
) else (
    echo 无效选择
)

echo.
pause
