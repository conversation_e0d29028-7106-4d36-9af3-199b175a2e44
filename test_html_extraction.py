#!/usr/bin/env python3
"""
测试HTML内容提取功能
使用本地demo-content.html文件测试评论提取
"""
import os
import sys
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from case_collector import CaseCollector
from models import CaseComment
from utils import logger


def load_demo_html():
    """加载本地demo-content.html文件"""
    try:
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.error("demo-content.html文件不存在")
        return None
    except Exception as e:
        logger.error(f"读取demo-content.html失败: {e}")
        return None


def analyze_html_structure(html_content):
    """分析HTML结构，找出评论相关的元素"""
    print("=" * 60)
    print("HTML结构分析")
    print("=" * 60)
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 查找可能的评论容器
    print("1. 查找class包含'comment'的元素:")
    comment_elements = soup.find_all(class_=lambda x: x and 'comment' in x.lower())
    for i, elem in enumerate(comment_elements):
        print(f"   {i+1}. {elem.name} class='{elem.get('class')}'")
        if elem.get_text(strip=True):
            text_preview = elem.get_text(strip=True)[:100]
            print(f"      内容预览: {text_preview}...")
    
    print("\n2. 查找class包含'akro'的元素:")
    akro_elements = soup.find_all(class_=lambda x: x and 'akro' in x.lower())
    for i, elem in enumerate(akro_elements):
        print(f"   {i+1}. {elem.name} class='{elem.get('class')}'")
    
    print("\n3. 查找class包含'col-md'的元素:")
    col_elements = soup.find_all(class_=lambda x: x and 'col-md' in x.lower())
    print(f"   找到 {len(col_elements)} 个col-md元素")
    
    print("\n4. 查找class包含'well'的元素:")
    well_elements = soup.find_all(class_=lambda x: x and 'well' in x.lower())
    print(f"   找到 {len(well_elements)} 个well元素")
    
    return soup


def extract_comments_original_method(soup, case_id):
    """使用原始方法提取评论"""
    print("\n" + "=" * 60)
    print("使用原始方法提取评论")
    print("=" * 60)
    
    comments = []
    
    try:
        # 查找所有评论容器
        comment_containers = soup.find_all(class_="akro-comment")
        print(f"找到 {len(comment_containers)} 个akro-comment容器")
        
        for i, container in enumerate(comment_containers):
            print(f"\n处理第 {i+1} 个评论容器:")
            try:
                # 提取评论来源
                source_element = container.find(class_="col-md-2")
                source = source_element.get_text(strip=True) if source_element else "Unknown"
                print(f"  来源: {source}")
                
                # 提取评论内容和时间
                content_element = container.find(class_="col-md-10")
                if content_element:
                    well_element = content_element.find(class_="well")
                    if well_element:
                        # 提取时间（通常在最后的span中）
                        time_spans = well_element.find_all('span')
                        timestamp = ""
                        if time_spans:
                            timestamp = time_spans[-1].get_text(strip=True)
                        print(f"  时间: {timestamp}")
                        
                        # 移除时间span后获取内容
                        well_copy = BeautifulSoup(str(well_element), 'html.parser')
                        time_spans_copy = well_copy.find_all('span')
                        if time_spans_copy:
                            time_spans_copy[-1].decompose()
                        
                        content = well_copy.get_text(strip=True)
                        print(f"  内容: {content[:100]}...")
                        
                        comment = CaseComment(
                            source=source,
                            content=content,
                            timestamp=timestamp
                        )
                        comments.append(comment)
                        
            except Exception as e:
                print(f"  解析评论失败: {e}")
                continue
                
    except Exception as e:
        print(f"提取评论失败: {e}")
    
    print(f"\n原始方法提取到 {len(comments)} 条评论")
    return comments


def extract_comments_improved_method(soup, case_id):
    """改进的评论提取方法"""
    print("\n" + "=" * 60)
    print("使用改进方法提取评论")
    print("=" * 60)
    
    comments = []
    
    try:
        # 方法1: 查找akro-comment容器
        comment_containers = soup.find_all(class_="akro-comment")
        print(f"方法1: 找到 {len(comment_containers)} 个akro-comment容器")
        
        for i, container in enumerate(comment_containers):
            print(f"\n处理akro-comment第 {i+1} 个:")
            try:
                # 查找所有div元素
                divs = container.find_all('div')
                print(f"  找到 {len(divs)} 个div元素")
                
                # 寻找包含col-md-2的div作为来源
                source = "Unknown"
                content = ""
                timestamp = ""
                
                for div in divs:
                    div_class = div.get('class', [])
                    if 'col-md-2' in div_class:
                        source = div.get_text(strip=True)
                        print(f"  来源: {source}")
                    elif 'col-md-10' in div_class:
                        # 查找well元素
                        well = div.find(class_="well")
                        if well:
                            # 提取时间
                            spans = well.find_all('span')
                            if spans:
                                # 尝试从最后一个span获取时间
                                for span in reversed(spans):
                                    span_text = span.get_text(strip=True)
                                    if any(char.isdigit() for char in span_text) and ('-' in span_text or ':' in span_text):
                                        timestamp = span_text
                                        break
                            
                            # 提取内容（移除时间span）
                            well_copy = BeautifulSoup(str(well), 'html.parser')
                            time_spans = well_copy.find_all('span')
                            for span in time_spans:
                                span_text = span.get_text(strip=True)
                                if any(char.isdigit() for char in span_text) and ('-' in span_text or ':' in span_text):
                                    span.decompose()
                            
                            # 移除hr元素
                            hrs = well_copy.find_all('hr')
                            for hr in hrs:
                                hr.decompose()
                            
                            content = well_copy.get_text(strip=True)
                            print(f"  时间: {timestamp}")
                            print(f"  内容: {content[:100]}...")
                
                if source != "Unknown" and content:
                    comment = CaseComment(
                        source=source,
                        content=content,
                        timestamp=timestamp
                    )
                    comments.append(comment)
                    
            except Exception as e:
                print(f"  解析评论失败: {e}")
                continue
        
        # 方法2: 直接查找Reply Content区域
        print(f"\n方法2: 查找Reply Content区域")
        reply_section = soup.find('h1', string=lambda text: text and 'Reply Content' in text)
        if reply_section:
            print("  找到Reply Content区域")
            # 找到Reply Content后的所有div
            parent = reply_section.find_parent()
            if parent:
                reply_divs = parent.find_all('div', recursive=True)
                print(f"  找到 {len(reply_divs)} 个相关div")
                
                current_source = ""
                current_content = ""
                current_timestamp = ""
                
                for div in reply_divs:
                    div_class = div.get('class', [])
                    div_text = div.get_text(strip=True)
                    
                    # 检查是否是来源div
                    if 'col-md-2' in div_class and div_text:
                        if current_source and current_content:
                            # 保存前一个评论
                            comment = CaseComment(
                                source=current_source,
                                content=current_content,
                                timestamp=current_timestamp
                            )
                            comments.append(comment)
                            print(f"  添加评论: {current_source} - {current_timestamp}")
                        
                        current_source = div_text
                        current_content = ""
                        current_timestamp = ""
                    
                    elif 'col-md-10' in div_class and 'well' in div_class:
                        # 这是内容div
                        spans = div.find_all('span')
                        for span in spans:
                            span_text = span.get_text(strip=True)
                            if any(char.isdigit() for char in span_text) and ('-' in span_text or ':' in span_text):
                                current_timestamp = span_text
                                span.decompose()
                        
                        # 移除hr元素
                        hrs = div.find_all('hr')
                        for hr in hrs:
                            hr.decompose()
                        
                        current_content = div.get_text(strip=True)
                
                # 保存最后一个评论
                if current_source and current_content:
                    comment = CaseComment(
                        source=current_source,
                        content=current_content,
                        timestamp=current_timestamp
                    )
                    comments.append(comment)
                    print(f"  添加评论: {current_source} - {current_timestamp}")
        
    except Exception as e:
        print(f"改进方法提取评论失败: {e}")
    
    print(f"\n改进方法提取到 {len(comments)} 条评论")
    return comments


def test_comment_extraction():
    """测试评论提取功能"""
    print("开始测试评论提取功能...")
    
    # 加载HTML内容
    html_content = load_demo_html()
    if not html_content:
        print("无法加载HTML内容，测试终止")
        return
    
    print(f"成功加载HTML内容，长度: {len(html_content)} 字符")
    
    # 分析HTML结构
    soup = analyze_html_structure(html_content)
    
    # 测试原始方法
    original_comments = extract_comments_original_method(soup, 5766)
    
    # 测试改进方法
    improved_comments = extract_comments_improved_method(soup, 5766)
    
    # 比较结果
    print("\n" + "=" * 60)
    print("结果比较")
    print("=" * 60)
    print(f"原始方法提取到: {len(original_comments)} 条评论")
    print(f"改进方法提取到: {len(improved_comments)} 条评论")
    
    # 显示详细结果
    if improved_comments:
        print("\n改进方法提取的评论详情:")
        for i, comment in enumerate(improved_comments, 1):
            print(f"\n评论 {i}:")
            print(f"  来源: {comment.source}")
            print(f"  时间: {comment.timestamp}")
            print(f"  内容: {comment.content[:200]}...")
    
    return improved_comments


def main():
    """主函数"""
    print("HTML评论提取测试程序")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists('demo-content.html'):
        print("错误: demo-content.html文件不存在")
        print("请确保文件在当前目录下")
        return
    
    # 运行测试
    comments = test_comment_extraction()
    
    if comments:
        print(f"\n✅ 测试成功！提取到 {len(comments)} 条评论")
    else:
        print("\n❌ 测试失败！未能提取到评论")
        print("需要进一步调试HTML结构")


if __name__ == "__main__":
    main()
