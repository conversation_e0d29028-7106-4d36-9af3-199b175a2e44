"""
数据模型定义
"""
from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from datetime import datetime


@dataclass
class CaseComment:
    """工单评论"""
    source: str  # 评论来源
    content: str  # 评论内容
    timestamp: str  # 评论时间
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'source': self.source,
            'content': self.content,
            'timestamp': self.timestamp
        }


@dataclass
class CaseAttachment:
    """工单附件"""
    attachment_id: str  # 附件ID
    file_name: str  # 文件名
    file_size: str  # 文件大小
    owner: str  # 所有者
    last_modified: str  # 最后修改时间
    download_url: str  # 下载链接
    local_path: str = ""  # 本地存储路径
    version: int = 1  # 版本号（用于增量更新）
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'attachment_id': self.attachment_id,
            'file_name': self.file_name,
            'file_size': self.file_size,
            'owner': self.owner,
            'last_modified': self.last_modified,
            'download_url': self.download_url,
            'local_path': self.local_path,
            'version': self.version
        }


@dataclass
class CaseSummary:
    """工单概要信息"""
    id: int
    case_number: str
    subject: str
    product_line: str
    product_name: str
    project_name: str
    cnum: str
    product_code: str
    case_level: int
    issue_type: int
    issue_description: str
    attach: Optional[str]
    priority: int
    version: str
    notify: Optional[str]
    status: int
    uid: int
    deadline: Optional[str]
    is_admin_create: int
    created_at: str
    updated_at: str
    company_name: str
    username: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'case_number': self.case_number,
            'subject': self.subject,
            'product_line': self.product_line,
            'product_name': self.product_name,
            'project_name': self.project_name,
            'cnum': self.cnum,
            'product_code': self.product_code,
            'case_level': self.case_level,
            'issue_type': self.issue_type,
            'issue_description': self.issue_description,
            'attach': self.attach,
            'priority': self.priority,
            'version': self.version,
            'notify': self.notify,
            'status': self.status,
            'uid': self.uid,
            'deadline': self.deadline,
            'is_admin_create': self.is_admin_create,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'company_name': self.company_name,
            'username': self.username
        }


@dataclass
class CaseDetail:
    """工单详细信息"""
    summary: CaseSummary
    comments: List[CaseComment] = field(default_factory=list)
    attachments: List[CaseAttachment] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'summary': self.summary.to_dict(),
            'comments': [comment.to_dict() for comment in self.comments],
            'attachments': [attachment.to_dict() for attachment in self.attachments]
        }


@dataclass
class CollectionState:
    """采集状态"""
    last_update_time: str
    collected_case_ids: List[int] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'last_update_time': self.last_update_time,
            'collected_case_ids': self.collected_case_ids
        }
