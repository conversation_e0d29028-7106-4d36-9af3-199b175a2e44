#!/bin/bash

echo "========================================"
echo "AkroCare工单采集程序"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请先安装Python 3.8+"
    exit 1
fi

# 检查依赖是否安装
echo "检查依赖包..."
if ! python3 -c "import selenium" &> /dev/null; then
    echo "正在安装依赖包..."
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

# 检查配置文件
if [ ! -f "config.py" ]; then
    echo "错误: 配置文件config.py不存在"
    echo "请复制config.example.py为config.py并修改登录信息"
    exit 1
fi

echo
echo "选择运行模式:"
echo "1. 运行主程序（采集工单）"
echo "2. 运行测试"
echo "3. 运行演示程序"
echo "4. 退出"
echo

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "启动工单采集程序..."
        python3 main.py
        ;;
    2)
        echo "运行测试..."
        python3 test_collector.py
        ;;
    3)
        echo "启动演示程序..."
        python3 demo.py
        ;;
    4)
        echo "退出程序"
        exit 0
        ;;
    *)
        echo "无效选择"
        ;;
esac

echo
read -p "按任意键继续..."
