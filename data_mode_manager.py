#!/usr/bin/env python3
"""
数据模式管理器
支持全量模式和增量模式的数据采集和管理
"""
import os
import json
import zipfile
import shutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, asdict

import config
from utils import logger, save_json, load_json
from models import CaseDetail, CaseSummary
from attachment_manager import get_attachment_manager


@dataclass
class PatchMetadata:
    """Patch元数据"""
    patch_id: str
    timestamp: str
    mode: str  # "full" 或 "incremental"
    base_version: Optional[str]  # 基于的版本（增量模式）
    case_count: int
    new_cases: List[int]  # 新增工单ID
    updated_cases: List[int]  # 更新工单ID
    description: str


@dataclass
class PatchContent:
    """Patch内容"""
    metadata: PatchMetadata
    cases: Dict[str, Any]  # 工单数据
    attachments: Dict[str, List[str]]  # 附件文件路径


class DataModeManager:
    """数据模式管理器"""

    def __init__(self):
        self.data_dir = config.DATA_DIR
        self.patches_dir = os.path.join(self.data_dir, "patches")
        self.current_version = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 确保目录存在
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.patches_dir, exist_ok=True)

        # 初始化附件管理器
        self.attachment_manager = get_attachment_manager(self.current_version)

    def create_full_mode_package(self, case_details: List[CaseDetail],
                                enable_zip: bool = True) -> str:
        """创建全量模式数据包"""
        logger.info("开始创建全量模式数据包...")

        # 创建全量数据目录
        full_data_dir = os.path.join(self.data_dir, f"full_{self.current_version}")
        os.makedirs(full_data_dir, exist_ok=True)

        # 保存工单数据
        cases_data = {}
        attachment_files = {}

        for case_detail in case_details:
            case_id = str(case_detail.summary.id)
            cases_data[case_id] = case_detail.to_dict()

            # 收集附件文件路径
            if case_detail.attachments:
                attachment_files[case_id] = []
                for attachment in case_detail.attachments:
                    if hasattr(attachment, 'local_path') and attachment.local_path:
                        attachment_files[case_id].append(attachment.local_path)

        # 创建Patch元数据
        metadata = PatchMetadata(
            patch_id=f"full_{self.current_version}",
            timestamp=datetime.now().isoformat(),
            mode="full",
            base_version=None,
            case_count=len(case_details),
            new_cases=[case.summary.id for case in case_details],
            updated_cases=[],
            description=f"全量模式采集，共{len(case_details)}个工单"
        )

        # 创建Patch内容
        patch_content = PatchContent(
            metadata=metadata,
            cases=cases_data,
            attachments=attachment_files
        )

        # 保存到目录
        self._save_patch_to_directory(patch_content, full_data_dir, case_details)

        # 如果启用ZIP打包
        if enable_zip:
            zip_path = f"{full_data_dir}.zip"
            self._create_zip_package(full_data_dir, zip_path)

            # 删除临时目录
            shutil.rmtree(full_data_dir, ignore_errors=True)

            logger.info(f"全量模式数据包已创建: {zip_path}")
            return zip_path
        else:
            logger.info(f"全量模式数据目录已创建: {full_data_dir}")
            return full_data_dir

    def create_incremental_patch(self, case_details: List[CaseDetail],
                                base_version: Optional[str] = None) -> str:
        """创建增量模式Patch"""
        logger.info("开始创建增量模式Patch...")

        # 获取基础版本数据
        existing_cases = self._load_existing_cases(base_version)

        # 分析变更
        new_cases = []
        updated_cases = []
        patch_cases = {}
        attachment_files = {}

        for case_detail in case_details:
            case_id = str(case_detail.summary.id)
            case_data = case_detail.to_dict()

            is_changed = False
            if case_id not in existing_cases:
                # 新增工单
                new_cases.append(case_detail.summary.id)
                patch_cases[case_id] = case_data
                is_changed = True
                logger.info(f"新增工单: {case_id} - {case_detail.summary.subject}")
            else:
                # 检查是否有更新
                if self._has_case_changed(case_data, existing_cases[case_id]):
                    updated_cases.append(case_detail.summary.id)
                    patch_cases[case_id] = case_data
                    is_changed = True
                    logger.info(f"更新工单: {case_id} - {case_detail.summary.subject}")

            # 只为有变更的工单收集附件文件路径
            if is_changed and case_detail.attachments:
                attachment_files[case_id] = []
                for attachment in case_detail.attachments:
                    if hasattr(attachment, 'local_path') and attachment.local_path:
                        attachment_files[case_id].append(attachment.local_path)
                logger.debug(f"工单 {case_id} 收集到 {len(attachment_files[case_id])} 个附件")

        # 创建Patch元数据
        metadata = PatchMetadata(
            patch_id=f"patch_{self.current_version}",
            timestamp=datetime.now().isoformat(),
            mode="incremental",
            base_version=base_version,
            case_count=len(patch_cases),
            new_cases=new_cases,
            updated_cases=updated_cases,
            description=f"增量采集，新增{len(new_cases)}个工单，更新{len(updated_cases)}个工单"
        )

        # 创建Patch内容
        patch_content = PatchContent(
            metadata=metadata,
            cases=patch_cases,
            attachments=attachment_files
        )

        # 保存Patch
        patch_dir = os.path.join(self.patches_dir, f"patch_{self.current_version}")
        os.makedirs(patch_dir, exist_ok=True)

        # 只传递有变更的工单
        changed_case_details = [case for case in case_details
                               if case.summary.id in new_cases + updated_cases]
        self._save_patch_to_directory(patch_content, patch_dir, changed_case_details)

        # 打包成ZIP
        patch_zip = f"{patch_dir}.zip"
        self._create_zip_package(patch_dir, patch_zip)

        # 删除临时目录
        shutil.rmtree(patch_dir, ignore_errors=True)

        logger.info(f"增量Patch已创建: {patch_zip}")
        return patch_zip

    def _load_existing_cases(self, base_version: Optional[str] = None) -> Dict[str, Any]:
        """加载现有工单数据"""
        existing_cases = {}

        if base_version:
            # 从指定版本加载
            base_path = os.path.join(self.data_dir, f"full_{base_version}")
            if os.path.exists(f"{base_path}.zip"):
                existing_cases = self._load_cases_from_zip(f"{base_path}.zip")
            elif os.path.exists(base_path):
                existing_cases = self._load_cases_from_directory(base_path)
        else:
            # 从当前数据文件加载
            if os.path.exists(config.CASES_DATA_FILE):
                data = load_json(config.CASES_DATA_FILE)
                if data and 'cases' in data:
                    for case in data['cases']:
                        case_id = str(case.get('summary', {}).get('id', ''))
                        if case_id:
                            existing_cases[case_id] = case

        return existing_cases

    def _load_cases_from_zip(self, zip_path: str) -> Dict[str, Any]:
        """从ZIP文件加载工单数据"""
        cases = {}
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                if 'patch_content.json' in zipf.namelist():
                    content_data = json.loads(zipf.read('patch_content.json').decode('utf-8'))
                    cases = content_data.get('cases', {})
        except Exception as e:
            logger.error(f"从ZIP文件加载数据失败: {e}")
        return cases

    def _load_cases_from_directory(self, dir_path: str) -> Dict[str, Any]:
        """从目录加载工单数据"""
        cases = {}
        try:
            content_file = os.path.join(dir_path, 'patch_content.json')
            if os.path.exists(content_file):
                content_data = load_json(content_file)
                if content_data:
                    cases = content_data.get('cases', {})
        except Exception as e:
            logger.error(f"从目录加载数据失败: {e}")
        return cases

    def _has_case_changed(self, new_case: Dict[str, Any],
                         existing_case: Dict[str, Any]) -> bool:
        """检查工单是否有变更"""
        # 比较关键字段
        key_fields = ['summary', 'comments', 'attachments']

        for field in key_fields:
            if new_case.get(field) != existing_case.get(field):
                return True

        return False

    def _save_patch_to_directory(self, patch_content: PatchContent, target_dir: str, case_details: List[CaseDetail] = None):
        """保存Patch到目录"""
        # 保存元数据
        metadata_file = os.path.join(target_dir, 'metadata.json')
        save_json(asdict(patch_content.metadata), metadata_file)

        # 保存完整内容
        content_file = os.path.join(target_dir, 'patch_content.json')
        save_json(asdict(patch_content), content_file)

        # 复制附件文件（使用附件管理器）
        if case_details and config.COPY_ATTACHMENTS_TO_PACKAGE:
            success = self.attachment_manager.copy_attachments_to_package(target_dir, case_details)
            if success:
                logger.info(f"附件已复制到数据包: {target_dir}")
            else:
                logger.warning(f"附件复制失败: {target_dir}")

        # 兼容旧的附件处理方式
        elif patch_content.attachments:
            attachments_dir = os.path.join(target_dir, 'attachments')
            os.makedirs(attachments_dir, exist_ok=True)

            for case_id, file_paths in patch_content.attachments.items():
                case_attachments_dir = os.path.join(attachments_dir, case_id)
                os.makedirs(case_attachments_dir, exist_ok=True)

                for file_path in file_paths:
                    if os.path.exists(file_path):
                        filename = os.path.basename(file_path)
                        target_path = os.path.join(case_attachments_dir, filename)
                        try:
                            shutil.copy2(file_path, target_path)
                        except Exception as e:
                            logger.warning(f"复制附件失败 {file_path}: {e}")

    def _create_zip_package(self, source_dir: str, zip_path: str):
        """创建ZIP包"""
        try:
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(source_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arc_name = os.path.relpath(file_path, source_dir)
                        zipf.write(file_path, arc_name)

            logger.info(f"ZIP包创建成功: {zip_path}")
        except Exception as e:
            logger.error(f"创建ZIP包失败: {e}")
            raise

    def get_latest_version(self) -> Optional[str]:
        """获取最新版本号"""
        versions = []

        # 扫描全量版本
        for item in os.listdir(self.data_dir):
            if item.startswith('full_') and (item.endswith('.zip') or os.path.isdir(os.path.join(self.data_dir, item))):
                version = item.replace('full_', '').replace('.zip', '')
                versions.append(version)

        return max(versions) if versions else None


# 全局实例（延迟初始化）
data_mode_manager = None

def get_data_mode_manager():
    """获取全局DataModeManager实例"""
    global data_mode_manager
    if data_mode_manager is None:
        data_mode_manager = DataModeManager()
    return data_mode_manager
