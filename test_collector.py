#!/usr/bin/env python3
"""
工单采集器测试文件
"""
import unittest
import os
import json
import tempfile
from unittest.mock import Mock, patch, MagicMock
from bs4 import BeautifulSoup

from models import CaseSummary, CaseDetail, CaseComment, CaseAttachment
from case_collector import CaseCollector
from utils import save_json, load_json, get_safe_filename, generate_versioned_filename
import config


class TestCaseCollector(unittest.TestCase):
    """工单采集器测试类"""

    def setUp(self):
        """测试前准备"""
        self.test_dir = tempfile.mkdtemp()
        self.original_data_dir = config.DATA_DIR
        self.original_attachments_dir = config.ATTACHMENTS_DIR

        # 使用临时目录
        config.DATA_DIR = self.test_dir
        config.ATTACHMENTS_DIR = os.path.join(self.test_dir, "attachments")
        config.CASES_DATA_FILE = os.path.join(self.test_dir, "cases_data.json")
        config.LAST_UPDATE_FILE = os.path.join(self.test_dir, "last_update.json")

        os.makedirs(config.ATTACHMENTS_DIR, exist_ok=True)

    def tearDown(self):
        """测试后清理"""
        config.DATA_DIR = self.original_data_dir
        config.ATTACHMENTS_DIR = self.original_attachments_dir

        # 清理临时文件
        import shutil
        shutil.rmtree(self.test_dir, ignore_errors=True)

    def test_case_summary_creation(self):
        """测试工单概要创建"""
        case_data = {
            'id': 7514,
            'case_number': '01747205919',
            'subject': 'MP01 指标说明',
            'product_line': 'MP01',
            'product_name': 'PHY NAME',
            'project_name': 'N001',
            'cnum': 'PADFPA_AS_CoreProcess',
            'product_code': 'S304-0',
            'case_level': 0,
            'issue_type': 1,
            'issue_description': '<p>测试描述</p>',
            'attach': None,
            'priority': 4,
            'version': '2.10B',
            'notify': None,
            'status': 3,
            'uid': 260,
            'deadline': None,
            'is_admin_create': 0,
            'created_at': '2025-05-14 14:58:39',
            'updated_at': '2025-05-15 15:31:15',
            'company_name': 'alicode',
            'username': 'wf w'
        }

        case_summary = CaseSummary(
            id=case_data['id'],
            case_number=case_data['case_number'],
            subject=case_data['subject'],
            product_line=case_data['product_line'],
            product_name=case_data['product_name'],
            project_name=case_data['project_name'],
            cnum=case_data['cnum'],
            product_code=case_data['product_code'],
            case_level=case_data['case_level'],
            issue_type=case_data['issue_type'],
            issue_description=case_data['issue_description'],
            attach=case_data['attach'],
            priority=case_data['priority'],
            version=case_data['version'],
            notify=case_data['notify'],
            status=case_data['status'],
            uid=case_data['uid'],
            deadline=case_data['deadline'],
            is_admin_create=case_data['is_admin_create'],
            created_at=case_data['created_at'],
            updated_at=case_data['updated_at'],
            company_name=case_data['company_name'],
            username=case_data['username']
        )

        self.assertEqual(case_summary.id, 7514)
        self.assertEqual(case_summary.subject, 'MP01 指标说明')
        self.assertEqual(case_summary.username, 'wf w')

    def test_comment_extraction(self):
        """测试评论提取"""
        # 模拟HTML内容
        html_content = """
        <div class="akro-comment">
            <div class="col-md-2">用户A</div>
            <div class="col-md-10">
                <div class="well">
                    这是一条测试评论内容
                    <span>2025-05-14 15:30:00</span>
                </div>
            </div>
        </div>
        <div class="akro-comment">
            <div class="col-md-2">用户B</div>
            <div class="col-md-10">
                <div class="well">
                    这是另一条评论
                    <span>2025-05-15 10:20:00</span>
                </div>
            </div>
        </div>
        """

        soup = BeautifulSoup(html_content, 'html.parser')

        # 创建模拟的采集器
        collector = CaseCollector()
        comments = collector._extract_comments(soup, 7514)

        self.assertEqual(len(comments), 2)
        self.assertEqual(comments[0].source, "用户A")
        self.assertIn("测试评论内容", comments[0].content)
        self.assertEqual(comments[0].timestamp, "2025-05-14 15:30:00")

    def test_attachment_extraction(self):
        """测试附件提取"""
        # 模拟附件表格HTML
        html_content = """
        <div>Attachments</div>
        <table>
            <tr><th>File Name</th><th>File Size</th><th>Owner</th><th>Last Modified</th><th>Action</th></tr>
            <tr>
                <td>test_file.pdf</td>
                <td>1.2 MB</td>
                <td>admin</td>
                <td>2025-05-14 16:00:00</td>
                <td>
                    <div class="dropdown-menu">
                        <a href="/download/123">Download</a>
                        <a onclick="addNotes(456)">Add Notes</a>
                    </div>
                </td>
            </tr>
        </table>
        """

        soup = BeautifulSoup(html_content, 'html.parser')

        collector = CaseCollector()
        attachments = collector._extract_attachments(soup, 7514)

        self.assertEqual(len(attachments), 1)
        self.assertEqual(attachments[0].file_name, "test_file.pdf")
        self.assertEqual(attachments[0].file_size, "1.2 MB")
        self.assertEqual(attachments[0].owner, "admin")
        self.assertEqual(attachments[0].attachment_id, "456")
        self.assertEqual(attachments[0].download_url, "https://akrocare.akrostar-tech.com/download/123")

    def test_safe_filename(self):
        """测试安全文件名生成"""
        unsafe_name = "test<>file|name?.txt"
        safe_name = get_safe_filename(unsafe_name)
        self.assertEqual(safe_name, "test__file_name_.txt")

    def test_versioned_filename(self):
        """测试版本化文件名生成"""
        base_path = os.path.join("test", "path")
        filename = "document.pdf"

        # 版本1
        path1 = generate_versioned_filename(base_path, filename, 1)
        expected1 = os.path.join(base_path, filename)
        self.assertEqual(path1, expected1)

        # 版本2
        path2 = generate_versioned_filename(base_path, filename, 2)
        expected2 = os.path.join(base_path, "document_v2.pdf")
        self.assertEqual(path2, expected2)

    def test_json_operations(self):
        """测试JSON操作"""
        test_data = {"test": "data", "number": 123}
        test_file = os.path.join(self.test_dir, "test.json")

        # 保存JSON
        save_json(test_data, test_file)
        self.assertTrue(os.path.exists(test_file))

        # 加载JSON
        loaded_data = load_json(test_file)
        self.assertEqual(loaded_data, test_data)

        # 加载不存在的文件
        non_existent = load_json("non_existent.json")
        self.assertIsNone(non_existent)

    @patch('case_collector.webdriver.Chrome')
    @patch('case_collector.ChromeDriverManager')
    def test_collector_initialization(self, mock_driver_manager, mock_chrome):
        """测试采集器初始化"""
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver
        mock_driver_manager.return_value.install.return_value = "/fake/path/chromedriver"

        collector = CaseCollector()
        collector._setup_driver()

        self.assertIsNotNone(collector.driver)
        mock_chrome.assert_called_once()

    @patch('case_collector.webdriver.Chrome')
    @patch('case_collector.os.path.exists')
    def test_custom_driver_path(self, mock_exists, mock_chrome):
        """测试自定义ChromeDriver路径"""
        # 模拟自定义路径存在
        mock_exists.return_value = True
        mock_driver = Mock()
        mock_chrome.return_value = mock_driver

        # 临时设置自定义路径
        original_path = config.CHROME_DRIVER_PATH
        config.CHROME_DRIVER_PATH = "/custom/path/chromedriver"

        try:
            collector = CaseCollector()
            collector._setup_driver()

            self.assertIsNotNone(collector.driver)
            mock_chrome.assert_called_once()
            # 验证使用了自定义路径
            mock_exists.assert_called_with("/custom/path/chromedriver")
        finally:
            # 恢复原始配置
            config.CHROME_DRIVER_PATH = original_path

    def test_get_cases_via_requests_structure(self):
        """测试requests方法的基本结构"""
        collector = CaseCollector()

        # 测试在没有driver的情况下调用方法
        result = collector._get_cases_via_requests(1)

        # 应该返回失败的响应结构（因为没有正确的driver和session）
        self.assertIn('code', result)
        self.assertIn('message', result)
        self.assertIn('data', result)

    @patch('case_collector.webdriver.Chrome')
    def test_get_cases_via_selenium(self, mock_chrome):
        """测试通过Selenium获取工单数据"""
        # 模拟WebDriver
        mock_driver = Mock()
        mock_driver.execute_script.return_value = None  # 模拟CSRF token获取
        mock_driver.execute_async_script.return_value = {
            "code": 200,
            "message": "Get Cases Success!",
            "data": {
                "data": [
                    {
                        "id": 456,
                        "case_number": "TEST002",
                        "subject": "Selenium测试工单",
                        "created_at": "2025-01-01 00:00:00",
                        "updated_at": "2025-01-01 00:00:00"
                    }
                ],
                "total": 1,
                "total_pages": 1
            }
        }
        mock_chrome.return_value = mock_driver

        collector = CaseCollector()
        collector.driver = mock_driver
        result = collector._get_cases_via_selenium(1)

        self.assertEqual(result['code'], 200)
        self.assertEqual(len(result['data']['data']), 1)
        mock_driver.execute_async_script.assert_called_once()

    @patch('case_collector.webdriver.Chrome')
    def test_session_headers_update(self, mock_chrome):
        """测试session headers更新功能"""
        # 模拟WebDriver
        mock_driver = Mock()
        mock_driver.execute_script.return_value = "Mozilla/5.0 Test Browser"
        mock_driver.current_url = "https://akrocare.akrostar-tech.com/cases"
        mock_driver.get_cookies.return_value = [
            {'name': 'session_id', 'value': 'test123', 'domain': '.akrocare.akrostar-tech.com', 'path': '/'},
            {'name': 'csrf_token', 'value': 'token456', 'domain': '.akrocare.akrostar-tech.com', 'path': '/'}
        ]
        mock_chrome.return_value = mock_driver

        collector = CaseCollector()
        collector.driver = mock_driver
        collector._update_session_headers()

        # 验证headers是否正确设置
        self.assertIn('User-Agent', collector.session.headers)
        self.assertIn('X-Requested-With', collector.session.headers)
        self.assertEqual(collector.session.headers['User-Agent'], "Mozilla/5.0 Test Browser")

        # 验证cookies是否正确同步
        self.assertEqual(len(collector.session.cookies), 2)
        self.assertEqual(collector.cookies['session_id'], 'test123')
        self.assertEqual(collector.cookies['csrf_token'], 'token456')

    @patch('case_collector.webdriver.Chrome')
    def test_csrf_token_extraction(self, mock_chrome):
        """测试CSRF token提取功能"""
        # 模拟WebDriver
        mock_driver = Mock()
        mock_driver.execute_script.side_effect = [
            "test_csrf_token_123",  # 第一次调用返回CSRF token
            None  # 第二次调用返回None
        ]
        mock_chrome.return_value = mock_driver

        collector = CaseCollector()
        collector.driver = mock_driver

        # 测试成功获取CSRF token
        token = collector._get_csrf_token()
        self.assertEqual(token, "test_csrf_token_123")

        # 测试获取失败的情况
        token = collector._get_csrf_token()
        self.assertEqual(token, "")

    def test_extract_comments_from_demo_html(self):
        """测试从demo-content.html提取评论"""
        # 加载demo HTML文件
        try:
            with open('demo-content.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
        except FileNotFoundError:
            self.skipTest("demo-content.html文件不存在")
            return

        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')

        collector = CaseCollector()
        comments = collector._extract_comments(soup, 5766)

        # 验证提取结果
        self.assertGreater(len(comments), 0, "应该能够提取到评论")

        # 验证评论内容
        for comment in comments:
            self.assertIsNotNone(comment.source, "评论来源不应为空")
            self.assertIsNotNone(comment.content, "评论内容不应为空")
            self.assertIsNotNone(comment.timestamp, "评论时间不应为空")

        # 打印提取结果用于验证
        print(f"\n从demo-content.html提取到 {len(comments)} 条评论:")
        for i, comment in enumerate(comments, 1):
            print(f"  评论{i}: {comment.source} - {comment.timestamp}")
            print(f"    内容: {comment.content[:100]}...")

    def test_extract_comments_fallback_method(self):
        """测试评论提取的降级方法"""
        # 创建一个没有akro-comment类但有Reply Content的HTML
        html_content = """
        <html>
        <body>
            <h1>Reply Content</h1>
            <div>
                <div class="col-md-2">测试用户</div>
                <div class="col-md-10 well">
                    这是一条测试评论内容
                    <hr>
                    <span>2025-01-01 12:00:00</span>
                </div>
            </div>
        </body>
        </html>
        """

        from bs4 import BeautifulSoup
        soup = BeautifulSoup(html_content, 'html.parser')

        collector = CaseCollector()
        comments = collector._extract_comments(soup, 123)

        # 验证降级方法能够工作
        self.assertEqual(len(comments), 1)
        self.assertEqual(comments[0].source, "测试用户")
        self.assertEqual(comments[0].timestamp, "2025-01-01 12:00:00")
        self.assertIn("测试评论内容", comments[0].content)

    def test_case_detail_to_dict(self):
        """测试工单详情转换为字典"""
        # 创建测试数据
        case_summary = CaseSummary(
            id=123, case_number="TEST001", subject="测试工单",
            product_line="TEST", product_name="测试产品", project_name="测试项目",
            cnum="TEST_CNUM", product_code="T001", case_level=1, issue_type=1,
            issue_description="测试描述", attach=None, priority=3, version="1.0",
            notify=None, status=1, uid=100, deadline=None, is_admin_create=0,
            created_at="2025-01-01 00:00:00", updated_at="2025-01-01 00:00:00",
            company_name="测试公司", username="测试用户"
        )

        comment = CaseComment(
            source="测试用户",
            content="测试评论",
            timestamp="2025-01-01 00:00:00"
        )

        attachment = CaseAttachment(
            attachment_id="1",
            file_name="test.pdf",
            file_size="1MB",
            owner="admin",
            last_modified="2025-01-01 00:00:00",
            download_url="http://test.com/download"
        )

        case_detail = CaseDetail(
            summary=case_summary,
            comments=[comment],
            attachments=[attachment]
        )

        # 转换为字典
        result_dict = case_detail.to_dict()

        # 验证结构
        self.assertIn('summary', result_dict)
        self.assertIn('comments', result_dict)
        self.assertIn('attachments', result_dict)
        self.assertEqual(len(result_dict['comments']), 1)
        self.assertEqual(len(result_dict['attachments']), 1)
        self.assertEqual(result_dict['summary']['id'], 123)

    def test_comment_extraction_with_real_data(self):
        """使用真实数据测试评论提取"""
        # 加载真实的评论数据
        try:
            with open('test_data/demo_comments.json', 'r', encoding='utf-8') as f:
                expected_comments = json.load(f)
        except FileNotFoundError:
            self.skipTest("test_data/demo_comments.json文件不存在")
            return

        # 加载demo HTML文件
        try:
            with open('demo-content.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
        except FileNotFoundError:
            self.skipTest("demo-content.html文件不存在")
            return

        soup = BeautifulSoup(html_content, 'html.parser')

        # 临时禁用增量更新
        original_incremental = config.ENABLE_INCREMENTAL_UPDATE
        config.ENABLE_INCREMENTAL_UPDATE = False

        try:
            collector = CaseCollector()
            comments = collector._extract_comments(soup, 5766)

            # 验证评论数量
            self.assertEqual(len(comments), len(expected_comments))

            # 验证每条评论的内容
            for i, comment in enumerate(comments):
                expected = expected_comments[i]
                self.assertEqual(comment.source, expected['source'])
                self.assertEqual(comment.timestamp, expected['timestamp'])
                # 验证内容的前50个字符匹配
                self.assertIn(expected['content'][:50], comment.content)

        finally:
            config.ENABLE_INCREMENTAL_UPDATE = original_incremental

    def test_attachment_extraction_with_real_data(self):
        """使用真实数据测试附件提取"""
        # 加载真实的附件数据
        try:
            with open('test_data/demo_attachments.json', 'r', encoding='utf-8') as f:
                expected_attachments = json.load(f)
        except FileNotFoundError:
            self.skipTest("test_data/demo_attachments.json文件不存在")
            return

        # 加载demo HTML文件
        try:
            with open('demo-content.html', 'r', encoding='utf-8') as f:
                html_content = f.read()
        except FileNotFoundError:
            self.skipTest("demo-content.html文件不存在")
            return

        soup = BeautifulSoup(html_content, 'html.parser')

        collector = CaseCollector()
        attachments = collector._extract_attachments(soup, 5766)

        # 验证附件数量
        self.assertEqual(len(attachments), len(expected_attachments))

        # 验证每个附件的信息
        for i, attachment in enumerate(attachments):
            expected = expected_attachments[i]
            self.assertEqual(attachment.file_name, expected['file_name'])
            self.assertEqual(attachment.file_size, expected['file_size'])
            self.assertEqual(attachment.owner, expected['owner'])

    def test_case_summary_with_real_data(self):
        """使用真实数据测试工单概要"""
        # 加载真实的工单概要数据
        try:
            with open('test_data/demo_case_summary.json', 'r', encoding='utf-8') as f:
                expected_summary = json.load(f)
        except FileNotFoundError:
            self.skipTest("test_data/demo_case_summary.json文件不存在")
            return

        # 创建CaseSummary对象
        case_summary = CaseSummary(
            id=expected_summary['id'],
            case_number=expected_summary['case_number'],
            subject=expected_summary['subject'],
            product_line=expected_summary['product_line'],
            product_name=expected_summary['product_name'],
            project_name=expected_summary['project_name'],
            cnum=expected_summary['cnum'],
            product_code=expected_summary['product_code'],
            case_level=expected_summary['case_level'],
            issue_type=expected_summary['issue_type'],
            issue_description=expected_summary['issue_description'],
            attach=expected_summary['attach'],
            priority=expected_summary['priority'],
            version=expected_summary['version'],
            notify=expected_summary['notify'],
            status=expected_summary['status'],
            uid=expected_summary['uid'],
            deadline=expected_summary['deadline'],
            is_admin_create=expected_summary['is_admin_create'],
            created_at=expected_summary['created_at'],
            updated_at=expected_summary['updated_at'],
            company_name=expected_summary['company_name'],
            username=expected_summary['username']
        )

        # 验证关键字段
        self.assertEqual(case_summary.id, 5766)
        self.assertEqual(case_summary.case_number, "01733361085")
        self.assertEqual(case_summary.subject, "ddr phy 手册寄存器疑问汇总")
        self.assertEqual(case_summary.product_line, "DDR4/3")
        self.assertEqual(case_summary.username, "wf w")
        self.assertEqual(case_summary.company_name, "Netforward")

    def test_complete_case_detail_with_real_data(self):
        """使用真实数据测试完整工单详情"""
        # 加载完整的工单数据
        try:
            with open('test_data/demo_case_detail.json', 'r', encoding='utf-8') as f:
                case_data = json.load(f)
        except FileNotFoundError:
            self.skipTest("test_data/demo_case_detail.json文件不存在")
            return

        # 验证数据结构
        self.assertIn('summary', case_data)
        self.assertIn('comments', case_data)
        self.assertIn('attachments', case_data)

        # 验证工单概要
        summary = case_data['summary']
        self.assertEqual(summary['id'], 5766)
        self.assertEqual(summary['case_number'], "01733361085")
        self.assertEqual(summary['subject'], "ddr phy 手册寄存器疑问汇总")

        # 验证评论数据
        comments = case_data['comments']
        self.assertEqual(len(comments), 4)
        self.assertEqual(comments[0]['source'], "AkroStar Reply:")
        self.assertEqual(comments[0]['timestamp'], "2025-03-05 10:14:35")

        # 验证附件数据
        attachments = case_data['attachments']
        self.assertEqual(len(attachments), 0)  # demo数据中没有附件


def run_tests():
    """运行测试"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()
