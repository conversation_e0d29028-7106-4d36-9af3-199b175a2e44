"""
工单采集器核心类
"""
import os
import json
import time
import requests
from datetime import datetime
from typing import List, Dict, Any, Optional
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from webdriver_manager.chrome import ChromeDriverManager
from bs4 import BeautifulSoup
import re

from models import CaseSummary, CaseDetail, CaseComment, CaseAttachment, CollectionState
from utils import (
    save_json, load_json, download_file, get_safe_filename,
    generate_versioned_filename, parse_datetime, retry_on_failure,
    clean_html_content, format_file_size, logger
)
import config
from raw_data_manager import get_raw_data_manager
from attachment_manager import get_attachment_manager


class CaseCollector:
    """工单采集器"""

    def __init__(self):
        self.driver = None
        self.session = requests.Session()
        # 根据配置决定是否禁用代理
        if config.DISABLE_PROXY:
            self.session.proxies = {
                'http': None,
                'https': None
            }
            logger.info("已禁用代理")
        self.cookies = {}
        self.collection_state = self._load_collection_state()

        # 初始化附件管理器
        self.attachment_manager = get_attachment_manager()

    def _load_collection_state(self) -> CollectionState:
        """加载采集状态"""
        state_data = load_json(config.LAST_UPDATE_FILE)
        if state_data:
            return CollectionState(
                last_update_time=state_data.get('last_update_time', ''),
                collected_case_ids=state_data.get('collected_case_ids', [])
            )
        return CollectionState(last_update_time='')

    def _save_collection_state(self):
        """保存采集状态"""
        save_json(self.collection_state.to_dict(), config.LAST_UPDATE_FILE)

    def _setup_driver(self):
        """设置Chrome WebDriver"""
        try:
            chrome_options = Options()
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--window-size=1920,1080')
            # chrome_options.add_argument('--headless')  # 如需无头模式，取消注释

            # 根据配置选择ChromeDriver路径
            if config.CHROME_DRIVER_PATH and os.path.exists(config.CHROME_DRIVER_PATH):
                # 使用指定的ChromeDriver路径
                service = Service(config.CHROME_DRIVER_PATH)
                logger.info(f"使用指定的ChromeDriver路径: {config.CHROME_DRIVER_PATH}")
            else:
                # 自动下载ChromeDriver
                service = Service(ChromeDriverManager().install())
                logger.info("使用自动下载的ChromeDriver")

            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_page_load_timeout(config.PAGE_LOAD_TIMEOUT)
            self.driver.implicitly_wait(config.IMPLICIT_WAIT)

            logger.info("WebDriver初始化成功")
        except Exception as e:
            logger.error(f"WebDriver初始化失败: {e}")
            raise

    def _update_session_headers(self):
        """更新session的headers信息"""
        try:
            # 获取当前页面的User-Agent
            user_agent = self.driver.execute_script("return navigator.userAgent;")

            # 获取当前页面的URL作为Referer
            current_url = self.driver.current_url

            # 获取页面中可能存在的CSRF token
            csrf_token = self._get_csrf_token()

            # 构建完整的headers
            headers = {
                'User-Agent': user_agent,
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
                'Referer': current_url,
                'Origin': 'https://akrocare.akrostar-tech.com',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'X-Requested-With': 'XMLHttpRequest'
            }

            # 如果有CSRF token，添加到headers中
            if csrf_token:
                headers['X-CSRF-TOKEN'] = csrf_token

            # 更新session的headers
            self.session.headers.update(headers)

            # 确保cookies已同步
            self._sync_cookies()

            logger.info("Session headers和cookies已更新")
            logger.debug(f"当前cookies: {list(self.session.cookies.keys())}")

        except Exception as e:
            logger.warning(f"更新session headers失败: {e}")

    def _get_csrf_token(self) -> str:
        """获取CSRF token"""
        try:
            # 尝试从meta标签获取CSRF token
            csrf_token = self.driver.execute_script("""
                var meta = document.querySelector('meta[name="csrf-token"]');
                return meta ? meta.getAttribute('content') : null;
            """)

            if csrf_token:
                return csrf_token

            # 尝试从其他可能的位置获取token
            csrf_token = self.driver.execute_script("""
                return window._token || window.Laravel ? window.Laravel.csrfToken : null;
            """)

            return csrf_token or ""

        except Exception as e:
            logger.debug(f"获取CSRF token失败: {e}")
            return ""

    def _sync_cookies(self):
        """同步Selenium和requests的cookies"""
        try:
            # 从Selenium获取最新的cookies
            selenium_cookies = self.driver.get_cookies()

            # 清除session中的旧cookies
            self.session.cookies.clear()

            # 添加新的cookies
            for cookie in selenium_cookies:
                self.session.cookies.set(
                    cookie['name'],
                    cookie['value'],
                    domain=cookie.get('domain'),
                    path=cookie.get('path', '/'),
                    secure=cookie.get('secure', False)
                )

            # 更新实例变量中的cookies
            self.cookies = {cookie['name']: cookie['value'] for cookie in selenium_cookies}

            logger.debug(f"同步了 {len(selenium_cookies)} 个cookies")

        except Exception as e:
            logger.warning(f"同步cookies失败: {e}")

    @retry_on_failure(max_retries=config.MAX_RETRIES, delay=config.RETRY_DELAY)
    def login(self) -> bool:
        """登录系统"""
        try:
            logger.info("开始登录...")
            self.driver.get(config.LOGIN_URL)

            # 等待页面加载
            wait = WebDriverWait(self.driver, config.WEBDRIVER_TIMEOUT)

            # 输入邮箱
            email_input = wait.until(EC.presence_of_element_located((By.ID, "email")))
            email_input.clear()
            email_input.send_keys(config.EMAIL)

            # 输入密码
            password_input = self.driver.find_element(By.ID, "password")
            password_input.clear()
            password_input.send_keys(config.PASSWORD)

            # 点击登录按钮
            submit_button = self.driver.find_element(By.CSS_SELECTOR, "button[type='submit']")
            submit_button.click()

            # 等待登录结果
            time.sleep(3)

            # 检查是否登录成功
            try:
                # 检查是否有错误提示
                error_element = self.driver.find_element(By.CLASS_NAME, "help-block")
                if error_element.is_displayed():
                    logger.error(f"登录失败: {error_element.text}")
                    return False
            except NoSuchElementException:
                pass

            # 检查是否显示欢迎信息
            try:
                welcome_text = wait.until(
                    EC.presence_of_element_located((By.XPATH, "//*[contains(text(), 'Welcome to AkroCare System!')]"))
                )
                logger.info("登录成功")

                # 保存cookies和headers
                self.cookies = {cookie['name']: cookie['value'] for cookie in self.driver.get_cookies()}
                self.session.cookies.update(self.cookies)

                # 获取当前页面的headers信息
                self._update_session_headers()

                logger.info(f"登录成功，已保存 {len(self.cookies)} 个cookies")

                return True
            except TimeoutException:
                logger.error("登录失败：未找到欢迎信息")
                return False

        except Exception as e:
            logger.error(f"登录过程中发生错误: {e}")
            return False

    def navigate_to_cases(self) -> bool:
        """导航到工单页面"""
        try:
            logger.info("导航到工单页面...")

            # 点击Cases链接
            wait = WebDriverWait(self.driver, config.WEBDRIVER_TIMEOUT)
            cases_link = wait.until(EC.element_to_be_clickable((By.LINK_TEXT, "Cases")))
            cases_link.click()

            # 等待页面加载
            time.sleep(3)

            # 更新session信息，确保在工单页面的鉴权信息是最新的
            self._update_session_headers()

            logger.info("成功导航到工单页面，session信息已更新")
            return True

        except Exception as e:
            logger.error(f"导航到工单页面失败: {e}")
            return False

    def get_cases_summary(self) -> List[CaseSummary]:
        """获取工单概要信息"""
        # 指定工单ID模式：从工单列表中筛选指定ID的工单
        if config.SPECIFIC_CASE_IDS:
            logger.info(f"指定工单ID模式，从工单列表中筛选指定ID: {config.SPECIFIC_CASE_IDS}")
            return self._get_specific_cases_from_list(config.SPECIFIC_CASE_IDS)

        all_cases = []
        page = 1
        collected_count = 0

        try:
            # 如果需要采集两种状态，分别处理
            status_list = []
            if config.COLLECT_BOTH_STATUS:
                status_list = [config.CASE_STATUS_OPEN, config.CASE_STATUS_CLOSED]
                logger.info(f"将采集两种状态的工单: Open({config.CASE_STATUS_OPEN}), Closed({config.CASE_STATUS_CLOSED})")
            else:
                status_list = [config.CASE_STATUS_CLOSED]  # 默认只采集已关闭的
                logger.info(f"只采集已关闭状态的工单: Closed({config.CASE_STATUS_CLOSED})")

            for status in status_list:
                logger.info(f"开始采集状态为 {status} 的工单...")
                page = 1

                while True:
                    logger.info(f"正在获取状态 {status} 第 {page} 页工单数据...")

                    # 根据配置选择请求方式
                    if config.API_REQUEST_METHOD == 'selenium':
                        data = self._get_cases_via_selenium(page, status)
                    else:
                        data = self._get_cases_via_requests(page, status)

                    # 保存原始响应数据
                    get_raw_data_manager().save_cases_list_response(page, data, str(status))

                    if not data or data.get('code') != 200:
                        if not data:
                            logger.error("API请求失败：无响应数据")
                        else:
                            logger.error(f"API返回错误: {data.get('message', '未知错误')}")

                        # 如果requests方式失败，尝试selenium方式
                        if config.API_REQUEST_METHOD == 'requests':
                            logger.info("尝试使用Selenium方式重新请求...")
                            data = self._get_cases_via_selenium(page, status)
                            if not data or data.get('code') != 200:
                                break
                        else:
                            break

                    cases_data = data.get('data', {})
                    cases_list = cases_data.get('data', [])
                    total_pages = cases_data.get('total_pages', 0)

                    if not cases_list:
                        logger.info(f"状态 {status} 没有更多工单数据")
                        break

                    # 解析工单数据
                    page_cases = []
                    for case_data in cases_list:
                        try:
                            case_summary = CaseSummary(
                                id=case_data.get('id'),
                                case_number=case_data.get('case_number', ''),
                                subject=case_data.get('subject', ''),
                                product_line=case_data.get('product_line', ''),
                                product_name=case_data.get('product_name', ''),
                                project_name=case_data.get('project_name', ''),
                                cnum=case_data.get('cnum', ''),
                                product_code=case_data.get('product_code', ''),
                                case_level=case_data.get('case_level', 0),
                                issue_type=case_data.get('issue_type', 0),
                                issue_description=case_data.get('issue_description', ''),
                                attach=case_data.get('attach'),
                                priority=case_data.get('priority', 0),
                                version=case_data.get('version', ''),
                                notify=case_data.get('notify'),
                                status=case_data.get('status', 0),
                                uid=case_data.get('uid', 0),
                                deadline=case_data.get('deadline'),
                                is_admin_create=case_data.get('is_admin_create', 0),
                                created_at=case_data.get('created_at', ''),
                                updated_at=case_data.get('updated_at', ''),
                                company_name=case_data.get('company_name', ''),
                                username=case_data.get('username', '')
                            )

                            # 应用各种过滤条件
                            if self._should_skip_case_by_filters(case_summary):
                                continue

                            page_cases.append(case_summary)
                            collected_count += 1

                            # 检查是否达到限制
                            if self._should_stop_collection(collected_count):
                                logger.info(f"达到采集限制，停止采集")
                                all_cases.extend(page_cases)
                                return all_cases

                        except Exception as e:
                            logger.error(f"解析工单数据失败: {e}")
                            continue

                    all_cases.extend(page_cases)
                    logger.info(f"状态 {status} 第 {page} 页获取到 {len(page_cases)} 条有效工单")

                    # 检查是否还有更多页面
                    if page >= total_pages:
                        break

                    page += 1
                    time.sleep(1)  # 避免请求过快

                logger.info(f"状态 {status} 采集完成，共 {len([c for c in all_cases if c.status == status])} 条工单")

        except Exception as e:
            logger.error(f"获取工单概要信息失败: {e}")

        logger.info(f"总共获取到 {len(all_cases)} 条工单概要信息")
        return all_cases

    def _should_skip_case(self, case_summary: CaseSummary) -> bool:
        """判断是否应该跳过该工单（增量更新逻辑）"""
        if not config.ENABLE_INCREMENTAL_UPDATE:
            return False

        # 如果是新工单，不跳过
        if case_summary.id not in self.collection_state.collected_case_ids:
            return False

        # 如果有更新时间且比上次采集时间新，不跳过
        if (self.collection_state.last_update_time and
            case_summary.updated_at > self.collection_state.last_update_time):
            return False

        return True

    def _get_specific_cases_from_list(self, case_ids: List[int]) -> List[CaseSummary]:
        """从工单列表中获取指定ID的工单概要信息"""
        specific_cases = []
        found_case_ids = set()

        try:
            logger.info(f"开始从工单列表中查找指定工单ID: {case_ids}")

            # 需要搜索的状态列表
            status_list = []
            if config.COLLECT_BOTH_STATUS:
                status_list = [config.CASE_STATUS_OPEN, config.CASE_STATUS_CLOSED]
                logger.info(f"将在两种状态中搜索: Open({config.CASE_STATUS_OPEN}), Closed({config.CASE_STATUS_CLOSED})")
            else:
                status_list = [config.CASE_STATUS_CLOSED]
                logger.info(f"将在Closed状态中搜索: ({config.CASE_STATUS_CLOSED})")

            # 在每种状态中搜索指定工单
            for status in status_list:
                if len(found_case_ids) == len(case_ids):
                    break  # 已找到所有指定工单

                logger.info(f"在状态 {status} 中搜索指定工单...")
                page = 1

                while len(found_case_ids) < len(case_ids):
                    logger.info(f"搜索状态 {status} 第 {page} 页...")

                    # 根据配置选择请求方式
                    if config.API_REQUEST_METHOD == 'selenium':
                        data = self._get_cases_via_selenium(page, status)
                    else:
                        data = self._get_cases_via_requests(page, status)

                    # 保存原始响应数据
                    get_raw_data_manager().save_cases_list_response(page, data, str(status))

                    if not data or data.get('code') != 200:
                        if not data:
                            logger.error("API请求失败：无响应数据")
                        else:
                            logger.error(f"API返回错误: {data.get('message', '未知错误')}")

                        # 如果requests方式失败，尝试selenium方式
                        if config.API_REQUEST_METHOD == 'requests':
                            logger.info("尝试使用Selenium方式重新请求...")
                            data = self._get_cases_via_selenium(page, status)
                            if not data or data.get('code') != 200:
                                break
                        else:
                            break

                    cases_data = data.get('data', {})
                    cases_list = cases_data.get('data', [])
                    total_pages = cases_data.get('total_pages', 0)

                    if not cases_list:
                        logger.info(f"状态 {status} 第 {page} 页没有更多工单数据")
                        break

                    # 在当前页中查找指定工单
                    for case_data in cases_list:
                        try:
                            case_id = case_data.get('id')
                            if case_id in case_ids and case_id not in found_case_ids:
                                # 找到指定工单，创建概要对象
                                case_summary = CaseSummary(
                                    id=case_data.get('id'),
                                    case_number=case_data.get('case_number', ''),
                                    subject=case_data.get('subject', ''),
                                    product_line=case_data.get('product_line', ''),
                                    product_name=case_data.get('product_name', ''),
                                    project_name=case_data.get('project_name', ''),
                                    cnum=case_data.get('cnum', ''),
                                    product_code=case_data.get('product_code', ''),
                                    case_level=case_data.get('case_level', 0),
                                    issue_type=case_data.get('issue_type', 0),
                                    issue_description=case_data.get('issue_description', ''),
                                    attach=case_data.get('attach'),
                                    priority=case_data.get('priority', 0),
                                    version=case_data.get('version', ''),
                                    notify=case_data.get('notify'),
                                    status=case_data.get('status', 0),
                                    uid=case_data.get('uid', 0),
                                    deadline=case_data.get('deadline'),
                                    is_admin_create=case_data.get('is_admin_create', 0),
                                    created_at=case_data.get('created_at', ''),
                                    updated_at=case_data.get('updated_at', ''),
                                    company_name=case_data.get('company_name', ''),
                                    username=case_data.get('username', '')
                                )

                                specific_cases.append(case_summary)
                                found_case_ids.add(case_id)
                                logger.info(f"找到指定工单 {case_id}: {case_summary.subject}")

                        except Exception as e:
                            logger.error(f"解析工单数据失败: {e}")
                            continue

                    # 检查是否还有更多页面
                    if page >= total_pages:
                        break

                    page += 1
                    time.sleep(1)  # 避免请求过快

            # 检查是否找到了所有指定工单
            missing_case_ids = set(case_ids) - found_case_ids
            if missing_case_ids:
                logger.warning(f"未找到以下指定工单ID: {list(missing_case_ids)}")

            logger.info(f"从工单列表中找到 {len(specific_cases)} 个指定工单")

        except Exception as e:
            logger.error(f"从工单列表获取指定工单失败: {e}")

        return specific_cases

    def _should_skip_case_by_filters(self, case_summary: CaseSummary) -> bool:
        """根据各种过滤条件判断是否跳过工单"""
        # 增量更新检查
        if config.ENABLE_INCREMENTAL_UPDATE and self._should_skip_case(case_summary):
            return True

        return False

    def _should_stop_collection(self, collected_count: int) -> bool:
        """判断是否应该停止采集"""
        # 检查最新N条限制
        if config.LATEST_N_CASES > 0 and collected_count >= config.LATEST_N_CASES:
            return True

        # 检查增量N条限制
        if config.INCREMENTAL_N_CASES > 0 and collected_count >= config.INCREMENTAL_N_CASES:
            return True

        return False

    def _get_cases_via_requests(self, page: int = 1, status: int = None) -> dict:
        """使用requests session获取工单数据"""
        try:
            # 在发送请求前，确保cookies和headers是最新的
            self._sync_cookies()

            # 构建请求数据
            request_data = {
                "status": status if status is not None else config.CASE_STATUS_CLOSED,
                "filter": [],
                "page": page,
                "page_size": config.PAGE_SIZE,
                "sort": []
            }

            # 构建请求headers，合并session headers和特定headers
            request_headers = self.session.headers.copy()
            request_headers.update({
                'Content-Type': 'application/json'
            })

            logger.debug(f"发送POST请求到: {config.CASES_API_URL}")
            logger.debug(f"请求数据: {request_data}")
            logger.debug(f"请求headers: {dict(request_headers)}")
            logger.debug(f"Cookies数量: {len(self.session.cookies)}")

            # 保存API请求信息
            get_raw_data_manager().save_api_request_info(
                config.CASES_API_URL,
                "POST",
                dict(request_headers),
                request_data
            )

            response = self.session.post(
                config.CASES_API_URL,
                json=request_data,
                headers=request_headers,
                timeout=30
            )

            logger.debug(f"响应状态码: {response.status_code}")
            logger.debug(f"响应headers: {dict(response.headers)}")

            if response.status_code != 200:
                logger.error(f"Requests API请求失败: {response.status_code}")
                logger.error(f"响应内容: {response.text[:500]}")
                logger.error(f"请求URL: {response.url}")
                logger.error(f"请求headers: {dict(response.request.headers)}")

                # 保存失败的响应状态
                get_raw_data_manager().save_api_request_info(
                    config.CASES_API_URL,
                    "POST",
                    dict(request_headers),
                    request_data,
                    response.status_code
                )
                return {}

            result = response.json()
            logger.debug(f"API响应: {result.get('code', 'unknown')} - {result.get('message', 'no message')}")

            return result

        except Exception as e:
            logger.error(f"Requests POST请求失败: {e}")
            logger.error(f"请求URL: {config.CASES_API_URL}")
            return {}

    def _get_cases_via_selenium(self, page: int = 1, status: int = None) -> dict:
        """使用Selenium直接执行POST请求获取工单数据"""
        try:
            # 构建请求数据
            request_data = {
                "status": status if status is not None else config.CASE_STATUS_CLOSED,
                "filter": [],
                "page": page,
                "page_size": config.PAGE_SIZE,
                "sort": []
            }

            # 获取CSRF token
            csrf_token = self._get_csrf_token()

            # 构建完整的headers
            headers = {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json, text/javascript, */*; q=0.01'
            }

            # 如果有CSRF token，添加到headers中
            if csrf_token:
                headers['X-CSRF-TOKEN'] = csrf_token

            logger.debug(f"Selenium发送POST请求到: {config.CASES_API_URL}")
            logger.debug(f"请求数据: {request_data}")
            logger.debug(f"请求headers: {headers}")

            # 使用Selenium执行JavaScript进行POST请求
            # 这种方式会自动携带浏览器中的所有cookies
            script = f"""
            return new Promise((resolve, reject) => {{
                const requestData = {json.dumps(request_data)};
                const headers = {json.dumps(headers)};

                console.log('发送POST请求:', '{config.CASES_API_URL}');
                console.log('请求数据:', requestData);
                console.log('请求headers:', headers);

                fetch('{config.CASES_API_URL}', {{
                    method: 'POST',
                    headers: headers,
                    body: JSON.stringify(requestData),
                    credentials: 'same-origin'  // 确保携带cookies
                }})
                .then(response => {{
                    console.log('响应状态:', response.status);
                    console.log('响应headers:', Object.fromEntries(response.headers.entries()));

                    if (!response.ok) {{
                        throw new Error(`HTTP error! status: ${{response.status}}`);
                    }}

                    return response.json();
                }})
                .then(data => {{
                    console.log('API响应:', data);
                    resolve(data);
                }})
                .catch(error => {{
                    console.error('请求失败:', error);
                    reject(error.toString());
                }});
            }});
            """

            # 执行JavaScript并获取结果
            result = self.driver.execute_async_script(script)

            logger.debug(f"Selenium API响应: {result.get('code', 'unknown') if isinstance(result, dict) else 'invalid response'}")

            return result if isinstance(result, dict) else {}

        except Exception as e:
            logger.error(f"Selenium POST请求失败: {e}")
            return {}

    def get_case_detail(self, case_summary: CaseSummary) -> Optional[CaseDetail]:
        """获取工单详细信息"""
        try:
            case_id = case_summary.id

            # 打印工单信息，增加分隔线
            logger.info(f"=" * 80)
            logger.info(f"正在获取工单 {case_id} 的详细信息...")
            logger.info(f"产品线: {case_summary.product_line}")
            logger.info(f"工单标题: {case_summary.subject}")
            logger.info(f"-" * 80)

            # 构建详情页面URL
            detail_url = config.CASE_DETAIL_URL_TEMPLATE.format(case_id)
            self.driver.get(detail_url)

            # 等待页面加载
            time.sleep(3)

            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            # 保存原始HTML页面
            get_raw_data_manager().save_html_page(case_id, page_source, "detail")

            # 创建工单详情对象
            case_detail = CaseDetail(summary=case_summary)

            # 采集评论信息
            case_detail.comments = self._extract_comments(soup, case_id)

            # 采集附件信息
            case_detail.attachments = self._extract_attachments(soup, case_id)

            # 保存附件信息到原始数据
            if case_detail.attachments:
                attachment_data = [att.__dict__ for att in case_detail.attachments]
                get_raw_data_manager().save_attachment_info(case_id, attachment_data)

            # 使用附件管理器处理附件和图片
            if config.ENABLE_SESSION_ATTACHMENTS:
                # 使用新的附件管理器，传递基础URL
                base_url = getattr(config, 'BASE_URL', None)
                case_detail = self.attachment_manager.process_case_detail(
                    case_detail, self.cookies, self.session.headers, base_url
                )
                logger.info(f"工单 {case_id} 附件和图片处理完成")
            else:
                # 使用原有的下载方式（兼容模式）
                has_content_to_download = (
                    case_detail.attachments or
                    self._has_images_in_content(case_detail)
                )

                if has_content_to_download:
                    # 下载附件
                    self._download_attachments(case_detail.attachments, case_id)

                    # 下载页面中的图片
                    self._download_images_from_content(case_detail, case_id)
                else:
                    logger.info(f"工单 {case_id} 没有附件或图片，跳过目录创建")

            # 保存工单详情的原始数据
            case_detail_data = case_detail.to_dict()
            get_raw_data_manager().save_case_detail_response(case_id, case_detail_data)

            logger.info(f"工单 {case_id} 详细信息采集完成")
            logger.info(f"=" * 80)
            return case_detail

        except Exception as e:
            logger.error(f"获取工单 {case_summary.id} 详细信息失败: {e}")
            logger.info(f"=" * 80)
            return None

    def _has_images_in_content(self, case_detail: CaseDetail) -> bool:
        """检查工单内容中是否包含图片"""
        try:
            # 检查工单描述中的图片
            if case_detail.summary.issue_description:
                soup = BeautifulSoup(case_detail.summary.issue_description, 'html.parser')
                if soup.find_all('img'):
                    return True

            # 检查评论中的图片
            for comment in case_detail.comments:
                if comment.content:
                    soup = BeautifulSoup(comment.content, 'html.parser')
                    if soup.find_all('img'):
                        return True

            return False
        except Exception:
            return False

    def _extract_comments(self, soup: BeautifulSoup, case_id: int) -> List[CaseComment]:
        """提取工单评论"""
        comments = []

        try:
            # 方法1: 查找akro-comment容器（原始方法）
            comment_containers = soup.find_all(class_="akro-comment")
            logger.debug(f"方法1: 找到 {len(comment_containers)} 个akro-comment容器")

            for container in comment_containers:
                try:
                    # 提取评论来源
                    source_element = container.find(class_="col-md-2")
                    source = source_element.get_text(strip=True) if source_element else "Unknown"

                    # 提取评论内容和时间
                    content_element = container.find(class_="col-md-10")
                    if content_element:
                        well_element = content_element.find(class_="well")
                        if well_element:
                            # 提取时间（通常在最后的span中）
                            time_spans = well_element.find_all('span')
                            timestamp = ""
                            if time_spans:
                                timestamp = time_spans[-1].get_text(strip=True)

                            # 移除时间span后获取内容
                            if time_spans:
                                time_spans[-1].decompose()

                            content = well_element.get_text(strip=True)

                            # 增量更新检查
                            if config.ENABLE_INCREMENTAL_UPDATE and self._should_skip_comment(timestamp):
                                continue

                            comment = CaseComment(
                                source=source,
                                content=content,
                                timestamp=timestamp
                            )
                            comments.append(comment)

                except Exception as e:
                    logger.error(f"解析评论失败: {e}")
                    continue

            # 如果方法1没有提取到评论，尝试方法2: 查找Reply Content区域
            if not comments:
                logger.debug("方法1未提取到评论，尝试方法2")
                comments = self._extract_comments_from_reply_section(soup, case_id)

        except Exception as e:
            logger.error(f"提取工单 {case_id} 评论失败: {e}")

        logger.info(f"工单 {case_id} 提取到 {len(comments)} 条评论")
        return comments

    def _extract_comments_from_reply_section(self, soup: BeautifulSoup, case_id: int) -> List[CaseComment]:
        """从Reply Content区域提取评论"""
        comments = []

        try:
            # 查找Reply Content区域
            reply_section = soup.find('h1', string=lambda text: text and 'Reply Content' in text)
            if not reply_section:
                logger.debug("未找到Reply Content区域")
                return comments

            logger.debug("找到Reply Content区域")
            parent = reply_section.find_parent()
            if not parent:
                return comments

            # 找到Reply Content后的所有div
            reply_divs = parent.find_all('div', recursive=True)
            logger.debug(f"找到 {len(reply_divs)} 个相关div")

            current_source = ""
            current_content = ""
            current_timestamp = ""

            for div in reply_divs:
                div_class = div.get('class', [])
                div_text = div.get_text(strip=True)

                # 检查是否是来源div
                if 'col-md-2' in div_class and div_text:
                    if current_source and current_content:
                        # 保存前一个评论
                        if not (config.ENABLE_INCREMENTAL_UPDATE and self._should_skip_comment(current_timestamp)):
                            comment = CaseComment(
                                source=current_source,
                                content=current_content,
                                timestamp=current_timestamp
                            )
                            comments.append(comment)
                            logger.debug(f"添加评论: {current_source} - {current_timestamp}")

                    current_source = div_text
                    current_content = ""
                    current_timestamp = ""

                elif 'col-md-10' in div_class and 'well' in div_class:
                    # 这是内容div
                    div_copy = BeautifulSoup(str(div), 'html.parser')

                    # 提取时间
                    spans = div_copy.find_all('span')
                    for span in spans:
                        span_text = span.get_text(strip=True)
                        if any(char.isdigit() for char in span_text) and ('-' in span_text or ':' in span_text):
                            current_timestamp = span_text
                            span.decompose()

                    # 移除hr元素
                    hrs = div_copy.find_all('hr')
                    for hr in hrs:
                        hr.decompose()

                    current_content = div_copy.get_text(strip=True)

            # 保存最后一个评论
            if current_source and current_content:
                if not (config.ENABLE_INCREMENTAL_UPDATE and self._should_skip_comment(current_timestamp)):
                    comment = CaseComment(
                        source=current_source,
                        content=current_content,
                        timestamp=current_timestamp
                    )
                    comments.append(comment)
                    logger.debug(f"添加最后一个评论: {current_source} - {current_timestamp}")

        except Exception as e:
            logger.error(f"从Reply Content区域提取评论失败: {e}")

        return comments

    def _should_skip_comment(self, timestamp: str) -> bool:
        """判断是否应该跳过该评论（增量更新逻辑）"""
        if not config.ENABLE_INCREMENTAL_UPDATE or not self.collection_state.last_update_time:
            return False

        try:
            comment_time = parse_datetime(timestamp)
            last_update_time = parse_datetime(self.collection_state.last_update_time)
            return comment_time <= last_update_time
        except Exception as e:
            logger.debug(f"时间解析失败: {e}")
            return False

    def _extract_attachments(self, soup: BeautifulSoup, case_id: int) -> List[CaseAttachment]:
        """提取工单附件信息"""
        attachments = []

        try:
            # 查找附件表格
            attachments_section = soup.find(string="Attachments")
            if not attachments_section:
                logger.info(f"工单 {case_id} 没有附件")
                return attachments

            # 找到附件表格
            table = attachments_section.find_next('table')
            if not table:
                logger.info(f"工单 {case_id} 没有找到附件表格")
                return attachments

            # 解析表格行
            rows = table.find_all('tr')[1:]  # 跳过表头

            for row in rows:
                try:
                    cells = row.find_all('td')
                    if len(cells) < 5:
                        continue

                    file_name = cells[0].get_text(strip=True)
                    file_size = cells[1].get_text(strip=True)
                    owner = cells[2].get_text(strip=True)
                    last_modified = cells[3].get_text(strip=True)

                    # 提取下载链接和附件ID
                    action_cell = cells[4]
                    dropdown_menu = action_cell.find(class_="dropdown-menu")

                    download_url = ""
                    attachment_id = ""

                    if dropdown_menu:
                        # 查找下载链接
                        download_link = dropdown_menu.find('a', string="Download")
                        if download_link and download_link.get('href'):
                            download_url = download_link['href']
                            if not download_url.startswith('http'):
                                download_url = f"https://akrocare.akrostar-tech.com{download_url}"

                        # 查找附件ID（从Add Notes的onclick中提取）
                        add_notes_link = dropdown_menu.find('a', string="Add Notes")
                        if add_notes_link and add_notes_link.get('onclick'):
                            onclick_text = add_notes_link['onclick']
                            # 提取addNotes中的数字
                            import re
                            match = re.search(r'addNotes\((\d+)\)', onclick_text)
                            if match:
                                attachment_id = match.group(1)

                    # 增量更新检查
                    if config.ENABLE_INCREMENTAL_UPDATE and self._should_skip_attachment(attachment_id, last_modified):
                        continue

                    attachment = CaseAttachment(
                        attachment_id=attachment_id,
                        file_name=file_name,
                        file_size=format_file_size(file_size),
                        owner=owner,
                        last_modified=last_modified,
                        download_url=download_url
                    )

                    attachments.append(attachment)

                except Exception as e:
                    logger.error(f"解析附件行失败: {e}")
                    continue

        except Exception as e:
            logger.error(f"提取工单 {case_id} 附件失败: {e}")

        logger.info(f"工单 {case_id} 提取到 {len(attachments)} 个附件")
        return attachments

    def _should_skip_attachment(self, attachment_id: str, last_modified: str) -> bool:
        """判断是否应该跳过该附件（增量更新逻辑）"""
        if not config.ENABLE_INCREMENTAL_UPDATE or not self.collection_state.last_update_time:
            return False

        try:
            attachment_time = parse_datetime(last_modified)
            last_update_time = parse_datetime(self.collection_state.last_update_time)
            return attachment_time <= last_update_time
        except:
            return False

    def _download_attachments(self, attachments: List[CaseAttachment], case_id: int):
        """下载附件到本地"""
        if not attachments:
            return

        case_dir = os.path.join(config.ATTACHMENTS_DIR, str(case_id))
        os.makedirs(case_dir, exist_ok=True)

        for attachment in attachments:
            try:
                if not attachment.download_url:
                    logger.warning(f"附件 {attachment.file_name} 没有下载链接")
                    continue

                # 生成安全的文件名
                safe_filename = get_safe_filename(attachment.file_name)

                # 检查是否需要版本控制
                version = 1
                if config.ENABLE_INCREMENTAL_UPDATE:
                    # 检查是否已存在该附件
                    existing_files = [f for f in os.listdir(case_dir) if f.startswith(safe_filename.split('.')[0])]
                    if existing_files:
                        version = len(existing_files) + 1
                        attachment.version = version

                # 生成本地路径
                local_path = generate_versioned_filename(case_dir, safe_filename, version)

                # 下载文件
                if download_file(attachment.download_url, local_path, cookies=self.cookies):
                    attachment.local_path = local_path
                    logger.info(f"附件下载成功: {attachment.file_name} -> {local_path}")
                else:
                    logger.error(f"附件下载失败: {attachment.file_name}")

            except Exception as e:
                logger.error(f"下载附件 {attachment.file_name} 失败: {e}")
                continue

    def _download_images_from_content(self, case_detail: CaseDetail, case_id: int):
        """下载工单内容中的图片"""
        try:
            case_dir = os.path.join(config.ATTACHMENTS_DIR, str(case_id), "images")
            os.makedirs(case_dir, exist_ok=True)

            # 从工单描述中提取图片
            self._extract_and_download_images(case_detail.summary.issue_description, case_dir, case_id)

            # 从评论中提取图片
            for comment in case_detail.comments:
                self._extract_and_download_images(comment.content, case_dir, case_id)

        except Exception as e:
            logger.error(f"下载工单 {case_id} 图片失败: {e}")

    def _extract_and_download_images(self, html_content: str, case_dir: str, case_id: int):
        """从HTML内容中提取并下载图片"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            images = soup.find_all('img')

            for img in images:
                src = img.get('src')
                if not src:
                    continue

                # 构建完整的图片URL
                if src.startswith('/'):
                    img_url = f"https://akrocare.akrostar-tech.com{src}"
                elif not src.startswith('http'):
                    img_url = f"https://akrocare.akrostar-tech.com/{src}"
                else:
                    img_url = src

                # 生成本地文件名
                img_name = os.path.basename(src)
                if not img_name:
                    img_name = f"image_{int(time.time())}.png"

                safe_img_name = get_safe_filename(img_name)
                local_path = os.path.join(case_dir, safe_img_name)

                # 避免重复下载
                if os.path.exists(local_path):
                    continue

                # 下载图片
                if download_file(img_url, local_path, cookies=self.cookies):
                    logger.info(f"图片下载成功: {img_name}")

        except Exception as e:
            logger.error(f"提取图片失败: {e}")

    def collect_all_cases(self) -> List[CaseDetail]:
        """采集所有工单的完整信息"""
        all_case_details = []

        try:
            # 获取工单概要信息（包括指定工单ID模式）
            case_summaries = self.get_cases_summary()

            if not case_summaries:
                logger.warning("没有获取到工单概要信息")
                return all_case_details

            # 逐个获取工单详细信息
            for i, case_summary in enumerate(case_summaries, 1):
                logger.info(f"正在处理第 {i}/{len(case_summaries)} 个工单: {case_summary.id}")

                case_detail = self.get_case_detail(case_summary)
                if case_detail:
                    all_case_details.append(case_detail)

                    # 更新采集状态
                    if case_summary.id not in self.collection_state.collected_case_ids:
                        self.collection_state.collected_case_ids.append(case_summary.id)

                # 避免请求过快
                time.sleep(2)

            # 更新最后采集时间
            self.collection_state.last_update_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self._save_collection_state()

            # 完成原始数据会话
            get_raw_data_manager().finalize_session()

            # 显示附件处理摘要
            if config.ENABLE_SESSION_ATTACHMENTS:
                attachment_summary = self.attachment_manager.get_attachment_summary()
                logger.info(f"附件处理摘要:")
                logger.info(f"  会话ID: {attachment_summary['session_id']}")
                logger.info(f"  附件目录: {attachment_summary['attachments_dir']}")
                logger.info(f"  总文件数: {attachment_summary['total_files']}")
                logger.info(f"  总大小: {attachment_summary['total_size_mb']} MB")
                logger.info(f"  URL映射: {attachment_summary['url_mappings']} 个")

        except Exception as e:
            logger.error(f"采集工单失败: {e}")
            # 即使出错也要完成原始数据会话
            get_raw_data_manager().finalize_session()

        return all_case_details

    def save_cases_data(self, case_details: List[CaseDetail]):
        """保存工单数据到文件"""
        try:
            data_to_save = [case_detail.to_dict() for case_detail in case_details]
            save_json(data_to_save, config.CASES_DATA_FILE)
            logger.info(f"已保存 {len(case_details)} 条工单数据")
        except Exception as e:
            logger.error(f"保存工单数据失败: {e}")

    def close(self):
        """关闭资源"""
        try:
            if self.driver:
                self.driver.quit()
            self.session.close()
            logger.info("资源已释放")
        except Exception as e:
            logger.error(f"释放资源失败: {e}")

    def __enter__(self):
        """上下文管理器入口"""
        self._setup_driver()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()