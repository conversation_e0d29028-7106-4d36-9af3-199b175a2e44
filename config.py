"""
配置文件
"""
import os

# 网站配置
LOGIN_URL = "https://akrocare.akrostar-tech.com/login"
CASES_API_URL = "https://akrocare.akrostar-tech.com/api/get_user_cases"
CASE_DETAIL_URL_TEMPLATE = "https://akrocare.akrostar-tech.com/review/case/{}"

# 登录凭据（请修改为实际的登录信息）
EMAIL = "<EMAIL>"
PASSWORD = "3z#63UOP"

# 数据存储配置
DATA_DIR = "data"
ATTACHMENTS_DIR = os.path.join(DATA_DIR, "attachments")
CASES_DATA_FILE = os.path.join(DATA_DIR, "cases_data.json")
LAST_UPDATE_FILE = os.path.join(DATA_DIR, "last_update.json")

# Selenium配置
WEBDRIVER_TIMEOUT = 30
PAGE_LOAD_TIMEOUT = 30
IMPLICIT_WAIT = 10
# ChromeDriver路径配置（如果为None则自动下载）
CHROME_DRIVER_PATH = "C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe"

# 采集配置
PAGE_SIZE = 30
MAX_RETRIES = 3
RETRY_DELAY = 2
# API请求方式：'requests' 或 'selenium'
API_REQUEST_METHOD = 'requests'  # 如果requests方式鉴权失败，可改为'selenium'
# 网络配置
DISABLE_PROXY = True  # 是否禁用代理

# 采集模式配置
SPECIFIC_CASE_IDS = []  # 指定工单ID列表，为空则采集所有
LATEST_N_CASES = 0  # 限制采集最新的N条工单，0表示不限制
INCREMENTAL_N_CASES = 0  # 限制采集离上次更新后的N条工单，0表示不限制

# 调试配置
SAVE_RAW_DATA = False  # 是否保存原始响应数据
PACK_RAW_DATA = True   # 是否打包原始数据（True=打包，False=单个文件）
RAW_DATA_DIR = "raw_data"  # 原始数据保存目录

# 工单状态配置
CASE_STATUS_OPEN = 4    # Open状态
CASE_STATUS_CLOSED = 5  # Closed状态
COLLECT_BOTH_STATUS = True  # 是否采集两种状态的工单

# 增量更新配置
ENABLE_INCREMENTAL_UPDATE = True

# 附件管理配置（新增）
ENABLE_SESSION_ATTACHMENTS = True  # 是否为每次采集创建独立的附件目录
UPDATE_LOCAL_PATHS = True  # 是否更新本地路径引用
COPY_ATTACHMENTS_TO_PACKAGE = True  # 是否将附件复制到数据包中
ATTACHMENT_PATH_PREFIX = "attachments"  # 附件在包中的路径前缀
BASE_URL = "https://akrocare.akrostar-tech.com/"  # 基础URL，用于构建相对路径的完整URL

# 确保数据目录存在
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(ATTACHMENTS_DIR, exist_ok=True)
