#!/usr/bin/env python3
"""
鉴权功能演示脚本
展示如何使用改进后的鉴权机制
"""
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from case_collector import CaseCollector
from utils import logger


def demo_auth_headers():
    """演示鉴权headers的构建"""
    print("=" * 60)
    print("鉴权Headers演示")
    print("=" * 60)
    
    print("程序会自动构建以下headers：")
    print()
    
    headers_example = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/javascript, */*; q=0.01',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://akrocare.akrostar-tech.com/cases',
        'Origin': 'https://akrocare.akrostar-tech.com',
        'Sec-Fetch-Dest': 'empty',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Site': 'same-origin',
        'X-Requested-With': 'XMLHttpRequest',
        'Content-Type': 'application/json'
    }
    
    for key, value in headers_example.items():
        print(f"  {key}: {value}")
    
    print()
    print("注意事项：")
    print("- User-Agent会从浏览器实时获取")
    print("- Referer会根据当前页面URL设置")
    print("- 如果页面有CSRF token，会自动添加X-CSRF-TOKEN")
    print("- 所有headers都会与浏览器保持一致")
    print()


def demo_cookie_sync():
    """演示Cookie同步机制"""
    print("=" * 60)
    print("Cookie同步机制演示")
    print("=" * 60)
    
    print("Cookie同步流程：")
    print()
    print("1. 登录成功后，自动获取浏览器中的所有cookies")
    print("2. 将cookies同步到requests session中")
    print("3. 保持cookie的完整属性（domain、path、secure等）")
    print("4. 在每次API请求前重新同步最新cookies")
    print()
    
    print("示例cookies：")
    cookies_example = [
        {'name': 'PHPSESSID', 'value': 'abc123def456', 'domain': '.akrocare.akrostar-tech.com'},
        {'name': 'csrf_token', 'value': 'xyz789uvw012', 'domain': '.akrocare.akrostar-tech.com'},
        {'name': 'user_session', 'value': 'session_data_here', 'domain': '.akrocare.akrostar-tech.com'},
        {'name': 'remember_token', 'value': 'remember_me_token', 'domain': '.akrocare.akrostar-tech.com'}
    ]
    
    for cookie in cookies_example:
        print(f"  {cookie['name']}: {cookie['value'][:20]}... (domain: {cookie['domain']})")
    
    print()
    print("同步时机：")
    print("- 登录成功后立即同步")
    print("- 导航到工单页面后再次同步")
    print("- 每次发送API请求前都会同步")
    print()


def demo_api_methods():
    """演示两种API请求方式"""
    print("=" * 60)
    print("API请求方式对比")
    print("=" * 60)
    
    print("方式一：requests + 鉴权信息")
    print("优点：")
    print("  - 性能好，速度快")
    print("  - 资源占用少")
    print("  - 支持详细的调试信息")
    print("缺点：")
    print("  - 需要正确配置headers和cookies")
    print("  - 可能遇到复杂的鉴权机制")
    print()
    
    print("方式二：selenium直接执行")
    print("优点：")
    print("  - 100%兼容浏览器行为")
    print("  - 自动处理所有鉴权信息")
    print("  - 无需手动配置headers")
    print("缺点：")
    print("  - 性能相对较慢")
    print("  - 资源占用较多")
    print()
    
    print("自动降级机制：")
    print("  如果requests方式失败，自动尝试selenium方式")
    print("  确保在任何情况下都能成功获取数据")
    print()


def demo_csrf_handling():
    """演示CSRF token处理"""
    print("=" * 60)
    print("CSRF Token处理演示")
    print("=" * 60)
    
    print("CSRF token获取策略：")
    print()
    print("1. 从meta标签获取：")
    print('   <meta name="csrf-token" content="token_value">')
    print()
    print("2. 从JavaScript变量获取：")
    print("   window._token 或 window.Laravel.csrfToken")
    print()
    print("3. 自动添加到请求headers：")
    print("   X-CSRF-TOKEN: token_value")
    print()
    print("处理流程：")
    print("- 页面加载后自动检测CSRF token")
    print("- 如果找到token，自动添加到所有API请求中")
    print("- 支持多种常见的CSRF token格式")
    print()


def demo_debug_info():
    """演示调试信息"""
    print("=" * 60)
    print("调试信息演示")
    print("=" * 60)
    
    print("程序提供详细的调试信息：")
    print()
    
    print("1. 登录过程：")
    print("   - 显示登录状态和错误信息")
    print("   - 记录保存的cookies数量")
    print("   - 显示session更新状态")
    print()
    
    print("2. API请求过程：")
    print("   - 显示请求URL和数据")
    print("   - 记录请求headers和cookies数量")
    print("   - 显示响应状态码和内容")
    print()
    
    print("3. 错误处理：")
    print("   - 详细的错误信息和堆栈跟踪")
    print("   - 自动重试机制的执行情况")
    print("   - 降级机制的触发过程")
    print()
    
    print("日志级别：")
    print("   - INFO: 一般操作信息")
    print("   - DEBUG: 详细的调试信息")
    print("   - WARNING: 警告信息")
    print("   - ERROR: 错误信息")
    print()


def demo_configuration():
    """演示配置选项"""
    print("=" * 60)
    print("配置选项演示")
    print("=" * 60)
    
    print("鉴权相关配置：")
    print()
    print("1. API请求方式：")
    print(f"   API_REQUEST_METHOD = '{config.API_REQUEST_METHOD}'")
    print("   可选值：'requests' 或 'selenium'")
    print()
    
    print("2. ChromeDriver路径：")
    print(f"   CHROME_DRIVER_PATH = {config.CHROME_DRIVER_PATH}")
    print("   None表示自动下载，或指定具体路径")
    print()
    
    print("3. 超时设置：")
    print(f"   WEBDRIVER_TIMEOUT = {config.WEBDRIVER_TIMEOUT}秒")
    print(f"   PAGE_LOAD_TIMEOUT = {config.PAGE_LOAD_TIMEOUT}秒")
    print()
    
    print("4. 重试机制：")
    print(f"   MAX_RETRIES = {config.MAX_RETRIES}次")
    print(f"   RETRY_DELAY = {config.RETRY_DELAY}秒")
    print()
    
    print("命令行配置示例：")
    print("   python main.py --api-method selenium")
    print("   python main.py --driver-path '/path/to/chromedriver'")
    print()


def main():
    """主函数"""
    print("AkroCare工单采集程序 - 鉴权功能演示")
    print()
    
    while True:
        print("请选择演示内容:")
        print("1. 鉴权Headers构建")
        print("2. Cookie同步机制")
        print("3. API请求方式对比")
        print("4. CSRF Token处理")
        print("5. 调试信息说明")
        print("6. 配置选项说明")
        print("7. 退出")
        print()
        
        choice = input("请输入选择 (1-7): ").strip()
        
        if choice == '1':
            demo_auth_headers()
        elif choice == '2':
            demo_cookie_sync()
        elif choice == '3':
            demo_api_methods()
        elif choice == '4':
            demo_csrf_handling()
        elif choice == '5':
            demo_debug_info()
        elif choice == '6':
            demo_configuration()
        elif choice == '7':
            print("退出演示程序")
            break
        else:
            print("无效选择，请重新输入")
        
        input("按回车键继续...")
        print()


if __name__ == "__main__":
    main()
