#!/usr/bin/env python3
"""
原始数据管理模块
用于保存和管理从网站获取的原始响应数据
"""
import os
import json
import zipfile
from datetime import datetime
from typing import Dict, Any, Optional

import config
from utils import logger, save_json


class RawDataManager:
    """原始数据管理器"""

    def __init__(self):
        self.raw_data_dir = config.RAW_DATA_DIR
        self.pack_data = config.PACK_RAW_DATA
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.temp_dir = None  # 初始化temp_dir属性

        if config.SAVE_RAW_DATA:
            self._setup_directories()

    def _setup_directories(self):
        """设置目录结构"""
        # 确保主目录存在
        os.makedirs(self.raw_data_dir, exist_ok=True)

        if not self.pack_data:
            # 创建分类目录
            directories = [
                os.path.join(self.raw_data_dir, "cases_list"),
                os.path.join(self.raw_data_dir, "case_details"),
                os.path.join(self.raw_data_dir, "attachments"),
                os.path.join(self.raw_data_dir, "html_pages")
            ]

            for directory in directories:
                os.makedirs(directory, exist_ok=True)
        else:
            # 创建临时目录用于打包
            self.temp_dir = os.path.join(self.raw_data_dir, f"temp_{self.session_id}")
            os.makedirs(self.temp_dir, exist_ok=True)

    def save_cases_list_response(self, page: int, response_data: Dict[str, Any], status: str = "all"):
        """保存工单列表响应数据"""
        if not config.SAVE_RAW_DATA:
            return

        filename = f"cases_list_page{page}_status{status}_{self.session_id}.json"

        if self.pack_data and self.temp_dir:
            filepath = os.path.join(self.temp_dir, filename)
        else:
            filepath = os.path.join(self.raw_data_dir, "cases_list", filename)

        # 添加元数据
        data_with_meta = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "page": page,
                "status": status,
                "data_type": "cases_list"
            },
            "response": response_data
        }

        save_json(data_with_meta, filepath)
        logger.debug(f"保存工单列表原始数据: {filename}")

    def save_case_detail_response(self, case_id: int, response_data: Dict[str, Any]):
        """保存工单详情响应数据"""
        if not config.SAVE_RAW_DATA:
            return

        filename = f"case_detail_{case_id}_{self.session_id}.json"

        if self.pack_data and self.temp_dir:
            filepath = os.path.join(self.temp_dir, filename)
        else:
            filepath = os.path.join(self.raw_data_dir, "case_details", filename)

        # 添加元数据
        data_with_meta = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "case_id": case_id,
                "data_type": "case_detail"
            },
            "response": response_data
        }

        save_json(data_with_meta, filepath)
        logger.debug(f"保存工单详情原始数据: {filename}")

    def save_html_page(self, case_id: int, html_content: str, page_type: str = "detail"):
        """保存HTML页面内容"""
        if not config.SAVE_RAW_DATA:
            return

        filename = f"case_{case_id}_{page_type}_{self.session_id}.html"

        if self.pack_data and self.temp_dir:
            filepath = os.path.join(self.temp_dir, filename)
        else:
            filepath = os.path.join(self.raw_data_dir, "html_pages", filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html_content)
            logger.debug(f"保存HTML页面: {filename}")
        except Exception as e:
            logger.error(f"保存HTML页面失败: {e}")

    def save_attachment_info(self, case_id: int, attachment_data: Dict[str, Any]):
        """保存附件信息"""
        if not config.SAVE_RAW_DATA:
            return

        filename = f"attachments_{case_id}_{self.session_id}.json"

        if self.pack_data and self.temp_dir:
            filepath = os.path.join(self.temp_dir, filename)
        else:
            filepath = os.path.join(self.raw_data_dir, "attachments", filename)

        # 添加元数据
        data_with_meta = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "case_id": case_id,
                "data_type": "attachments"
            },
            "data": attachment_data
        }

        save_json(data_with_meta, filepath)
        logger.debug(f"保存附件信息: {filename}")

    def save_api_request_info(self, url: str, method: str, headers: Dict[str, str],
                             data: Optional[Dict[str, Any]] = None, response_status: int = 0):
        """保存API请求信息"""
        if not config.SAVE_RAW_DATA:
            return

        timestamp = datetime.now().strftime("%H%M%S_%f")
        filename = f"api_request_{timestamp}.json"

        if self.pack_data and self.temp_dir:
            filepath = os.path.join(self.temp_dir, filename)
        else:
            filepath = os.path.join(self.raw_data_dir, filename)

        request_info = {
            "metadata": {
                "timestamp": datetime.now().isoformat(),
                "data_type": "api_request"
            },
            "request": {
                "url": url,
                "method": method,
                "headers": headers,
                "data": data,
                "response_status": response_status
            }
        }

        save_json(request_info, filepath)
        logger.debug(f"保存API请求信息: {filename}")

    def finalize_session(self):
        """完成会话，打包数据（如果启用打包模式）"""
        if not config.SAVE_RAW_DATA or not self.pack_data:
            return

        if not hasattr(self, 'temp_dir') or not os.path.exists(self.temp_dir):
            return

        # 检查临时目录是否有文件
        temp_files = os.listdir(self.temp_dir)
        if not temp_files:
            logger.debug("没有原始数据需要打包")
            # 清理空的临时目录
            try:
                os.rmdir(self.temp_dir)
            except:
                pass
            return

        # 创建ZIP文件
        zip_filename = f"raw_data_{self.session_id}.zip"
        zip_filepath = os.path.join(self.raw_data_dir, zip_filename)

        try:
            with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for filename in temp_files:
                    file_path = os.path.join(self.temp_dir, filename)
                    zipf.write(file_path, filename)

            logger.info(f"原始数据已打包保存: {zip_filepath}")

            # 清理临时文件
            for filename in temp_files:
                file_path = os.path.join(self.temp_dir, filename)
                try:
                    os.remove(file_path)
                except:
                    pass

            # 删除临时目录
            try:
                os.rmdir(self.temp_dir)
            except:
                pass

        except Exception as e:
            logger.error(f"打包原始数据失败: {e}")

    def get_session_summary(self) -> Dict[str, Any]:
        """获取会话摘要信息"""
        summary = {
            "session_id": self.session_id,
            "raw_data_enabled": config.SAVE_RAW_DATA,
            "pack_mode": self.pack_data,
            "raw_data_dir": self.raw_data_dir
        }

        if config.SAVE_RAW_DATA:
            if self.pack_data:
                # 检查是否有打包文件
                zip_filename = f"raw_data_{self.session_id}.zip"
                zip_filepath = os.path.join(self.raw_data_dir, zip_filename)
                summary["packed_file"] = zip_filepath if os.path.exists(zip_filepath) else None
            else:
                # 统计各类文件数量
                summary["files_count"] = {}
                if os.path.exists(self.raw_data_dir):
                    for subdir in ["cases_list", "case_details", "attachments", "html_pages"]:
                        subdir_path = os.path.join(self.raw_data_dir, subdir)
                        if os.path.exists(subdir_path):
                            summary["files_count"][subdir] = len(os.listdir(subdir_path))

        return summary


# 全局实例（延迟初始化）
raw_data_manager = None

def get_raw_data_manager():
    """获取全局RawDataManager实例"""
    global raw_data_manager
    if raw_data_manager is None:
        raw_data_manager = RawDataManager()
    return raw_data_manager
