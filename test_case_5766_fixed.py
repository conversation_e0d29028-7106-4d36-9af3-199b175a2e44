#!/usr/bin/env python3
"""
测试修正后的工单5766附件采集
"""
import os
import sys
import tempfile
import shutil
import json
import re
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from case_collector import CaseCollector
from data_mode_manager import DataModeManager
from utils import logger
import logging

# 设置调试级别
logging.getLogger().setLevel(logging.DEBUG)
logger.setLevel(logging.DEBUG)


def test_case_5766_attachment_extraction():
    """测试工单5766的附件提取"""
    print("=" * 80)
    print("测试工单5766附件提取（修正后）")
    print("=" * 80)

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    original_attachments_dir = config.ATTACHMENTS_DIR

    try:
        # 设置临时配置
        config.DATA_DIR = temp_dir
        config.ATTACHMENTS_DIR = os.path.join(temp_dir, "attachments")
        config.ENABLE_SESSION_ATTACHMENTS = True
        config.UPDATE_LOCAL_PATHS = True
        config.COPY_ATTACHMENTS_TO_PACKAGE = True
        config.SAVE_RAW_DATA = True
        config.PACK_RAW_DATA = True
        config.RAW_DATA_DIR = os.path.join(temp_dir, "raw_data")
        config.ENABLE_INCREMENTAL_UPDATE = False  # 禁用增量更新以确保附件被采集

        print(f"✅ 测试环境设置完成")
        print(f"   临时目录: {temp_dir}")
        print(f"   数据目录: {config.DATA_DIR}")
        print(f"   附件目录: {config.ATTACHMENTS_DIR}")
        print(f"   基础URL: {config.BASE_URL}")

        # 创建工单采集器
        with CaseCollector() as collector:
            print(f"\n🔍 开始测试工单5766...")

            # 首先登录
            print(f"🔐 正在登录...")
            if not collector.login():
                print(f"❌ 登录失败")
                return False, None

            print(f"✅ 登录成功")

            # 导航到工单页面
            print(f"🧭 导航到工单页面...")
            if not collector.navigate_to_cases():
                print(f"❌ 导航失败")
                return False, None

            print(f"✅ 导航成功")

            # 获取工单5766详情
            try:
                print(f"📋 获取工单5766详情...")
                case_detail = collector.get_case_detail(5766)

                if case_detail:
                    print(f"✅ 工单5766获取成功")
                    print(f"   工单号: {case_detail.summary.case_number}")
                    print(f"   标题: {case_detail.summary.subject}")
                    print(f"   产品线: {case_detail.summary.product_line}")
                    print(f"   状态: {case_detail.summary.status}")
                    print(f"   附件数量: {len(case_detail.attachments)}")
                    print(f"   评论数量: {len(case_detail.comments)}")

                    # 详细分析附件信息
                    if case_detail.attachments:
                        print(f"\n📎 附件详细信息:")
                        for i, attachment in enumerate(case_detail.attachments, 1):
                            print(f"   附件 {i}:")
                            print(f"     ID: {attachment.attachment_id}")
                            print(f"     文件名: {attachment.file_name}")
                            print(f"     大小: {attachment.file_size}")
                            print(f"     所有者: {attachment.owner}")
                            print(f"     修改时间: {attachment.last_modified}")
                            print(f"     下载URL: {attachment.download_url}")
                            print(f"     本地路径: {attachment.local_path}")

                            # 验证下载URL
                            if attachment.download_url:
                                print(f"     ✅ 下载URL存在")
                            else:
                                print(f"     ❌ 下载URL缺失")
                    else:
                        print(f"\n⚠️  工单5766没有检测到附件")

                    # 分析图片信息
                    print(f"\n🖼️  图片内容分析:")

                    # 检查工单描述中的图片
                    import re
                    if case_detail.summary.issue_description:
                        img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
                        img_matches = re.findall(img_pattern, case_detail.summary.issue_description)

                        if img_matches:
                            print(f"   工单描述包含 {len(img_matches)} 个图片:")
                            for i, img_src in enumerate(img_matches, 1):
                                print(f"     图片 {i}: {img_src}")
                        else:
                            print(f"   工单描述不包含图片")

                    # 检查评论中的图片
                    comment_images = 0
                    for comment in case_detail.comments:
                        if comment.content:
                            img_matches = re.findall(img_pattern, comment.content)
                            comment_images += len(img_matches)

                    if comment_images > 0:
                        print(f"   评论包含 {comment_images} 个图片")
                    else:
                        print(f"   评论不包含图片")

                    # 检查附件管理器处理结果
                    if hasattr(collector, 'attachment_manager'):
                        summary = collector.attachment_manager.get_attachment_summary()
                        print(f"\n📊 附件管理器摘要:")
                        print(f"   会话ID: {summary['session_id']}")
                        print(f"   总文件数: {summary['total_files']}")
                        print(f"   总大小: {summary['total_size_mb']} MB")
                        print(f"   URL映射: {summary['url_mappings']} 个")

                        # 检查会话目录
                        session_dir = collector.attachment_manager.session_attachments_dir
                        case_dir = os.path.join(session_dir, "5766")

                        if os.path.exists(case_dir):
                            files = os.listdir(case_dir)
                            print(f"   工单5766附件目录: {case_dir}")
                            print(f"   包含文件: {len(files)} 个")
                            for file in files:
                                file_path = os.path.join(case_dir, file)
                                size = os.path.getsize(file_path) if os.path.isfile(file_path) else 0
                                print(f"     - {file} ({size} bytes)")
                        else:
                            print(f"   ⚠️  工单5766附件目录不存在: {case_dir}")

                    # 测试数据包创建
                    if case_detail.attachments or comment_images > 0:
                        print(f"\n📦 测试数据包创建...")
                        data_manager = DataModeManager()

                        # 创建全量包
                        full_package = data_manager.create_full_mode_package([case_detail], enable_zip=True)
                        print(f"✅ 全量包创建成功: {full_package}")

                        # 验证包内容
                        import zipfile
                        with zipfile.ZipFile(full_package, 'r') as zipf:
                            files = zipf.namelist()
                            attachment_files = [f for f in files if f.startswith('attachments/')]

                            print(f"   ZIP文件总数: {len(files)}")
                            print(f"   附件文件数: {len(attachment_files)}")

                            if attachment_files:
                                print(f"   附件文件列表:")
                                for file in attachment_files:
                                    print(f"     - {file}")

                            # 检查工单数据中的路径
                            if 'patch_content.json' in files:
                                content_data = json.loads(zipf.read('patch_content.json').decode('utf-8'))
                                cases_data = content_data.get('cases', {})

                                print(f"\n📋 数据包中的路径信息:")
                                for case_id_str, case_data in cases_data.items():
                                    print(f"   工单 {case_id_str}:")

                                    # 检查附件路径
                                    attachments = case_data.get('attachments', [])
                                    for attachment in attachments:
                                        local_path = attachment.get('local_path', '')
                                        if local_path:
                                            print(f"     附件路径: {attachment.get('file_name')} -> {local_path}")

                    return True, case_detail
                else:
                    print(f"❌ 工单5766获取失败")
                    return False, None

            except Exception as e:
                print(f"❌ 获取工单5766时发生异常: {e}")
                import traceback
                traceback.print_exc()
                return False, None

    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False, None

    finally:
        # 恢复配置
        config.DATA_DIR = original_data_dir
        config.ATTACHMENTS_DIR = original_attachments_dir

        # 清理临时目录
        try:
            shutil.rmtree(temp_dir, ignore_errors=True)
        except:
            pass


def test_html_parsing():
    """测试HTML解析逻辑"""
    print("\n" + "=" * 80)
    print("测试HTML解析逻辑")
    print("=" * 80)

    try:
        # 使用提供的HTML内容进行测试
        test_html = '''
        <div class="akro-attach" style="padding: 5px;">
            <div class="col-md-12">
                <h1 style="text-align: center;margin-bottom: 30px;">Attachments:</h1>
            </div>
            <div class="col-md-12" style="margin-bottom: 100px;">
                <table class="table table-condensed">
                    <thead>
                    <tr>
                        <th>File Name</th>
                        <th>File Size</th>
                        <th>Notes</th>
                        <th>Owner</th>
                        <th>Last Modified</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>DDRPHY手册疑问记录_20241204.docx</td>
                            <td>0.94 MB</td>
                            <td></td>
                            <td>wf w</td>
                            <td>2024-12-05 09:11:25</td>
                            <td>
                                <div class="btn-group">
                                    <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false" style="height: 25px;">
                                        <span class="caret"></span>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a href="https://akrocare.akrostar-tech.com/case/download/uploads/case/20241205/DDRPHY%E6%89%8B%E5%86%8C%E7%96%91%E9%97%AE%E8%AE%B0%E5%BD%95_20241204.docx/DDRPHY%E6%89%8B%E5%86%8C%E7%96%91%E9%97%AE%E8%AE%B0%E5%BD%95_20241204.docx">Download</a></li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        '''

        print("测试HTML内容:")
        print(test_html[:200] + "...")

        # 使用BeautifulSoup解析
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(test_html, 'html.parser')

        # 查找附件标题
        attachments_section = soup.find(string="Attachments:")
        if attachments_section:
            print("✅ 找到Attachments:标题")

            # 找到附件表格
            table = attachments_section.find_next('table')
            if table:
                print("✅ 找到附件表格")

                # 解析表格行
                rows = table.find_all('tr')[1:]  # 跳过表头
                print(f"✅ 找到 {len(rows)} 行附件数据")

                for i, row in enumerate(rows, 1):
                    cells = row.find_all('td')
                    print(f"\n附件 {i}:")
                    print(f"   单元格数量: {len(cells)}")

                    if len(cells) >= 6:
                        file_name = cells[0].get_text(strip=True)
                        file_size = cells[1].get_text(strip=True)
                        notes = cells[2].get_text(strip=True)
                        owner = cells[3].get_text(strip=True)
                        last_modified = cells[4].get_text(strip=True)
                        action_cell = cells[5]

                        print(f"   文件名: {file_name}")
                        print(f"   大小: {file_size}")
                        print(f"   备注: {notes}")
                        print(f"   所有者: {owner}")
                        print(f"   修改时间: {last_modified}")

                        # 提取下载链接
                        dropdown_menu = action_cell.find(class_="dropdown-menu")
                        if dropdown_menu:
                            download_link = dropdown_menu.find('a', string="Download")
                            if download_link and download_link.get('href'):
                                download_url = download_link['href']
                                print(f"   下载URL: {download_url}")
                                print("✅ 成功解析附件信息")
                            else:
                                print("❌ 未找到下载链接")
                        else:
                            print("❌ 未找到下拉菜单")

                return True
            else:
                print("❌ 未找到附件表格")
                return False
        else:
            print("❌ 未找到Attachments:标题")
            return False

    except Exception as e:
        print(f"❌ HTML解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🔍 工单5766附件采集修正测试")

    tests = [
        ("HTML解析逻辑测试", test_html_parsing),
        ("工单5766附件提取测试", test_case_5766_attachment_extraction),
    ]

    results = []
    case_detail = None

    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_name == "工单5766附件提取测试":
                result, detail = test_func()
                if detail:
                    case_detail = detail
            else:
                result = test_func()

            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))

    # 总结
    print("\n" + "=" * 80)
    print("测试结果总结")
    print("=" * 80)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    print(f"\n总计: {passed}/{total} 测试通过")

    if passed == total:
        print("🎉 所有测试通过！工单5766附件采集问题已修正")
        if case_detail and case_detail.attachments:
            print(f"\n✅ 工单5766附件采集成功:")
            for attachment in case_detail.attachments:
                print(f"   - {attachment.file_name} ({attachment.file_size})")
    else:
        print("⚠️  部分测试失败，需要进一步检查")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
