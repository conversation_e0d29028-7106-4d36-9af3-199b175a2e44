#!/usr/bin/env python3
"""
新功能演示脚本
展示所有新增的采集模式和功能
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from raw_data_manager import raw_data_manager


def demo_collection_modes():
    """演示采集模式"""
    print("=" * 60)
    print("采集模式演示")
    print("=" * 60)

    print("1. 指定工单ID采集模式")
    print("   配置: SPECIFIC_CASE_IDS = [5766, 7514]")
    print("   命令: python main.py --case-ids 5766,7514")
    print("   说明: 只采集指定ID的工单")
    print()

    print("2. 最新N条工单模式")
    print("   配置: LATEST_N_CASES = 10")
    print("   命令: python main.py --latest-n 10")
    print("   说明: 采集最新的10条工单，不使用增量更新")
    print()

    print("3. 增量N条工单模式")
    print("   配置: INCREMENTAL_N_CASES = 5")
    print("   命令: python main.py --incremental-n 5")
    print("   说明: 采集离上次更新后的5条工单，使用增量更新")
    print()

    print("4. 全量采集模式（默认）")
    print("   配置: 所有限制参数为0或空")
    print("   命令: python main.py")
    print("   说明: 采集所有符合条件的工单")
    print()


def demo_status_modes():
    """演示工单状态模式"""
    print("=" * 60)
    print("工单状态模式演示")
    print("=" * 60)

    print("1. 采集两种状态（推荐）")
    print("   配置: COLLECT_BOTH_STATUS = True")
    print("   命令: python main.py --both-status")
    print("   说明: 同时采集Open和Closed状态的工单")
    print("   状态码: Open=4, Closed=5")
    print()

    print("2. 只采集Open状态")
    print("   配置: COLLECT_BOTH_STATUS = False, 状态设为Open")
    print("   命令: python main.py --only-open")
    print("   说明: 只采集Open状态的工单")
    print()

    print("3. 只采集Closed状态（默认）")
    print("   配置: COLLECT_BOTH_STATUS = False")
    print("   命令: python main.py --only-closed")
    print("   说明: 只采集Closed状态的工单")
    print()


def demo_raw_data_features():
    """演示原始数据保存功能"""
    print("=" * 60)
    print("原始数据保存功能演示")
    print("=" * 60)

    print("1. 启用原始数据保存")
    print("   配置: SAVE_RAW_DATA = True")
    print("   命令: python main.py --save-raw-data")
    print("   说明: 保存所有API响应和HTML页面")
    print()

    print("2. 打包模式（推荐）")
    print("   配置: PACK_RAW_DATA = True")
    print("   命令: python main.py --save-raw-data --pack-raw-data")
    print("   说明: 将所有原始数据打包成ZIP文件")
    print("   文件: raw_data/raw_data_YYYYMMDD_HHMMSS.zip")
    print()

    print("3. 分别保存模式")
    print("   配置: PACK_RAW_DATA = False")
    print("   命令: python main.py --save-raw-data --separate-raw-data")
    print("   说明: 分别保存到不同目录")
    print("   目录结构:")
    print("     raw_data/")
    print("     ├── cases_list/     # 工单列表响应")
    print("     ├── case_details/   # 工单详情响应")
    print("     ├── attachments/    # 附件信息")
    print("     └── html_pages/     # HTML页面")
    print()

    print("保存的数据类型:")
    print("- API请求和响应信息")
    print("- 工单列表JSON数据")
    print("- 工单详情JSON数据")
    print("- HTML页面源码")
    print("- 附件信息")
    print("- 请求headers和cookies")
    print()


def demo_directory_optimization():
    """演示目录创建优化"""
    print("=" * 60)
    print("目录创建优化演示")
    print("=" * 60)

    print("优化前:")
    print("- 为每个工单都创建目录")
    print("- 即使没有附件或图片也创建空目录")
    print()

    print("优化后:")
    print("- 只有在存在附件或图片时才创建目录")
    print("- 检查工单描述和评论中的图片")
    print("- 避免创建不必要的空目录")
    print()

    print("检查逻辑:")
    print("1. 检查是否有附件")
    print("2. 检查工单描述中是否有<img>标签")
    print("3. 检查评论内容中是否有<img>标签")
    print("4. 只有满足条件才创建目录和下载")
    print()


def demo_project_title_display():
    """演示产品线和标题显示"""
    print("=" * 60)
    print("产品线和标题显示演示")
    print("=" * 60)

    print("在提取工单详情时，程序会显示:")
    print()

    print("示例输出:")
    print("=" * 80)
    print("INFO - 正在获取工单 5766 的详细信息...")
    print("INFO - 产品线: MP01")
    print("INFO - 工单标题: ddr phy 手册寄存器疑问汇总")
    print("-" * 80)
    print("... 采集过程 ...")
    print("INFO - 工单 5766 详细信息采集完成")
    print("=" * 80)
    print()

    print("显示信息包括:")
    print("- 工单ID")
    print("- 产品线 (product_line)")
    print("- 工单标题 (subject)")
    print("- 分隔线用于清晰区分不同工单")
    print()

    print("这有助于:")
    print("- 快速识别正在处理的工单")
    print("- 了解工单所属产品线")
    print("- 监控采集进度")
    print("- 清晰区分不同工单的处理过程")
    print()


def demo_command_examples():
    """演示命令行使用示例"""
    print("=" * 60)
    print("命令行使用示例")
    print("=" * 60)

    examples = [
        {
            "description": "采集指定工单",
            "command": "python main.py --case-ids 5766,7514 --save-raw-data"
        },
        {
            "description": "采集最新10条工单",
            "command": "python main.py --latest-n 10 --both-status"
        },
        {
            "description": "增量采集5条工单",
            "command": "python main.py --incremental-n 5 --pack-raw-data"
        },
        {
            "description": "采集所有Open状态工单",
            "command": "python main.py --only-open --save-raw-data"
        },
        {
            "description": "完整功能演示",
            "command": "python main.py --both-status --latest-n 20 --save-raw-data --pack-raw-data --api-method requests --disable-proxy"
        }
    ]

    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}")
        print(f"   {example['command']}")
        print()


def demo_raw_data_manager():
    """演示原始数据管理器"""
    print("=" * 60)
    print("原始数据管理器演示")
    print("=" * 60)

    print("RawDataManager功能:")
    print()

    print("1. 自动会话管理")
    print("   - 每次运行创建唯一会话ID")
    print("   - 格式: YYYYMMDD_HHMMSS")
    print()

    print("2. 数据分类保存")
    print("   - 工单列表响应")
    print("   - 工单详情响应")
    print("   - HTML页面内容")
    print("   - 附件信息")
    print("   - API请求信息")
    print()

    print("3. 元数据记录")
    print("   - 时间戳")
    print("   - 数据类型")
    print("   - 请求参数")
    print("   - 响应状态")
    print()

    print("4. 自动打包")
    print("   - 会话结束时自动打包")
    print("   - 清理临时文件")
    print("   - 生成摘要信息")
    print()

    # 显示当前配置
    summary = raw_data_manager.get_session_summary()
    print("当前配置:")
    for key, value in summary.items():
        print(f"   {key}: {value}")
    print()


def main():
    """主函数"""
    print("AkroCare工单采集程序 - 新功能演示")
    print()

    while True:
        print("请选择演示内容:")
        print("1. 采集模式演示")
        print("2. 工单状态模式演示")
        print("3. 原始数据保存功能演示")
        print("4. 目录创建优化演示")
        print("5. 产品线和标题显示演示")
        print("6. 命令行使用示例")
        print("7. 原始数据管理器演示")
        print("8. 退出")
        print()

        choice = input("请输入选择 (1-8): ").strip()

        if choice == '1':
            demo_collection_modes()
        elif choice == '2':
            demo_status_modes()
        elif choice == '3':
            demo_raw_data_features()
        elif choice == '4':
            demo_directory_optimization()
        elif choice == '5':
            demo_project_title_display()
        elif choice == '6':
            demo_command_examples()
        elif choice == '7':
            demo_raw_data_manager()
        elif choice == '8':
            print("退出演示程序")
            break
        else:
            print("无效选择，请重新输入")

        input("按回车键继续...")
        print()


if __name__ == "__main__":
    main()
