# 附件管理功能使用指南

本指南详细说明了AkroCare工单采集程序的附件管理功能，包括图片和附件的存储、路径更新和打包功能。

## 🎯 功能概述

### 解决的问题
1. **附件存储隔离**: 每次采集的附件存储在独立的会话目录下
2. **路径引用更新**: 自动更新工单内容中的图片和附件路径引用
3. **数据包完整性**: 全量包和增量包正确包含所有附件文件
4. **增量附件管理**: 附件更新时在patch包中正确处理

### 核心特性
- ✅ **会话隔离**: 每次采集创建独立的附件目录
- ✅ **路径更新**: 自动更新HTML内容中的图片路径
- ✅ **完整打包**: 数据包包含所有相关附件
- ✅ **增量支持**: 增量模式正确处理附件变更
- ✅ **相对路径**: 使用相对路径确保数据包可移植性

## 🔧 配置选项

### 附件管理配置
在 `config.py` 中添加了以下配置项：

```python
# 附件管理配置
ENABLE_SESSION_ATTACHMENTS = True  # 是否为每次采集创建独立的附件目录
UPDATE_LOCAL_PATHS = True  # 是否更新本地路径引用
COPY_ATTACHMENTS_TO_PACKAGE = True  # 是否将附件复制到数据包中
ATTACHMENT_PATH_PREFIX = "attachments"  # 附件在包中的路径前缀
```

### 配置说明

#### ENABLE_SESSION_ATTACHMENTS
- **True**: 为每次采集创建独立的会话附件目录
- **False**: 使用传统的共享附件目录
- **推荐**: True（确保数据隔离）

#### UPDATE_LOCAL_PATHS
- **True**: 自动更新工单内容中的图片和附件路径
- **False**: 保持原始URL不变
- **推荐**: True（确保数据包自包含）

#### COPY_ATTACHMENTS_TO_PACKAGE
- **True**: 将附件复制到数据包中
- **False**: 数据包不包含附件文件
- **推荐**: True（确保数据包完整性）

## 📁 目录结构

### 会话附件目录结构
```
data/
└── attachments/
    └── session_20250529_143256/    # 会话ID目录
        ├── 1001/                   # 工单ID目录
        │   ├── document_1001.txt   # 文档附件
        │   ├── image_1001.png      # 图片附件
        │   └── diagram_1001.pdf    # 其他附件
        ├── 1002/
        │   ├── spec_1002.docx
        │   └── screenshot_1002.png
        └── ...
```

### 数据包结构
```
full_20250529_143256.zip
├── metadata.json               # Patch元数据
├── patch_content.json          # 工单数据
└── attachments/                # 附件目录
    ├── 1001/                   # 工单1001的附件
    │   ├── document_1001.txt
    │   ├── image_1001.png
    │   └── diagram_1001.pdf
    ├── 1002/                   # 工单1002的附件
    │   ├── spec_1002.docx
    │   └── screenshot_1002.png
    └── ...
```

## 🚀 使用方法

### 1. 启用附件管理功能

#### 修改配置
```python
# 在 config.py 中设置
ENABLE_SESSION_ATTACHMENTS = True
UPDATE_LOCAL_PATHS = True
COPY_ATTACHMENTS_TO_PACKAGE = True
```

#### 运行采集
```bash
# 全量模式 + 附件管理
python main.py --full-mode --enable-zip-package

# 增量模式 + 附件管理
python main.py --incremental-mode

# 指定工单 + 附件管理
python main.py --case-ids 5766,7514 --full-mode --enable-zip-package
```

### 2. 路径更新示例

#### 原始工单内容
```html
<p>请参考附件中的设计图：</p>
<img src="http://akrocare.com/uploads/design_5766.png" alt="设计图">
<p>详细说明见文档。</p>
```

#### 更新后内容
```html
<p>请参考附件中的设计图：</p>
<img src="attachments/5766/design_5766.png" alt="设计图">
<p>详细说明见文档。</p>
```

### 3. 数据包验证

#### 检查ZIP内容
```bash
# 查看ZIP文件内容
unzip -l full_20250529_143256.zip

# 或使用Python
python -c "
import zipfile
with zipfile.ZipFile('full_20250529_143256.zip', 'r') as z:
    files = z.namelist()
    print('总文件数:', len(files))
    attachment_files = [f for f in files if f.startswith('attachments/')]
    print('附件文件数:', len(attachment_files))
    for f in attachment_files[:10]:
        print('  -', f)
"
```

## 📊 功能验证

### 1. 运行测试
```bash
# 运行附件管理测试
python test_attachment_management.py

# 运行完整测试套件
python run_all_tests.py
```

### 2. 预期结果
```
================================================================================
附件管理功能测试
================================================================================

✅ 附件管理器基础功能 测试通过
✅ 路径更新功能 测试通过  
✅ 数据模式附件打包 测试通过

总计: 3/3 测试通过
🎉 所有测试通过！附件管理功能正常

功能说明:
1. ✅ 附件按会话ID独立存储
2. ✅ 图片和附件路径正确更新
3. ✅ 数据包正确包含附件文件
4. ✅ 增量模式正确处理附件变更
5. ✅ 路径引用更新为相对路径
```

## 🔍 技术实现

### 1. AttachmentManager类

#### 核心方法
- `process_case_detail()`: 处理工单的附件和图片
- `extract_and_download_images()`: 提取并下载HTML中的图片
- `download_and_store_attachment()`: 下载并存储附件
- `copy_attachments_to_package()`: 复制附件到数据包

#### 路径管理
- 自动生成会话ID和目录结构
- 处理文件名冲突和版本控制
- 计算相对路径用于数据包

### 2. DataModeManager集成

#### 全量模式
```python
# 创建全量数据包时自动包含附件
package_path = data_manager.create_full_mode_package(
    case_details, enable_zip=True
)
```

#### 增量模式
```python
# 创建增量Patch时只包含变更的附件
patch_path = data_manager.create_incremental_patch(
    case_details, base_version
)
```

### 3. CaseCollector集成

#### 附件处理流程
1. 提取工单附件信息
2. 使用AttachmentManager处理附件和图片
3. 更新工单内容中的路径引用
4. 保存处理后的工单数据

## 📈 性能优化

### 1. 下载优化
- 避免重复下载相同文件
- 支持断点续传（如果服务器支持）
- 并发下载控制

### 2. 存储优化
- 文件去重和版本管理
- 压缩存储减少空间占用
- 清理过期的会话目录

### 3. 内存优化
- 流式处理大文件
- 及时释放文件句柄
- 分批处理大量附件

## 🛠️ 故障排除

### 常见问题

#### 1. 附件下载失败
**现象**: 日志显示"附件下载失败"
**原因**: 网络问题、权限问题或URL无效
**解决**: 
- 检查网络连接
- 验证登录状态和权限
- 确认附件URL有效性

#### 2. 路径更新不生效
**现象**: 工单内容中的路径没有更新
**原因**: `UPDATE_LOCAL_PATHS` 配置为False
**解决**: 设置 `UPDATE_LOCAL_PATHS = True`

#### 3. 数据包不包含附件
**现象**: ZIP文件中没有attachments目录
**原因**: `COPY_ATTACHMENTS_TO_PACKAGE` 配置为False
**解决**: 设置 `COPY_ATTACHMENTS_TO_PACKAGE = True`

#### 4. 会话目录冲突
**现象**: 多次运行时附件目录冲突
**原因**: 会话ID生成重复
**解决**: 确保系统时间正确，或手动指定会话ID

### 调试方法

#### 1. 启用详细日志
```python
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

#### 2. 检查附件摘要
```python
attachment_summary = attachment_manager.get_attachment_summary()
print(f"总文件数: {attachment_summary['total_files']}")
print(f"总大小: {attachment_summary['total_size_mb']} MB")
```

#### 3. 验证数据包内容
```python
import zipfile
with zipfile.ZipFile('package.zip', 'r') as z:
    files = z.namelist()
    print("包含的文件:")
    for f in sorted(files):
        print(f"  {f}")
```

## 🔄 升级和迁移

### 从旧版本升级
1. 备份现有数据和配置
2. 更新代码到新版本
3. 修改配置启用新功能
4. 运行测试验证功能
5. 逐步迁移现有数据

### 数据迁移
```bash
# 迁移现有附件到新结构
python -c "
from attachment_manager import AttachmentManager
# 迁移逻辑
"
```

## 📚 最佳实践

### 1. 配置管理
- 根据实际需求调整配置
- 定期检查和更新配置
- 备份重要配置文件

### 2. 存储管理
- 定期清理过期的会话目录
- 监控磁盘空间使用
- 建立附件备份策略

### 3. 性能监控
- 监控下载速度和成功率
- 跟踪存储空间使用情况
- 分析处理时间和瓶颈

### 4. 安全考虑
- 验证下载文件的安全性
- 限制文件类型和大小
- 保护敏感附件内容

---

*本指南提供了完整的附件管理功能说明，确保您能够充分利用新的附件管理能力。*
