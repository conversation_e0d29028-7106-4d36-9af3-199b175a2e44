# 测试数据集说明

本目录包含从 `demo-content.html` 提取的真实测试数据，用于验证AkroCare工单采集程序的功能。

## 📁 文件结构

```
test_data/
├── README.md                    # 本说明文件
├── demo_case_detail.json       # 完整工单数据
├── demo_case_summary.json      # 工单概要数据
├── demo_comments.json          # 评论数据
├── demo_attachments.json       # 附件数据
├── updated_test_methods.py     # 更新的测试方法
└── validation_report.txt       # 验证报告
```

## 📋 数据概览

### 工单信息
- **工单ID**: 5766
- **工单号**: 01733361085
- **主题**: ddr phy 手册寄存器疑问汇总
- **产品线**: DDR4/3
- **产品名称**: AKS DDR4/3 PHY SMIC12SFE
- **项目名称**: N0013
- **用户**: wf w
- **公司**: Netforward
- **状态**: 已关闭 (Closed)
- **创建时间**: 2024-12-05 09:11:25
- **更新时间**: 2025-03-05 10:14:35

### 评论统计
- **评论总数**: 4条
- **时间跨度**: 2024-12-05 至 2025-03-05
- **参与者**: AkroStar Reply (技术支持), wf w (用户)

### 附件统计
- **附件数量**: 0个 (demo数据中无附件)

## 🔍 数据详情

### 评论列表

1. **AkroStar Reply** (2025-03-05 10:14:35)
   - 内容: 关于case关闭的最终回复
   - 长度: 67字符

2. **AkroStar Reply** (2024-12-10 18:42:23)
   - 内容: 详细的技术问题解答
   - 长度: 381字符
   - 包含: 多个技术问题的详细回复

3. **wf w** (2024-12-10 16:16:43)
   - 内容: 用户询问进展
   - 长度: 24字符

4. **AkroStar Reply** (2024-12-05 20:39:03)
   - 内容: 确认收到问题的初始回复
   - 长度: 45字符

## 🧪 测试用例

### 新增的测试方法

1. **test_comment_extraction_with_real_data**
   - 使用真实HTML数据测试评论提取功能
   - 验证评论数量、来源、时间戳和内容

2. **test_attachment_extraction_with_real_data**
   - 使用真实HTML数据测试附件提取功能
   - 验证附件信息的正确性

3. **test_case_summary_with_real_data**
   - 使用真实数据测试工单概要创建
   - 验证所有关键字段的正确性

4. **test_complete_case_detail_with_real_data**
   - 使用完整的真实数据测试工单详情
   - 验证数据结构和内容的完整性

## 🔧 使用方法

### 运行测试
```bash
# 运行所有测试
python test_collector.py

# 运行特定的真实数据测试
python -m pytest test_collector.py::TestCaseCollector::test_comment_extraction_with_real_data -v
```

### 验证数据集
```bash
# 验证测试数据集的完整性
python validate_test_dataset.py
```

### 更新数据集
```bash
# 从demo-content.html重新生成测试数据
python update_test_dataset.py
```

## 📊 数据验证

测试数据集经过以下验证：

- ✅ **文件完整性**: 所有必需文件存在且可读
- ✅ **JSON格式**: 所有JSON文件格式正确
- ✅ **数据结构**: 字段完整，类型正确
- ✅ **时间格式**: 时间戳符合 `YYYY-MM-DD HH:MM:SS` 格式
- ✅ **内容完整**: 评论内容非空，工单信息完整
- ✅ **数据一致性**: 独立文件与完整数据文件内容一致

## 🎯 测试覆盖

### 功能测试
- [x] 评论提取功能
- [x] 附件提取功能
- [x] 工单概要创建
- [x] 完整工单数据处理
- [x] HTML解析能力
- [x] 时间戳处理
- [x] 中文内容处理

### 边界测试
- [x] 空附件列表处理
- [x] 多条评论处理
- [x] 长文本内容处理
- [x] 特殊字符处理

### 集成测试
- [x] 数据模型转换
- [x] JSON序列化/反序列化
- [x] 文件读写操作

## 📈 数据来源

本测试数据集基于真实的AkroCare工单页面HTML内容生成：

1. **源文件**: `demo-content.html`
2. **提取时间**: 2025-05-24
3. **提取方法**: 使用改进的评论提取算法
4. **数据处理**: 自动清理HTML标签，格式化时间戳

## 🔄 更新历史

- **v1.0** (2025-05-24): 初始版本，包含4条评论的完整工单数据
- 基于demo-content.html生成
- 支持评论提取功能测试
- 包含完整的数据验证

## 📝 注意事项

1. **数据隐私**: 测试数据已脱敏处理，不包含敏感信息
2. **版本兼容**: 数据格式与当前代码版本兼容
3. **更新频率**: 当HTML结构变化时需要重新生成
4. **测试环境**: 仅用于单元测试，不影响生产数据

## 🤝 贡献指南

如需更新测试数据集：

1. 更新 `demo-content.html` 文件
2. 运行 `python update_test_dataset.py`
3. 运行 `python validate_test_dataset.py` 验证
4. 运行 `python test_collector.py` 确保测试通过
5. 更新本README文件的相关信息

---

*本测试数据集是AkroCare工单采集程序质量保证的重要组成部分，确保了代码的可靠性和稳定性。*
