
    def test_comment_extraction_with_real_data(self):
        """使用真实数据测试评论提取"""
        # 加载真实的评论数据
        with open('test_data/demo_comments.json', 'r', encoding='utf-8') as f:
            expected_comments = json.load(f)
        
        # 加载demo HTML文件
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 临时禁用增量更新
        original_incremental = config.ENABLE_INCREMENTAL_UPDATE
        config.ENABLE_INCREMENTAL_UPDATE = False
        
        try:
            collector = CaseCollector()
            comments = collector._extract_comments(soup, 5766)
            
            # 验证评论数量
            self.assertEqual(len(comments), len(expected_comments))
            
            # 验证每条评论的内容
            for i, comment in enumerate(comments):
                expected = expected_comments[i]
                self.assertEqual(comment.source, expected['source'])
                self.assertEqual(comment.timestamp, expected['timestamp'])
                self.assertIn(expected['content'][:50], comment.content)
                
        finally:
            config.ENABLE_INCREMENTAL_UPDATE = original_incremental
    
    def test_attachment_extraction_with_real_data(self):
        """使用真实数据测试附件提取"""
        # 加载真实的附件数据
        with open('test_data/demo_attachments.json', 'r', encoding='utf-8') as f:
            expected_attachments = json.load(f)
        
        # 加载demo HTML文件
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        collector = CaseCollector()
        attachments = collector._extract_attachments(soup, 5766)
        
        # 验证附件数量
        self.assertEqual(len(attachments), len(expected_attachments))
        
        # 验证每个附件的信息
        for i, attachment in enumerate(attachments):
            expected = expected_attachments[i]
            self.assertEqual(attachment.file_name, expected['file_name'])
            self.assertEqual(attachment.file_size, expected['file_size'])
            self.assertEqual(attachment.owner, expected['owner'])
    