[{"source": "AkroStar Reply:", "content": "Hi Yadong，遗留的问题在另外的case中已经回复，我将关闭该case，如果有后续的问题可以reopen或开新的case，谢谢！", "timestamp": "2025-03-05 10:14:35"}, {"source": "AkroStar Reply:", "content": "Hi Yadong，1. 问题1我们任在确认中；2. 问题2：a. 不建议使用单个bit的训练结果来代替全部的16根线；RxPBDly是用来减小DQ和DBI信号相对于DQS的skew的，由于物理实现时无法保证每根线的delay完全一致，因此做出这样的修改后可能会导致读数据错误；b. 同样不建议使用一个rank的训练结果给两个rank使用，原理同上；3. 问题3：a. 贵司可以参考IO Retention流程中需要保存的寄存器值，如果该寄存器值被保存，则意味着热复位后需要将该值重新写入PHY中；4. 问题4：a. 从PHY工作的角度来看，只有DevInit是必须要做的初始化配置，其他训练步骤都是为了保证PHY和颗粒之前可以正常通信，因此不建议关闭WrDQ1D和MxRdlat训练，如果关闭该训练可能会导致写数据错误和不同Dbyte之间的读数据不对齐；谢谢！", "timestamp": "2024-12-10 18:42:23"}, {"source": "wf w:", "content": "HI,Aden:有任何进展，可以先同步给我们吗?", "timestamp": "2024-12-10 16:16:43"}, {"source": "AkroStar Reply:", "content": "Hi NetForward Colleagues，已收到贵司问题，我将尽快给出回复，谢谢！", "timestamp": "2024-12-05 20:39:03"}]