{"metadata": {"generated_at": "2025-05-29T23:38:59.529172", "type": "incremental_dataset", "case_count": 5, "description": "增量测试数据集，包含1个无变化、1个更新、3个新增工单"}, "cases": [{"summary": {"id": 1001, "case_number": "TEST001001", "subject": "基础测试工单1001", "product_line": "MP01", "product_name": "测试产品MP01", "project_name": "项目MP01", "cnum": "MP01", "product_code": "T1001", "case_level": 2, "issue_type": 3, "issue_description": "这是一个基础测试工单，ID为1001", "attach": 1, "priority": 2, "version": "v1.0", "notify": null, "status": 5, "uid": 101, "deadline": null, "is_admin_create": 0, "created_at": "2022-09-01 21:38:59", "updated_at": "2022-09-01 23:38:59", "company_name": "测试公司3", "username": "测试用户2"}, "comments": [{"source": "用户2", "content": "这是工单1001的第1条评论。", "timestamp": "2022-09-01 23:38:59"}, {"source": "用户3", "content": "这是工单1001的第2条评论。", "timestamp": "2022-09-01 22:38:59"}], "attachments": [{"attachment_id": "att_1001_000", "file_name": "document_1001.txt", "file_size": "201", "owner": "用户3", "last_modified": "2022-09-01 23:38:59", "download_url": "http://test.akrocare.com/download/att_1001_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1001.txt", "version": 1}]}, {"summary": {"id": 1002, "case_number": "TEST001002", "subject": "更新后的工单1002", "product_line": "MP01", "product_name": "测试产品MP01", "project_name": "项目MP01", "cnum": "MP01", "product_code": "T1002", "case_level": 1, "issue_type": 1, "issue_description": "这是更新后的工单描述，ID为1002，包含新内容", "attach": 1, "priority": 3, "version": "v1.0", "notify": null, "status": 4, "uid": 102, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-31 21:38:59", "updated_at": "2022-08-31 23:38:59", "company_name": "测试公司1", "username": "测试用户3"}, "comments": [{"source": "用户3", "content": "这是工单1002的第1条评论。", "timestamp": "2022-08-31 23:38:59"}, {"source": "用户4", "content": "这是工单1002的第2条评论。", "timestamp": "2022-08-31 22:38:59"}, {"source": "用户5", "content": "这是工单1002的第3条评论。", "timestamp": "2022-08-31 21:38:59"}], "attachments": [{"attachment_id": "att_1002_000", "file_name": "document_1002.txt", "file_size": "201", "owner": "用户1", "last_modified": "2022-08-31 23:38:59", "download_url": "http://test.akrocare.com/download/att_1002_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1002.txt", "version": 1}, {"attachment_id": "att_1002_001", "file_name": "data_1002_1.csv", "file_size": "141", "owner": "用户1", "last_modified": "2022-08-31 23:28:59", "download_url": "http://test.akrocare.com/download/att_1002_001", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\data_1002_1.csv", "version": 1}]}, {"summary": {"id": 1006, "case_number": "TEST001006", "subject": "基础测试工单1006", "product_line": "MP01", "product_name": "测试产品MP01", "project_name": "项目MP01", "cnum": "MP01", "product_code": "T1006", "case_level": 1, "issue_type": 2, "issue_description": "这是一个基础测试工单，ID为1006", "attach": 1, "priority": 2, "version": "v1.0", "notify": null, "status": 5, "uid": 106, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-27 21:38:59", "updated_at": "2022-08-27 23:38:59", "company_name": "测试公司2", "username": "测试用户2"}, "comments": [{"source": "用户2", "content": "这是工单1006的第1条评论。", "timestamp": "2022-08-27 23:38:59"}, {"source": "用户3", "content": "这是工单1006的第2条评论。", "timestamp": "2022-08-27 22:38:59"}, {"source": "用户4", "content": "这是工单1006的第3条评论。", "timestamp": "2022-08-27 21:38:59"}], "attachments": [{"attachment_id": "att_1006_000", "file_name": "document_1006.txt", "file_size": "201", "owner": "用户2", "last_modified": "2022-08-27 23:38:59", "download_url": "http://test.akrocare.com/download/att_1006_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1006.txt", "version": 1}]}, {"summary": {"id": 1007, "case_number": "TEST001007", "subject": "包含图片的工单1007", "product_line": "MP02", "product_name": "测试产品MP02", "project_name": "项目MP02", "cnum": "MP02", "product_code": "T1007", "case_level": 2, "issue_type": 3, "issue_description": "这个工单包含图片内容 <img src='test_image_1007.png' alt='测试图片'>", "attach": 1, "priority": 3, "version": "v1.0", "notify": null, "status": 5, "uid": 107, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-26 21:38:59", "updated_at": "2022-08-26 23:38:59", "company_name": "测试公司3", "username": "测试用户3"}, "comments": [{"source": "用户3", "content": "这是工单1007的第1条评论。 <img src='comment_image_1007_0.png' alt='评论图片'>", "timestamp": "2022-08-26 23:38:59"}, {"source": "用户4", "content": "这是工单1007的第2条评论。", "timestamp": "2022-08-26 22:38:59"}, {"source": "用户5", "content": "这是工单1007的第3条评论。", "timestamp": "2022-08-26 21:38:59"}, {"source": "用户1", "content": "这是工单1007的第4条评论。", "timestamp": "2022-08-26 20:38:59"}], "attachments": [{"attachment_id": "att_1007_000", "file_name": "document_1007.txt", "file_size": "201", "owner": "用户3", "last_modified": "2022-08-26 23:38:59", "download_url": "http://test.akrocare.com/download/att_1007_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1007.txt", "version": 1}, {"attachment_id": "att_1007_001", "file_name": "image_1007.png", "file_size": "660", "owner": "用户3", "last_modified": "2022-08-26 23:28:59", "download_url": "http://test.akrocare.com/download/att_1007_001", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\image_1007.png", "version": 1}]}, {"summary": {"id": 1008, "case_number": "TEST001008", "subject": "复杂工单1008", "product_line": "MP03", "product_name": "测试产品MP03", "project_name": "项目MP03", "cnum": "MP03", "product_code": "T1008", "case_level": 1, "issue_type": 1, "issue_description": "复杂工单包含多种内容 <img src='diagram_1008.png'> 和链接", "attach": 1, "priority": 4, "version": "v1.0", "notify": null, "status": 4, "uid": 108, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-25 21:38:59", "updated_at": "2022-08-25 23:38:59", "company_name": "测试公司1", "username": "测试用户4"}, "comments": [{"source": "用户4", "content": "这是工单1008的第1条评论。 <img src='comment_image_1008_0.png' alt='评论图片'>", "timestamp": "2022-08-25 23:38:59"}], "attachments": [{"attachment_id": "att_1008_000", "file_name": "document_1008.txt", "file_size": "201", "owner": "用户1", "last_modified": "2022-08-25 23:38:59", "download_url": "http://test.akrocare.com/download/att_1008_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1008.txt", "version": 1}, {"attachment_id": "att_1008_001", "file_name": "image_1008.png", "file_size": "659", "owner": "用户1", "last_modified": "2022-08-25 23:28:59", "download_url": "http://test.akrocare.com/download/att_1008_001", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\image_1008.png", "version": 1}, {"attachment_id": "att_1008_002", "file_name": "data_1008_2.csv", "file_size": "141", "owner": "用户1", "last_modified": "2022-08-25 23:18:59", "download_url": "http://test.akrocare.com/download/att_1008_002", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\data_1008_2.csv", "version": 1}]}]}