#!/usr/bin/env python3
"""
Patch应用脚本
支持解析、应用和合并Patch文件
"""
import os
import json
import zipfile
import shutil
import argparse
from datetime import datetime
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass

import config
from utils import logger, save_json, load_json
from data_mode_manager import PatchContent, PatchMetadata, DataModeManager


class PatchApplier:
    """Patch应用器"""
    
    def __init__(self):
        self.data_dir = config.DATA_DIR
        self.output_dir = os.path.join(self.data_dir, "applied")
        os.makedirs(self.output_dir, exist_ok=True)
    
    def parse_patch(self, patch_path: str) -> Optional[PatchContent]:
        """解析Patch文件"""
        try:
            if patch_path.endswith('.zip'):
                return self._parse_patch_from_zip(patch_path)
            else:
                return self._parse_patch_from_directory(patch_path)
        except Exception as e:
            logger.error(f"解析Patch失败 {patch_path}: {e}")
            return None
    
    def _parse_patch_from_zip(self, zip_path: str) -> Optional[PatchContent]:
        """从ZIP文件解析Patch"""
        try:
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                if 'patch_content.json' not in zipf.namelist():
                    logger.error(f"ZIP文件中缺少patch_content.json: {zip_path}")
                    return None
                
                content_data = json.loads(zipf.read('patch_content.json').decode('utf-8'))
                
                # 重构PatchContent对象
                metadata = PatchMetadata(**content_data['metadata'])
                patch_content = PatchContent(
                    metadata=metadata,
                    cases=content_data['cases'],
                    attachments=content_data['attachments']
                )
                
                return patch_content
        except Exception as e:
            logger.error(f"从ZIP解析Patch失败: {e}")
            return None
    
    def _parse_patch_from_directory(self, dir_path: str) -> Optional[PatchContent]:
        """从目录解析Patch"""
        try:
            content_file = os.path.join(dir_path, 'patch_content.json')
            if not os.path.exists(content_file):
                logger.error(f"目录中缺少patch_content.json: {dir_path}")
                return None
            
            content_data = load_json(content_file)
            if not content_data:
                return None
            
            # 重构PatchContent对象
            metadata = PatchMetadata(**content_data['metadata'])
            patch_content = PatchContent(
                metadata=metadata,
                cases=content_data['cases'],
                attachments=content_data['attachments']
            )
            
            return patch_content
        except Exception as e:
            logger.error(f"从目录解析Patch失败: {e}")
            return None
    
    def print_patch_info(self, patch_content: PatchContent):
        """打印Patch信息"""
        metadata = patch_content.metadata
        
        print("=" * 80)
        print(f"Patch信息: {metadata.patch_id}")
        print("=" * 80)
        print(f"时间戳: {metadata.timestamp}")
        print(f"模式: {metadata.mode}")
        print(f"基础版本: {metadata.base_version or 'N/A'}")
        print(f"工单总数: {metadata.case_count}")
        print(f"描述: {metadata.description}")
        print()
        
        if metadata.new_cases:
            print(f"新增工单 ({len(metadata.new_cases)}个):")
            for case_id in metadata.new_cases:
                case_data = patch_content.cases.get(str(case_id), {})
                summary = case_data.get('summary', {})
                subject = summary.get('subject', 'N/A')
                product_line = summary.get('product_line', 'N/A')
                print(f"  - ID: {case_id}, 产品线: {product_line}, 标题: {subject}")
            print()
        
        if metadata.updated_cases:
            print(f"更新工单 ({len(metadata.updated_cases)}个):")
            for case_id in metadata.updated_cases:
                case_data = patch_content.cases.get(str(case_id), {})
                summary = case_data.get('summary', {})
                subject = summary.get('subject', 'N/A')
                product_line = summary.get('product_line', 'N/A')
                print(f"  - ID: {case_id}, 产品线: {product_line}, 标题: {subject}")
            print()
        
        # 打印附件信息
        if patch_content.attachments:
            print("附件信息:")
            for case_id, file_paths in patch_content.attachments.items():
                if file_paths:
                    case_data = patch_content.cases.get(case_id, {})
                    summary = case_data.get('summary', {})
                    subject = summary.get('subject', 'N/A')
                    print(f"  工单 {case_id} ({subject}): {len(file_paths)} 个附件")
                    for file_path in file_paths[:3]:  # 只显示前3个
                        filename = os.path.basename(file_path)
                        print(f"    - {filename}")
                    if len(file_paths) > 3:
                        print(f"    ... 还有 {len(file_paths) - 3} 个附件")
            print()
        
        print("=" * 80)
    
    def apply_patches_to_empty_directory(self, patch_paths: List[str], 
                                       output_path: str) -> bool:
        """应用Patch到空目录（只处理新增工单）"""
        logger.info("开始应用Patch到空目录...")
        
        all_new_cases = {}
        ignored_updates = []
        
        for patch_path in patch_paths:
            logger.info(f"处理Patch: {patch_path}")
            patch_content = self.parse_patch(patch_path)
            
            if not patch_content:
                logger.error(f"无法解析Patch: {patch_path}")
                continue
            
            # 只处理新增工单
            for case_id in patch_content.metadata.new_cases:
                case_id_str = str(case_id)
                if case_id_str in patch_content.cases:
                    all_new_cases[case_id_str] = patch_content.cases[case_id_str]
                    logger.info(f"添加新增工单: {case_id}")
            
            # 记录被忽略的更新工单
            for case_id in patch_content.metadata.updated_cases:
                case_data = patch_content.cases.get(str(case_id), {})
                summary = case_data.get('summary', {})
                subject = summary.get('subject', 'N/A')
                ignored_updates.append(f"工单 {case_id}: {subject}")
        
        # 打印忽略信息
        if ignored_updates:
            print("\n忽略的更新工单:")
            for update_info in ignored_updates:
                print(f"  - {update_info}")
            print()
        
        # 保存结果
        result_data = {
            "metadata": {
                "applied_at": datetime.now().isoformat(),
                "mode": "new_cases_only",
                "source_patches": patch_paths,
                "case_count": len(all_new_cases)
            },
            "cases": all_new_cases
        }
        
        save_json(result_data, output_path)
        logger.info(f"新增工单数据已保存到: {output_path}")
        
        return True
    
    def apply_patches_to_existing_data(self, base_data_path: str, 
                                     patch_paths: List[str], 
                                     output_path: str) -> bool:
        """应用Patch到现有数据"""
        logger.info("开始应用Patch到现有数据...")
        
        # 加载基础数据
        base_cases = self._load_base_data(base_data_path)
        if not base_cases:
            logger.error(f"无法加载基础数据: {base_data_path}")
            return False
        
        logger.info(f"加载基础数据: {len(base_cases)} 个工单")
        
        # 应用所有Patch
        final_cases = base_cases.copy()
        patch_summary = []
        
        for patch_path in patch_paths:
            logger.info(f"应用Patch: {patch_path}")
            patch_content = self.parse_patch(patch_path)
            
            if not patch_content:
                logger.error(f"无法解析Patch: {patch_path}")
                continue
            
            # 应用新增和更新
            new_count = 0
            update_count = 0
            
            for case_id_str, case_data in patch_content.cases.items():
                if case_id_str not in base_cases:
                    # 新增工单
                    final_cases[case_id_str] = case_data
                    new_count += 1
                else:
                    # 更新工单
                    final_cases[case_id_str] = case_data
                    update_count += 1
            
            patch_summary.append({
                "patch": os.path.basename(patch_path),
                "new_cases": new_count,
                "updated_cases": update_count
            })
        
        # 打印应用摘要
        print("\nPatch应用摘要:")
        for summary in patch_summary:
            print(f"  {summary['patch']}: 新增 {summary['new_cases']} 个，更新 {summary['updated_cases']} 个")
        print()
        
        # 保存结果
        result_data = {
            "metadata": {
                "applied_at": datetime.now().isoformat(),
                "mode": "full_merge",
                "base_data": base_data_path,
                "source_patches": patch_paths,
                "case_count": len(final_cases)
            },
            "cases": final_cases
        }
        
        save_json(result_data, output_path)
        logger.info(f"合并数据已保存到: {output_path}")
        
        return True
    
    def _load_base_data(self, data_path: str) -> Dict[str, Any]:
        """加载基础数据"""
        cases = {}
        
        try:
            if data_path.endswith('.zip'):
                # 从ZIP文件加载
                with zipfile.ZipFile(data_path, 'r') as zipf:
                    if 'patch_content.json' in zipf.namelist():
                        content_data = json.loads(zipf.read('patch_content.json').decode('utf-8'))
                        cases = content_data.get('cases', {})
                    elif 'cases_data.json' in zipf.namelist():
                        # 兼容旧格式
                        data = json.loads(zipf.read('cases_data.json').decode('utf-8'))
                        if 'cases' in data:
                            for case in data['cases']:
                                case_id = str(case.get('summary', {}).get('id', ''))
                                if case_id:
                                    cases[case_id] = case
            elif os.path.isdir(data_path):
                # 从目录加载
                content_file = os.path.join(data_path, 'patch_content.json')
                if os.path.exists(content_file):
                    content_data = load_json(content_file)
                    if content_data:
                        cases = content_data.get('cases', {})
            else:
                # 从JSON文件加载
                data = load_json(data_path)
                if data:
                    if 'cases' in data:
                        cases = data['cases']
                    elif isinstance(data, dict):
                        cases = data
        
        except Exception as e:
            logger.error(f"加载基础数据失败: {e}")
        
        return cases
    
    def create_merged_patch(self, applied_data_path: str, output_patch_path: str) -> bool:
        """将应用后的数据重新打包成Patch"""
        logger.info("开始创建合并Patch...")
        
        try:
            # 加载应用后的数据
            applied_data = load_json(applied_data_path)
            if not applied_data:
                logger.error(f"无法加载应用后的数据: {applied_data_path}")
                return False
            
            cases = applied_data.get('cases', {})
            metadata_info = applied_data.get('metadata', {})
            
            # 创建新的Patch元数据
            current_time = datetime.now()
            patch_metadata = PatchMetadata(
                patch_id=f"merged_{current_time.strftime('%Y%m%d_%H%M%S')}",
                timestamp=current_time.isoformat(),
                mode="merged",
                base_version=None,
                case_count=len(cases),
                new_cases=list(range(len(cases))),  # 所有工单都视为新增
                updated_cases=[],
                description=f"合并Patch，包含{len(cases)}个工单"
            )
            
            # 创建Patch内容
            patch_content = PatchContent(
                metadata=patch_metadata,
                cases=cases,
                attachments={}  # 附件信息在合并时可能丢失
            )
            
            # 保存到临时目录
            temp_dir = f"{output_patch_path}_temp"
            os.makedirs(temp_dir, exist_ok=True)
            
            # 使用DataModeManager保存
            data_manager = DataModeManager()
            data_manager._save_patch_to_directory(patch_content, temp_dir)
            
            # 创建ZIP包
            data_manager._create_zip_package(temp_dir, output_patch_path)
            
            # 清理临时目录
            shutil.rmtree(temp_dir, ignore_errors=True)
            
            logger.info(f"合并Patch已创建: {output_patch_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建合并Patch失败: {e}")
            return False


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Patch应用工具")
    parser.add_argument('--mode', choices=['empty', 'merge', 'info', 'repack'], 
                       required=True, help='操作模式')
    parser.add_argument('--patches', nargs='+', required=True, 
                       help='Patch文件路径列表')
    parser.add_argument('--base-data', help='基础数据路径（merge模式必需）')
    parser.add_argument('--output', help='输出文件路径')
    
    args = parser.parse_args()
    
    applier = PatchApplier()
    
    if args.mode == 'info':
        # 显示Patch信息
        for patch_path in args.patches:
            patch_content = applier.parse_patch(patch_path)
            if patch_content:
                applier.print_patch_info(patch_content)
    
    elif args.mode == 'empty':
        # 应用到空目录
        output_path = args.output or os.path.join(applier.output_dir, 
                                                 f"new_cases_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        applier.apply_patches_to_empty_directory(args.patches, output_path)
    
    elif args.mode == 'merge':
        # 合并到现有数据
        if not args.base_data:
            logger.error("merge模式需要指定--base-data参数")
            return
        
        output_path = args.output or os.path.join(applier.output_dir, 
                                                 f"merged_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
        applier.apply_patches_to_existing_data(args.base_data, args.patches, output_path)
    
    elif args.mode == 'repack':
        # 重新打包
        if len(args.patches) != 1:
            logger.error("repack模式只能处理一个输入文件")
            return
        
        output_path = args.output or os.path.join(applier.output_dir, 
                                                 f"repacked_{datetime.now().strftime('%Y%m%d_%H%M%S')}.zip")
        applier.create_merged_patch(args.patches[0], output_path)


if __name__ == "__main__":
    main()
