#!/usr/bin/env python3
"""
使用示例：展示如何配置和使用工单采集程序
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from utils import logger


def example_basic_config():
    """基本配置示例"""
    print("=" * 60)
    print("基本配置示例")
    print("=" * 60)

    print("1. 在config.py中设置登录信息：")
    print('   EMAIL = "<EMAIL>"')
    print('   PASSWORD = "your_password"')
    print()

    print("2. 运行程序：")
    print("   python main.py")
    print()


def example_custom_driver_config():
    """自定义ChromeDriver配置示例"""
    print("=" * 60)
    print("自定义ChromeDriver配置示例")
    print("=" * 60)

    print("方法1: 在config.py中设置：")
    print('   CHROME_DRIVER_PATH = "C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe"')
    print()

    print("方法2: 使用命令行参数：")
    print('   python main.py --driver-path "C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe"')
    print()

    print("方法3: 在代码中动态设置：")
    print("   import config")
    print('   config.CHROME_DRIVER_PATH = "/path/to/chromedriver"')
    print()


def example_command_line_usage():
    """命令行使用示例"""
    print("=" * 60)
    print("命令行使用示例")
    print("=" * 60)

    examples = [
        {
            "description": "基本使用",
            "command": "python main.py"
        },
        {
            "description": "指定登录信息",
            "command": "python main.py --email <EMAIL> --password mypassword"
        },
        {
            "description": "指定ChromeDriver路径",
            "command": 'python main.py --driver-path "C:/path/to/chromedriver.exe"'
        },
        {
            "description": "使用selenium方式请求API",
            "command": "python main.py --api-method selenium"
        },
        {
            "description": "使用requests方式请求API",
            "command": "python main.py --api-method requests"
        },
        {
            "description": "禁用代理",
            "command": "python main.py --disable-proxy"
        },
        {
            "description": "启用代理",
            "command": "python main.py --enable-proxy"
        },
        {
            "description": "启用增量更新",
            "command": "python main.py --incremental"
        },
        {
            "description": "禁用增量更新",
            "command": "python main.py --disable-incremental"
        },
        {
            "description": "组合使用多个参数",
            "command": 'python main.py --email <EMAIL> --password mypass --driver-path "/path/to/chromedriver" --api-method requests --disable-proxy --incremental'
        }
    ]

    for i, example in enumerate(examples, 1):
        print(f"{i}. {example['description']}:")
        print(f"   {example['command']}")
        print()


def example_programmatic_usage():
    """编程方式使用示例"""
    print("=" * 60)
    print("编程方式使用示例")
    print("=" * 60)

    print("```python")
    print("from case_collector import CaseCollector")
    print("import config")
    print()
    print("# 配置登录信息")
    print('config.EMAIL = "<EMAIL>"')
    print('config.PASSWORD = "your_password"')
    print()
    print("# 可选：配置ChromeDriver路径")
    print('config.CHROME_DRIVER_PATH = "/path/to/chromedriver"')
    print()
    print("# 使用采集器")
    print("with CaseCollector() as collector:")
    print("    # 登录")
    print("    if collector.login():")
    print("        # 导航到工单页面")
    print("        if collector.navigate_to_cases():")
    print("            # 采集工单数据")
    print("            case_details = collector.collect_all_cases()")
    print("            # 保存数据")
    print("            collector.save_cases_data(case_details)")
    print("```")
    print()


def check_current_config():
    """检查当前配置"""
    print("=" * 60)
    print("当前配置检查")
    print("=" * 60)

    print(f"登录邮箱: {config.EMAIL}")
    print(f"密码: {'已设置' if config.PASSWORD and config.PASSWORD != 'your_password' else '未设置'}")
    print(f"ChromeDriver路径: {config.CHROME_DRIVER_PATH or '自动下载'}")
    print(f"API请求方式: {config.API_REQUEST_METHOD}")
    print(f"代理设置: {'禁用' if config.DISABLE_PROXY else '启用'}")
    print(f"增量更新: {'启用' if config.ENABLE_INCREMENTAL_UPDATE else '禁用'}")
    print(f"页面大小: {config.PAGE_SIZE}")
    print(f"WebDriver超时: {config.WEBDRIVER_TIMEOUT}秒")
    print(f"最大重试次数: {config.MAX_RETRIES}")
    print()

    # 检查配置有效性
    issues = []
    if not config.EMAIL or config.EMAIL == "<EMAIL>":
        issues.append("需要设置有效的EMAIL")
    if not config.PASSWORD or config.PASSWORD == "your_password":
        issues.append("需要设置有效的PASSWORD")
    if config.CHROME_DRIVER_PATH and not os.path.exists(config.CHROME_DRIVER_PATH):
        issues.append(f"ChromeDriver路径不存在: {config.CHROME_DRIVER_PATH}")

    if issues:
        print("⚠️  配置问题:")
        for issue in issues:
            print(f"   - {issue}")
    else:
        print("✅ 配置检查通过")
    print()


def main():
    """主函数"""
    print("AkroCare工单采集程序 - 使用示例")
    print()

    while True:
        print("请选择示例:")
        print("1. 基本配置示例")
        print("2. 自定义ChromeDriver配置示例")
        print("3. 命令行使用示例")
        print("4. 编程方式使用示例")
        print("5. 检查当前配置")
        print("6. 退出")
        print()

        choice = input("请输入选择 (1-6): ").strip()

        if choice == '1':
            example_basic_config()
        elif choice == '2':
            example_custom_driver_config()
        elif choice == '3':
            example_command_line_usage()
        elif choice == '4':
            example_programmatic_usage()
        elif choice == '5':
            check_current_config()
        elif choice == '6':
            print("退出示例程序")
            break
        else:
            print("无效选择，请重新输入")

        input("按回车键继续...")
        print()


if __name__ == "__main__":
    main()
