{"metadata": {"generated_at": "2025-05-29T23:38:59.522171", "type": "base_dataset", "case_count": 5, "description": "基础测试数据集，包含5个工单"}, "cases": [{"summary": {"id": 1001, "case_number": "TEST001001", "subject": "基础测试工单1001", "product_line": "MP01", "product_name": "测试产品MP01", "project_name": "项目MP01", "cnum": "MP01", "product_code": "T1001", "case_level": 2, "issue_type": 3, "issue_description": "这是一个基础测试工单，ID为1001", "attach": 1, "priority": 2, "version": "v1.0", "notify": null, "status": 5, "uid": 101, "deadline": null, "is_admin_create": 0, "created_at": "2022-09-01 21:38:59", "updated_at": "2022-09-01 23:38:59", "company_name": "测试公司3", "username": "测试用户2"}, "comments": [{"source": "用户2", "content": "这是工单1001的第1条评论。", "timestamp": "2022-09-01 23:38:59"}, {"source": "用户3", "content": "这是工单1001的第2条评论。", "timestamp": "2022-09-01 22:38:59"}], "attachments": [{"attachment_id": "att_1001_000", "file_name": "document_1001.txt", "file_size": "201", "owner": "用户3", "last_modified": "2022-09-01 23:38:59", "download_url": "http://test.akrocare.com/download/att_1001_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1001.txt", "version": 1}]}, {"summary": {"id": 1002, "case_number": "TEST001002", "subject": "包含图片的工单1002", "product_line": "MP02", "product_name": "测试产品MP02", "project_name": "项目MP02", "cnum": "MP02", "product_code": "T1002", "case_level": 1, "issue_type": 1, "issue_description": "这个工单包含图片内容 <img src='test_image_1002.png' alt='测试图片'>", "attach": 1, "priority": 3, "version": "v1.0", "notify": null, "status": 4, "uid": 102, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-31 21:38:59", "updated_at": "2022-08-31 23:38:59", "company_name": "测试公司1", "username": "测试用户3"}, "comments": [{"source": "用户3", "content": "这是工单1002的第1条评论。 <img src='comment_image_1002_0.png' alt='评论图片'>", "timestamp": "2022-08-31 23:38:59"}, {"source": "用户4", "content": "这是工单1002的第2条评论。", "timestamp": "2022-08-31 22:38:59"}, {"source": "用户5", "content": "这是工单1002的第3条评论。", "timestamp": "2022-08-31 21:38:59"}], "attachments": [{"attachment_id": "att_1002_000", "file_name": "document_1002.txt", "file_size": "201", "owner": "用户1", "last_modified": "2022-08-31 23:38:59", "download_url": "http://test.akrocare.com/download/att_1002_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1002.txt", "version": 1}, {"attachment_id": "att_1002_001", "file_name": "image_1002.png", "file_size": "666", "owner": "用户1", "last_modified": "2022-08-31 23:28:59", "download_url": "http://test.akrocare.com/download/att_1002_001", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\image_1002.png", "version": 1}]}, {"summary": {"id": 1003, "case_number": "TEST001003", "subject": "复杂工单1003", "product_line": "MP03", "product_name": "测试产品MP03", "project_name": "项目MP03", "cnum": "MP03", "product_code": "T1003", "case_level": 2, "issue_type": 2, "issue_description": "复杂工单包含多种内容 <img src='diagram_1003.png'> 和链接", "attach": 1, "priority": 4, "version": "v1.0", "notify": null, "status": 5, "uid": 103, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-30 21:38:59", "updated_at": "2022-08-30 23:38:59", "company_name": "测试公司2", "username": "测试用户4"}, "comments": [{"source": "用户4", "content": "这是工单1003的第1条评论。 <img src='comment_image_1003_0.png' alt='评论图片'>", "timestamp": "2022-08-30 23:38:59"}, {"source": "用户5", "content": "这是工单1003的第2条评论。", "timestamp": "2022-08-30 22:38:59"}, {"source": "用户1", "content": "这是工单1003的第3条评论。", "timestamp": "2022-08-30 21:38:59"}, {"source": "用户2", "content": "这是工单1003的第4条评论。", "timestamp": "2022-08-30 20:38:59"}], "attachments": [{"attachment_id": "att_1003_000", "file_name": "document_1003.txt", "file_size": "201", "owner": "用户2", "last_modified": "2022-08-30 23:38:59", "download_url": "http://test.akrocare.com/download/att_1003_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1003.txt", "version": 1}, {"attachment_id": "att_1003_001", "file_name": "image_1003.png", "file_size": "669", "owner": "用户2", "last_modified": "2022-08-30 23:28:59", "download_url": "http://test.akrocare.com/download/att_1003_001", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\image_1003.png", "version": 1}, {"attachment_id": "att_1003_002", "file_name": "data_1003_2.csv", "file_size": "141", "owner": "用户2", "last_modified": "2022-08-30 23:18:59", "download_url": "http://test.akrocare.com/download/att_1003_002", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\data_1003_2.csv", "version": 1}]}, {"summary": {"id": 1004, "case_number": "TEST001004", "subject": "基础测试工单1004", "product_line": "MP01", "product_name": "测试产品MP01", "project_name": "项目MP01", "cnum": "MP01", "product_code": "T1004", "case_level": 1, "issue_type": 3, "issue_description": "这是一个基础测试工单，ID为1004", "attach": 1, "priority": 5, "version": "v1.0", "notify": null, "status": 5, "uid": 104, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-29 21:38:59", "updated_at": "2022-08-29 23:38:59", "company_name": "测试公司3", "username": "测试用户5"}, "comments": [{"source": "用户5", "content": "这是工单1004的第1条评论。", "timestamp": "2022-08-29 23:38:59"}], "attachments": [{"attachment_id": "att_1004_000", "file_name": "document_1004.txt", "file_size": "201", "owner": "用户3", "last_modified": "2022-08-29 23:38:59", "download_url": "http://test.akrocare.com/download/att_1004_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1004.txt", "version": 1}]}, {"summary": {"id": 1005, "case_number": "TEST001005", "subject": "包含图片的工单1005", "product_line": "MP02", "product_name": "测试产品MP02", "project_name": "项目MP02", "cnum": "MP02", "product_code": "T1005", "case_level": 2, "issue_type": 1, "issue_description": "这个工单包含图片内容 <img src='test_image_1005.png' alt='测试图片'>", "attach": 1, "priority": 1, "version": "v1.0", "notify": null, "status": 4, "uid": 105, "deadline": null, "is_admin_create": 0, "created_at": "2022-08-28 21:38:59", "updated_at": "2022-08-28 23:38:59", "company_name": "测试公司1", "username": "测试用户1"}, "comments": [{"source": "用户1", "content": "这是工单1005的第1条评论。 <img src='comment_image_1005_0.png' alt='评论图片'>", "timestamp": "2022-08-28 23:38:59"}, {"source": "用户2", "content": "这是工单1005的第2条评论。", "timestamp": "2022-08-28 22:38:59"}], "attachments": [{"attachment_id": "att_1005_000", "file_name": "document_1005.txt", "file_size": "201", "owner": "用户1", "last_modified": "2022-08-28 23:38:59", "download_url": "http://test.akrocare.com/download/att_1005_000", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\document_1005.txt", "version": 1}, {"attachment_id": "att_1005_001", "file_name": "image_1005.png", "file_size": "674", "owner": "用户1", "last_modified": "2022-08-28 23:28:59", "download_url": "http://test.akrocare.com/download/att_1005_001", "local_path": "C:\\Users\\<USER>\\Documents\\augment-projects\\selenium-aks\\test_data_complete\\attachments\\image_1005.png", "version": 1}]}]}