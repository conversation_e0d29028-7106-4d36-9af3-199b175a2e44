#!/usr/bin/env python3
"""
测试RawDataManager修复
"""
import sys
import os
import tempfile
import shutil

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config


def test_raw_data_manager_fix():
    """测试RawDataManager修复"""
    print("=" * 60)
    print("测试RawDataManager修复")
    print("=" * 60)
    
    # 保存原始配置
    original_save_raw_data = config.SAVE_RAW_DATA
    original_pack_raw_data = config.PACK_RAW_DATA
    original_raw_data_dir = config.RAW_DATA_DIR
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    config.RAW_DATA_DIR = temp_dir
    
    try:
        # 测试配置1：禁用原始数据保存
        print("测试配置1：禁用原始数据保存")
        config.SAVE_RAW_DATA = False
        config.PACK_RAW_DATA = True
        
        from raw_data_manager import RawDataManager
        manager1 = RawDataManager()
        
        print(f"✅ 创建成功")
        print(f"   temp_dir: {manager1.temp_dir}")
        print(f"   pack_data: {manager1.pack_data}")
        
        # 测试保存方法（应该直接返回）
        manager1.save_cases_list_response(1, {'test': 'data'}, '5')
        print(f"✅ save_cases_list_response 正常返回")
        
        # 测试配置2：启用原始数据保存，分别保存模式
        print("\n测试配置2：启用原始数据保存，分别保存模式")
        config.SAVE_RAW_DATA = True
        config.PACK_RAW_DATA = False
        
        # 重新导入以获取新配置
        import importlib
        import raw_data_manager
        importlib.reload(raw_data_manager)
        
        manager2 = raw_data_manager.RawDataManager()
        
        print(f"✅ 创建成功")
        print(f"   temp_dir: {manager2.temp_dir}")
        print(f"   pack_data: {manager2.pack_data}")
        
        # 测试保存方法
        manager2.save_cases_list_response(1, {'test': 'data'}, '5')
        print(f"✅ save_cases_list_response 成功")
        
        manager2.save_case_detail_response(123, {'test': 'detail'})
        print(f"✅ save_case_detail_response 成功")
        
        # 测试配置3：启用原始数据保存，打包模式
        print("\n测试配置3：启用原始数据保存，打包模式")
        config.SAVE_RAW_DATA = True
        config.PACK_RAW_DATA = True
        
        # 重新导入以获取新配置
        importlib.reload(raw_data_manager)
        
        manager3 = raw_data_manager.RawDataManager()
        
        print(f"✅ 创建成功")
        print(f"   temp_dir: {manager3.temp_dir}")
        print(f"   pack_data: {manager3.pack_data}")
        
        # 测试保存方法
        manager3.save_cases_list_response(1, {'test': 'data'}, '5')
        print(f"✅ save_cases_list_response 成功")
        
        manager3.save_case_detail_response(123, {'test': 'detail'})
        print(f"✅ save_case_detail_response 成功")
        
        manager3.save_html_page(123, '<html>test</html>')
        print(f"✅ save_html_page 成功")
        
        manager3.save_attachment_info(123, [{'name': 'test.txt'}])
        print(f"✅ save_attachment_info 成功")
        
        manager3.save_api_request_info('http://test.com', 'POST', {}, {})
        print(f"✅ save_api_request_info 成功")
        
        # 完成会话
        manager3.finalize_session()
        print(f"✅ finalize_session 成功")
        
        # 检查生成的文件
        zip_files = [f for f in os.listdir(temp_dir) if f.endswith('.zip')]
        if zip_files:
            print(f"✅ 生成ZIP文件: {zip_files[0]}")
        
        print("\n🎉 所有测试通过！RawDataManager修复成功")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 恢复原始配置
        config.SAVE_RAW_DATA = original_save_raw_data
        config.PACK_RAW_DATA = original_pack_raw_data
        config.RAW_DATA_DIR = original_raw_data_dir
        
        # 清理临时目录
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_attribute_access():
    """测试属性访问"""
    print("\n" + "=" * 60)
    print("测试属性访问")
    print("=" * 60)
    
    # 保存原始配置
    original_save_raw_data = config.SAVE_RAW_DATA
    original_pack_raw_data = config.PACK_RAW_DATA
    
    try:
        # 测试不同配置下的属性访问
        test_configs = [
            (False, True),   # 禁用保存，打包模式
            (True, False),   # 启用保存，分别保存模式
            (True, True),    # 启用保存，打包模式
        ]
        
        for i, (save_raw, pack_raw) in enumerate(test_configs, 1):
            print(f"测试配置 {i}: SAVE_RAW_DATA={save_raw}, PACK_RAW_DATA={pack_raw}")
            
            config.SAVE_RAW_DATA = save_raw
            config.PACK_RAW_DATA = pack_raw
            
            # 重新导入以获取新配置
            import importlib
            import raw_data_manager
            importlib.reload(raw_data_manager)
            
            manager = raw_data_manager.RawDataManager()
            
            # 检查所有必要属性
            assert hasattr(manager, 'temp_dir'), "缺少temp_dir属性"
            assert hasattr(manager, 'pack_data'), "缺少pack_data属性"
            assert hasattr(manager, 'raw_data_dir'), "缺少raw_data_dir属性"
            assert hasattr(manager, 'session_id'), "缺少session_id属性"
            
            print(f"  ✅ 所有属性存在")
            print(f"     temp_dir: {manager.temp_dir}")
            print(f"     pack_data: {manager.pack_data}")
            
            # 测试temp_dir的正确性
            if save_raw and pack_raw:
                assert manager.temp_dir is not None, "打包模式下temp_dir不应为None"
                assert isinstance(manager.temp_dir, str), "temp_dir应为字符串"
            else:
                # 在非打包模式下，temp_dir可以为None
                print(f"     非打包模式，temp_dir为: {manager.temp_dir}")
            
            print(f"  ✅ 属性值正确")
        
        print("\n🎉 属性访问测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 属性访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 恢复原始配置
        config.SAVE_RAW_DATA = original_save_raw_data
        config.PACK_RAW_DATA = original_pack_raw_data


def main():
    """主函数"""
    print("RawDataManager修复测试程序")
    print("=" * 60)
    
    tests = [
        ("RawDataManager修复", test_raw_data_manager_fix),
        ("属性访问", test_attribute_access)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！RawDataManager修复成功")
        print("\n修复内容:")
        print("1. ✅ 初始化temp_dir属性为None")
        print("2. ✅ 在所有使用temp_dir的地方添加None检查")
        print("3. ✅ 确保目录创建逻辑正确")
        print("4. ✅ 支持所有配置组合")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
