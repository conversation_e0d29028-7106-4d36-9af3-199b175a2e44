#!/usr/bin/env python3
"""
全量模式和增量模式使用示例
"""
import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def show_usage_examples():
    """显示使用示例"""
    print("=" * 80)
    print("AkroCare工单采集程序 - 全量模式和增量模式使用示例")
    print("=" * 80)
    
    print("\n📋 基础命令")
    print("-" * 40)
    
    basic_examples = [
        {
            "description": "标准采集（默认模式）",
            "command": "python main.py"
        },
        {
            "description": "指定工单采集",
            "command": "python main.py --case-ids 5766,7514"
        },
        {
            "description": "最新10条工单",
            "command": "python main.py --latest-n 10"
        },
        {
            "description": "采集两种状态工单",
            "command": "python main.py --both-status"
        }
    ]
    
    for example in basic_examples:
        print(f"• {example['description']}")
        print(f"  {example['command']}")
        print()
    
    print("\n🗂️ 全量模式示例")
    print("-" * 40)
    
    full_mode_examples = [
        {
            "description": "全量模式 - 目录输出",
            "command": "python main.py --full-mode"
        },
        {
            "description": "全量模式 - ZIP打包",
            "command": "python main.py --full-mode --enable-zip-package"
        },
        {
            "description": "全量模式 - 指定工单 + ZIP",
            "command": "python main.py --full-mode --case-ids 5766,7514 --enable-zip-package"
        },
        {
            "description": "全量模式 - 最新20条 + ZIP",
            "command": "python main.py --full-mode --latest-n 20 --enable-zip-package"
        },
        {
            "description": "全量模式 - 两种状态 + ZIP",
            "command": "python main.py --full-mode --both-status --enable-zip-package"
        }
    ]
    
    for example in full_mode_examples:
        print(f"• {example['description']}")
        print(f"  {example['command']}")
        print()
    
    print("\n📈 增量模式示例")
    print("-" * 40)
    
    incremental_examples = [
        {
            "description": "增量模式 - 自动检测基础版本",
            "command": "python main.py --incremental-mode"
        },
        {
            "description": "增量模式 - 指定基础版本",
            "command": "python main.py --incremental-mode --base-version 20250524_142348"
        },
        {
            "description": "增量模式 - 最新10条",
            "command": "python main.py --incremental-mode --latest-n 10"
        },
        {
            "description": "增量模式 - 指定工单",
            "command": "python main.py --incremental-mode --case-ids 5766,7514"
        },
        {
            "description": "增量模式 - 两种状态",
            "command": "python main.py --incremental-mode --both-status"
        }
    ]
    
    for example in incremental_examples:
        print(f"• {example['description']}")
        print(f"  {example['command']}")
        print()
    
    print("\n🔧 Patch应用示例")
    print("-" * 40)
    
    patch_examples = [
        {
            "description": "查看Patch信息",
            "command": "python patch_applier.py --mode info --patches data/patches/patch_20250524_143256.zip"
        },
        {
            "description": "查看多个Patch信息",
            "command": "python patch_applier.py --mode info --patches data/patches/patch_*.zip"
        },
        {
            "description": "提取新增工单（空目录模式）",
            "command": "python patch_applier.py --mode empty --patches patch1.zip patch2.zip --output new_cases.json"
        },
        {
            "description": "合并到现有数据",
            "command": "python patch_applier.py --mode merge --base-data full_20250524_142348.zip --patches patch1.zip patch2.zip --output merged.json"
        },
        {
            "description": "重新打包数据",
            "command": "python patch_applier.py --mode repack --patches merged.json --output repacked.zip"
        }
    ]
    
    for example in patch_examples:
        print(f"• {example['description']}")
        print(f"  {example['command']}")
        print()
    
    print("\n🚀 完整工作流示例")
    print("-" * 40)
    
    workflows = [
        {
            "name": "初始部署工作流",
            "steps": [
                "# 1. 全量采集",
                "python main.py --full-mode --enable-zip-package",
                "",
                "# 2. 查看数据包信息",
                "python patch_applier.py --mode info --patches data/full_*.zip",
                "",
                "# 3. 备份数据包",
                "cp data/full_*.zip backup/"
            ]
        },
        {
            "name": "日常增量更新工作流",
            "steps": [
                "# 1. 增量采集最新20条",
                "python main.py --incremental-mode --latest-n 20",
                "",
                "# 2. 查看Patch内容",
                "python patch_applier.py --mode info --patches data/patches/patch_*.zip",
                "",
                "# 3. 应用到生产数据",
                "python patch_applier.py --mode merge --base-data production.zip --patches data/patches/patch_*.zip --output updated_production.json",
                "",
                "# 4. 重新打包",
                "python patch_applier.py --mode repack --patches updated_production.json --output production_new.zip"
            ]
        },
        {
            "name": "多Patch合并工作流",
            "steps": [
                "# 1. 收集所有Patch文件",
                "ls data/patches/patch_*.zip",
                "",
                "# 2. 提取新增工单",
                "python patch_applier.py --mode empty --patches data/patches/patch_*.zip --output new_cases_summary.json",
                "",
                "# 3. 完整合并",
                "python patch_applier.py --mode merge --base-data base_data.zip --patches data/patches/patch_*.zip --output complete_merge.json",
                "",
                "# 4. 最终打包",
                "python patch_applier.py --mode repack --patches complete_merge.json --output final_package.zip"
            ]
        },
        {
            "name": "数据分析工作流",
            "steps": [
                "# 1. 分析Patch内容",
                "python patch_applier.py --mode info --patches suspicious_patch.zip",
                "",
                "# 2. 提取特定数据",
                "python patch_applier.py --mode empty --patches patch1.zip patch2.zip --output analysis_data.json",
                "",
                "# 3. 数据统计分析",
                "python -c \"",
                "import json",
                "with open('analysis_data.json') as f:",
                "    data = json.load(f)",
                "    cases = data['cases']",
                "    print(f'总工单数: {len(cases)}')",
                "    for case_id, case_data in cases.items():",
                "        summary = case_data['summary']",
                "        print(f'ID: {case_id}, 产品线: {summary[\\\"product_line\\\"]}, 标题: {summary[\\\"subject\\\"]}')\"",
                ""
            ]
        }
    ]
    
    for workflow in workflows:
        print(f"📋 {workflow['name']}")
        print()
        for step in workflow['steps']:
            if step.startswith('#'):
                print(f"  {step}")
            elif step == "":
                print()
            else:
                print(f"  {step}")
        print()
    
    print("\n💡 高级技巧")
    print("-" * 40)
    
    tips = [
        {
            "title": "组合参数使用",
            "examples": [
                "python main.py --full-mode --both-status --latest-n 50 --enable-zip-package --save-raw-data",
                "python main.py --incremental-mode --case-ids 5766,7514 --base-version 20250524_142348"
            ]
        },
        {
            "title": "批量处理Patch",
            "examples": [
                "# 批量查看所有Patch",
                "for patch in data/patches/*.zip; do",
                "    echo \"=== $patch ===\"",
                "    python patch_applier.py --mode info --patches \"$patch\"",
                "done"
            ]
        },
        {
            "title": "数据验证",
            "examples": [
                "# 验证Patch完整性",
                "python -c \"",
                "import zipfile",
                "with zipfile.ZipFile('patch.zip', 'r') as z:",
                "    print('文件列表:', z.namelist())\"",
                "",
                "# 检查数据一致性",
                "python -c \"",
                "import json",
                "with open('merged.json') as f:",
                "    data = json.load(f)",
                "    print('工单数量:', len(data['cases']))\""
            ]
        }
    ]
    
    for tip in tips:
        print(f"💡 {tip['title']}")
        for example in tip['examples']:
            if example.startswith('#') or example == "":
                print(f"  {example}")
            else:
                print(f"  {example}")
        print()
    
    print("\n📁 输出文件结构")
    print("-" * 40)
    
    print("""
data/
├── full_20250524_142348/          # 全量模式目录输出
│   ├── metadata.json              # Patch元数据
│   ├── patch_content.json         # 完整内容
│   └── attachments/               # 附件文件
├── full_20250524_142348.zip       # 全量模式ZIP输出
├── patches/                       # 增量Patch目录
│   ├── patch_20250524_143256.zip  # 增量Patch文件
│   └── patch_20250524_144512.zip
├── applied/                       # Patch应用结果
│   ├── new_cases_20250524_143500.json
│   ├── merged_20250524_143500.json
│   └── repacked_20250524_143500.zip
└── cases_data.json               # 标准模式输出（兼容）
    """)
    
    print("\n🔍 故障排除")
    print("-" * 40)
    
    troubleshooting = [
        {
            "problem": "Patch解析失败",
            "solutions": [
                "检查ZIP文件是否损坏",
                "验证patch_content.json是否存在",
                "确认JSON格式是否正确"
            ]
        },
        {
            "problem": "增量模式无变更",
            "solutions": [
                "检查基础版本是否正确",
                "确认工单数据是否真的有变化",
                "查看日志中的变更检测信息"
            ]
        },
        {
            "problem": "合并数据不完整",
            "solutions": [
                "验证基础数据文件完整性",
                "检查Patch文件是否按顺序应用",
                "确认没有重复的工单ID冲突"
            ]
        }
    ]
    
    for item in troubleshooting:
        print(f"❓ {item['problem']}")
        for solution in item['solutions']:
            print(f"  • {solution}")
        print()


def main():
    """主函数"""
    show_usage_examples()
    
    print("\n" + "=" * 80)
    print("更多信息请参考:")
    print("• README.md - 基础使用说明")
    print("• DATA_MODE_SOLUTION.md - 详细技术方案")
    print("• NEW_FEATURES_SUMMARY.md - 新功能总结")
    print("=" * 80)


if __name__ == "__main__":
    main()
