#!/usr/bin/env python3
"""
测试数据生成器
生成包含图片、附件等完整信息的测试数据
"""
import os
import sys
import json
import shutil
import tempfile
from datetime import datetime, timedelta
from typing import List, Dict, Any
import base64

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from models import CaseDetail, CaseSummary, CaseComment, CaseAttachment
from utils import logger, save_json


class TestDataGenerator:
    """测试数据生成器"""
    
    def __init__(self):
        self.test_data_dir = os.path.join(os.path.dirname(__file__), "test_data_complete")
        self.attachments_dir = os.path.join(self.test_data_dir, "attachments")
        os.makedirs(self.test_data_dir, exist_ok=True)
        os.makedirs(self.attachments_dir, exist_ok=True)
    
    def create_test_image(self, filename: str, size: tuple = (100, 100)) -> str:
        """创建测试图片文件"""
        try:
            from PIL import Image, ImageDraw
            
            # 创建一个简单的测试图片
            img = Image.new('RGB', size, color='lightblue')
            draw = ImageDraw.Draw(img)
            
            # 绘制一些内容
            draw.rectangle([10, 10, size[0]-10, size[1]-10], outline='blue', width=2)
            draw.text((20, 20), f"Test\nImage\n{filename}", fill='darkblue')
            
            # 保存图片
            image_path = os.path.join(self.attachments_dir, filename)
            img.save(image_path, 'PNG')
            
            return image_path
        except ImportError:
            # 如果没有PIL，创建一个简单的文本文件作为替代
            image_path = os.path.join(self.attachments_dir, filename.replace('.png', '.txt'))
            with open(image_path, 'w', encoding='utf-8') as f:
                f.write(f"Test image placeholder: {filename}\n")
                f.write(f"Created at: {datetime.now()}\n")
                f.write("This is a test image file for testing purposes.")
            return image_path
    
    def create_test_attachment(self, filename: str, content: str) -> str:
        """创建测试附件文件"""
        file_path = os.path.join(self.attachments_dir, filename)
        
        if filename.endswith('.json'):
            # JSON文件
            data = {"test": content, "timestamp": datetime.now().isoformat()}
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        elif filename.endswith('.txt'):
            # 文本文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"测试附件: {content}\n")
                f.write(f"创建时间: {datetime.now()}\n")
                f.write("这是一个测试附件文件，用于验证附件下载和处理功能。\n")
                f.write("内容包含中文字符以测试编码处理。\n")
        elif filename.endswith('.csv'):
            # CSV文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write("ID,名称,描述,时间\n")
                f.write(f"1,{content},测试数据1,{datetime.now()}\n")
                f.write(f"2,{content}_2,测试数据2,{datetime.now()}\n")
        else:
            # 其他文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"Test file: {filename}\n")
                f.write(f"Content: {content}\n")
                f.write(f"Generated: {datetime.now()}\n")
        
        return file_path
    
    def generate_test_case_detail(self, case_id: int, scenario: str) -> CaseDetail:
        """生成测试工单详情"""
        base_time = datetime.now() - timedelta(days=case_id)
        
        # 根据场景设置不同的数据
        scenarios = {
            "basic": {
                "subject": f"基础测试工单{case_id}",
                "product_line": "MP01",
                "description": f"这是一个基础测试工单，ID为{case_id}",
                "has_images": False,
                "attachment_count": 1
            },
            "with_images": {
                "subject": f"包含图片的工单{case_id}",
                "product_line": "MP02", 
                "description": f"这个工单包含图片内容 <img src='test_image_{case_id}.png' alt='测试图片'>",
                "has_images": True,
                "attachment_count": 2
            },
            "complex": {
                "subject": f"复杂工单{case_id}",
                "product_line": "MP03",
                "description": f"复杂工单包含多种内容 <img src='diagram_{case_id}.png'> 和链接",
                "has_images": True,
                "attachment_count": 3
            },
            "updated": {
                "subject": f"更新后的工单{case_id}",
                "product_line": "MP01",
                "description": f"这是更新后的工单描述，ID为{case_id}，包含新内容",
                "has_images": False,
                "attachment_count": 2
            }
        }
        
        scenario_data = scenarios.get(scenario, scenarios["basic"])
        
        # 创建工单概要
        summary = CaseSummary(
            id=case_id,
            case_number=f"TEST{case_id:06d}",
            subject=scenario_data["subject"],
            product_line=scenario_data["product_line"],
            product_name=f"测试产品{scenario_data['product_line']}",
            project_name=f"项目{scenario_data['product_line']}",
            cnum=scenario_data["product_line"],
            product_code=f"T{case_id:03d}",
            case_level=1 if case_id % 2 == 0 else 2,
            issue_type=case_id % 3 + 1,
            issue_description=scenario_data["description"],
            attach=1 if scenario_data["attachment_count"] > 0 else 0,
            priority=case_id % 5 + 1,
            version="v1.0",
            notify=None,
            status=5 if case_id % 3 != 0 else 4,  # 大部分是Closed，少部分是Open
            uid=100 + case_id % 10,
            deadline=None,
            is_admin_create=0,
            created_at=(base_time - timedelta(hours=2)).strftime('%Y-%m-%d %H:%M:%S'),
            updated_at=base_time.strftime('%Y-%m-%d %H:%M:%S'),
            company_name=f"测试公司{case_id % 3 + 1}",
            username=f"测试用户{case_id % 5 + 1}"
        )
        
        # 创建评论
        comments = []
        comment_count = case_id % 4 + 1  # 1-4条评论
        
        for i in range(comment_count):
            comment_time = base_time - timedelta(hours=i)
            comment_content = f"这是工单{case_id}的第{i+1}条评论。"
            
            # 某些评论包含图片
            if scenario_data["has_images"] and i == 0:
                comment_content += f" <img src='comment_image_{case_id}_{i}.png' alt='评论图片'>"
            
            comment = CaseComment(
                source=f"用户{(case_id + i) % 5 + 1}",
                content=comment_content,
                timestamp=comment_time.strftime('%Y-%m-%d %H:%M:%S')
            )
            comments.append(comment)
        
        # 创建附件
        attachments = []
        for i in range(scenario_data["attachment_count"]):
            attachment_id = f"att_{case_id}_{i:03d}"
            
            if i == 0:
                # 第一个附件总是文档
                filename = f"document_{case_id}.txt"
                file_path = self.create_test_attachment(filename, f"工单{case_id}文档")
            elif i == 1 and scenario_data["has_images"]:
                # 第二个附件是图片（如果场景包含图片）
                filename = f"image_{case_id}.png"
                file_path = self.create_test_image(filename)
            else:
                # 其他附件
                filename = f"data_{case_id}_{i}.csv"
                file_path = self.create_test_attachment(filename, f"数据{case_id}_{i}")
            
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 1024
            
            attachment = CaseAttachment(
                attachment_id=attachment_id,
                file_name=filename,
                file_size=str(file_size),
                owner=f"用户{case_id % 3 + 1}",
                last_modified=(base_time - timedelta(minutes=i*10)).strftime('%Y-%m-%d %H:%M:%S'),
                download_url=f"http://test.akrocare.com/download/{attachment_id}",
                local_path=file_path
            )
            attachments.append(attachment)
        
        return CaseDetail(
            summary=summary,
            comments=comments,
            attachments=attachments
        )
    
    def generate_base_dataset(self) -> List[CaseDetail]:
        """生成基础数据集"""
        logger.info("生成基础数据集...")
        
        base_cases = []
        
        # 生成不同类型的工单
        test_scenarios = [
            (1001, "basic"),
            (1002, "with_images"), 
            (1003, "complex"),
            (1004, "basic"),
            (1005, "with_images"),
        ]
        
        for case_id, scenario in test_scenarios:
            case_detail = self.generate_test_case_detail(case_id, scenario)
            base_cases.append(case_detail)
            logger.info(f"生成基础工单: {case_id} - {scenario}")
        
        return base_cases
    
    def generate_incremental_dataset(self, base_cases: List[CaseDetail]) -> List[CaseDetail]:
        """生成增量数据集"""
        logger.info("生成增量数据集...")
        
        incremental_cases = []
        
        # 1. 包含一些基础数据（无变化）
        incremental_cases.append(base_cases[0])  # 1001 - 无变化
        
        # 2. 包含更新的工单
        updated_case = self.generate_test_case_detail(1002, "updated")
        incremental_cases.append(updated_case)
        logger.info(f"生成更新工单: 1002 - updated")
        
        # 3. 包含新增工单
        new_scenarios = [
            (1006, "basic"),
            (1007, "with_images"),
            (1008, "complex"),
        ]
        
        for case_id, scenario in new_scenarios:
            case_detail = self.generate_test_case_detail(case_id, scenario)
            incremental_cases.append(case_detail)
            logger.info(f"生成新增工单: {case_id} - {scenario}")
        
        return incremental_cases
    
    def save_test_datasets(self):
        """保存测试数据集"""
        logger.info("开始生成完整测试数据集...")
        
        # 生成基础数据集
        base_cases = self.generate_base_dataset()
        base_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "type": "base_dataset",
                "case_count": len(base_cases),
                "description": "基础测试数据集，包含5个工单"
            },
            "cases": [case.to_dict() for case in base_cases]
        }
        
        base_file = os.path.join(self.test_data_dir, "base_dataset.json")
        save_json(base_data, base_file)
        logger.info(f"基础数据集已保存: {base_file}")
        
        # 生成增量数据集
        incremental_cases = self.generate_incremental_dataset(base_cases)
        incremental_data = {
            "metadata": {
                "generated_at": datetime.now().isoformat(),
                "type": "incremental_dataset", 
                "case_count": len(incremental_cases),
                "description": "增量测试数据集，包含1个无变化、1个更新、3个新增工单"
            },
            "cases": [case.to_dict() for case in incremental_cases]
        }
        
        incremental_file = os.path.join(self.test_data_dir, "incremental_dataset.json")
        save_json(incremental_data, incremental_file)
        logger.info(f"增量数据集已保存: {incremental_file}")
        
        # 生成附件清单
        attachment_manifest = {
            "generated_at": datetime.now().isoformat(),
            "attachments_dir": self.attachments_dir,
            "files": []
        }
        
        for root, dirs, files in os.walk(self.attachments_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path)
                attachment_manifest["files"].append({
                    "filename": file,
                    "path": file_path,
                    "size": file_size,
                    "type": "image" if file.endswith(('.png', '.jpg', '.jpeg')) else "document"
                })
        
        manifest_file = os.path.join(self.test_data_dir, "attachment_manifest.json")
        save_json(attachment_manifest, manifest_file)
        logger.info(f"附件清单已保存: {manifest_file}")
        
        return base_cases, incremental_cases
    
    def create_test_summary(self):
        """创建测试数据摘要"""
        summary = {
            "test_data_overview": {
                "generated_at": datetime.now().isoformat(),
                "generator_version": "1.0",
                "total_cases": 8,  # 5个基础 + 3个新增
                "base_cases": 5,
                "incremental_cases": 5,  # 1个无变化 + 1个更新 + 3个新增
                "new_cases": 3,
                "updated_cases": 1,
                "unchanged_cases": 1
            },
            "test_scenarios": {
                "basic": "基础工单，包含基本信息和1个附件",
                "with_images": "包含图片的工单，描述和评论中有图片",
                "complex": "复杂工单，包含多种附件和图片",
                "updated": "更新后的工单，用于测试增量检测"
            },
            "attachment_types": {
                "documents": "文本文档 (.txt)",
                "images": "图片文件 (.png)",
                "data": "数据文件 (.csv)",
                "config": "配置文件 (.json)"
            },
            "test_coverage": {
                "product_lines": ["MP01", "MP02", "MP03"],
                "case_statuses": [4, 5],  # Open, Closed
                "priority_levels": [1, 2, 3, 4, 5],
                "case_levels": [1, 2],
                "issue_types": [1, 2, 3]
            }
        }
        
        summary_file = os.path.join(self.test_data_dir, "test_data_summary.json")
        save_json(summary, summary_file)
        logger.info(f"测试数据摘要已保存: {summary_file}")
        
        return summary


def main():
    """主函数"""
    print("=" * 60)
    print("测试数据生成器")
    print("=" * 60)
    
    generator = TestDataGenerator()
    
    try:
        # 生成测试数据集
        base_cases, incremental_cases = generator.save_test_datasets()
        
        # 创建测试摘要
        summary = generator.create_test_summary()
        
        print(f"\n✅ 测试数据生成完成!")
        print(f"📁 数据目录: {generator.test_data_dir}")
        print(f"📁 附件目录: {generator.attachments_dir}")
        print(f"📊 基础工单: {len(base_cases)} 个")
        print(f"📊 增量工单: {len(incremental_cases)} 个")
        print(f"📎 附件文件: {len(summary['test_data_overview'])} 个")
        
        print(f"\n📋 生成的文件:")
        for file in os.listdir(generator.test_data_dir):
            if file.endswith('.json'):
                file_path = os.path.join(generator.test_data_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"  - {file} ({file_size} bytes)")
        
        print(f"\n📎 附件文件:")
        for file in os.listdir(generator.attachments_dir):
            file_path = os.path.join(generator.attachments_dir, file)
            file_size = os.path.getsize(file_path)
            file_type = "图片" if file.endswith(('.png', '.jpg')) else "文档"
            print(f"  - {file} ({file_size} bytes, {file_type})")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试数据生成失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
