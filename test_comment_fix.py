#!/usr/bin/env python3
"""
测试修复后的评论提取功能
"""
import os
import sys
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from case_collector import CaseCollector
from utils import logger
import logging
import config

# 设置日志级别为DEBUG以查看详细信息
logging.getLogger().setLevel(logging.DEBUG)

# 临时禁用增量更新以测试评论提取功能
original_incremental_setting = config.ENABLE_INCREMENTAL_UPDATE
config.ENABLE_INCREMENTAL_UPDATE = False


def test_demo_html_extraction():
    """测试从demo-content.html提取评论"""
    print("=" * 60)
    print("测试修复后的评论提取功能")
    print("=" * 60)

    # 检查文件是否存在
    if not os.path.exists('demo-content.html'):
        print("❌ demo-content.html文件不存在")
        return False

    # 加载HTML内容
    try:
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        print(f"✅ 成功加载HTML文件，长度: {len(html_content)} 字符")
    except Exception as e:
        print(f"❌ 加载HTML文件失败: {e}")
        return False

    # 解析HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # 创建采集器并提取评论
    collector = CaseCollector()
    comments = collector._extract_comments(soup, 5766)

    # 验证结果
    print(f"\n📊 提取结果:")
    print(f"   评论数量: {len(comments)}")

    if len(comments) > 0:
        print(f"✅ 成功提取到 {len(comments)} 条评论")

        print(f"\n📝 评论详情:")
        for i, comment in enumerate(comments, 1):
            print(f"   评论 {i}:")
            print(f"     来源: {comment.source}")
            print(f"     时间: {comment.timestamp}")
            print(f"     内容: {comment.content[:100]}...")
            print()

        return True
    else:
        print("❌ 未能提取到评论")
        return False


def test_fallback_method():
    """测试降级方法"""
    print("=" * 60)
    print("测试降级方法")
    print("=" * 60)

    # 创建测试HTML（没有akro-comment类）
    html_content = """
    <html>
    <body>
        <h1>Reply Content</h1>
        <div>
            <div class="col-md-2">测试用户A</div>
            <div class="col-md-10 well">
                这是第一条测试评论内容，包含一些详细的描述信息。
                <hr>
                <span>2025-01-01 12:00:00</span>
            </div>
        </div>
        <div>
            <div class="col-md-2">测试用户B</div>
            <div class="col-md-10 well">
                这是第二条测试评论，回复了前面的问题。
                <hr>
                <span>2025-01-01 15:30:00</span>
            </div>
        </div>
    </body>
    </html>
    """

    soup = BeautifulSoup(html_content, 'html.parser')

    # 创建采集器并提取评论
    collector = CaseCollector()
    comments = collector._extract_comments(soup, 123)

    # 验证结果
    print(f"📊 降级方法提取结果:")
    print(f"   评论数量: {len(comments)}")

    if len(comments) == 2:
        print(f"✅ 降级方法成功提取到 {len(comments)} 条评论")

        for i, comment in enumerate(comments, 1):
            print(f"   评论 {i}: {comment.source} - {comment.timestamp}")
            print(f"     内容: {comment.content}")

        return True
    else:
        print("❌ 降级方法未能正确提取评论")
        return False


def main():
    """主函数"""
    print("评论提取功能修复验证")
    print()

    # 测试1: demo-content.html
    success1 = test_demo_html_extraction()

    print()

    # 测试2: 降级方法
    success2 = test_fallback_method()

    print()
    print("=" * 60)
    print("测试总结")
    print("=" * 60)

    if success1 and success2:
        print("🎉 所有测试通过！评论提取功能修复成功")
        print()
        print("修复要点:")
        print("1. ✅ 原始方法：支持akro-comment容器结构")
        print("2. ✅ 降级方法：支持Reply Content区域结构")
        print("3. ✅ 时间提取：正确识别和提取时间戳")
        print("4. ✅ 内容清理：移除时间span和hr元素")
        print("5. ✅ 增量更新：支持基于时间的增量检查")
    else:
        print("❌ 部分测试失败，需要进一步调试")
        if not success1:
            print("   - demo-content.html提取失败")
        if not success2:
            print("   - 降级方法测试失败")

    # 恢复原始设置
    config.ENABLE_INCREMENTAL_UPDATE = original_incremental_setting


if __name__ == "__main__":
    main()
