#!/usr/bin/env python3
"""
调试工单5766的HTML内容
查看实际的HTML结构以便修正解析逻辑
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from case_collector import CaseCollector
from utils import logger


def debug_case_5766_html():
    """调试工单5766的HTML内容"""
    print("=" * 80)
    print("调试工单5766的HTML内容")
    print("=" * 80)
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    original_save_raw_data = config.SAVE_RAW_DATA
    original_pack_raw_data = config.PACK_RAW_DATA
    
    try:
        # 设置临时配置
        config.DATA_DIR = temp_dir
        config.SAVE_RAW_DATA = True
        config.PACK_RAW_DATA = True
        config.RAW_DATA_DIR = os.path.join(temp_dir, "raw_data")
        
        print(f"✅ 调试环境设置完成")
        print(f"   临时目录: {temp_dir}")
        print(f"   原始数据目录: {config.RAW_DATA_DIR}")
        
        # 创建工单采集器
        with CaseCollector() as collector:
            print(f"\n🔍 开始获取工单5766的HTML内容...")
            
            # 直接访问工单5766页面
            detail_url = config.CASE_DETAIL_URL_TEMPLATE.format(5766)
            print(f"访问URL: {detail_url}")
            
            collector.driver.get(detail_url)
            
            # 等待页面加载
            import time
            time.sleep(5)
            
            # 获取页面源码
            page_source = collector.driver.page_source
            
            # 保存完整HTML到文件
            html_file = os.path.join(temp_dir, "case_5766_debug.html")
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(page_source)
            
            print(f"✅ HTML内容已保存到: {html_file}")
            
            # 使用BeautifulSoup解析
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 查找所有可能的附件相关内容
            print(f"\n🔍 分析HTML结构...")
            
            # 1. 查找所有包含"Attachments"的文本
            attachments_texts = soup.find_all(string=lambda text: text and 'Attachments' in text)
            print(f"1. 包含'Attachments'的文本: {len(attachments_texts)} 个")
            for i, text in enumerate(attachments_texts, 1):
                print(f"   {i}. '{text.strip()}'")
            
            # 2. 查找所有表格
            tables = soup.find_all('table')
            print(f"\n2. 页面中的表格: {len(tables)} 个")
            for i, table in enumerate(tables, 1):
                # 获取表格的前几行内容
                rows = table.find_all('tr')[:3]  # 只看前3行
                print(f"   表格 {i}: {len(table.find_all('tr'))} 行")
                for j, row in enumerate(rows):
                    cells = row.find_all(['th', 'td'])
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    print(f"     行 {j+1}: {cell_texts}")
                print()
            
            # 3. 查找所有包含"DDRPHY"的内容（基于您提供的附件名）
            ddrphy_texts = soup.find_all(string=lambda text: text and 'DDRPHY' in text)
            print(f"3. 包含'DDRPHY'的文本: {len(ddrphy_texts)} 个")
            for i, text in enumerate(ddrphy_texts, 1):
                print(f"   {i}. '{text.strip()}'")
                # 查找父元素
                parent = text.parent
                if parent:
                    print(f"      父元素: {parent.name} - {parent.get('class', [])}")
            
            # 4. 查找所有下载链接
            download_links = soup.find_all('a', href=lambda href: href and 'download' in href)
            print(f"\n4. 下载链接: {len(download_links)} 个")
            for i, link in enumerate(download_links, 1):
                href = link.get('href', '')
                text = link.get_text(strip=True)
                print(f"   {i}. 文本: '{text}' -> {href}")
            
            # 5. 查找所有包含文件扩展名的文本
            file_extensions = ['.docx', '.pdf', '.txt', '.png', '.jpg', '.xlsx']
            for ext in file_extensions:
                ext_texts = soup.find_all(string=lambda text: text and ext in text)
                if ext_texts:
                    print(f"\n5. 包含'{ext}'的文本: {len(ext_texts)} 个")
                    for i, text in enumerate(ext_texts, 1):
                        print(f"   {i}. '{text.strip()}'")
            
            # 6. 查找所有class包含"akro"的元素
            akro_elements = soup.find_all(class_=lambda cls: cls and any('akro' in c for c in cls))
            print(f"\n6. class包含'akro'的元素: {len(akro_elements)} 个")
            for i, element in enumerate(akro_elements, 1):
                classes = element.get('class', [])
                tag = element.name
                text = element.get_text(strip=True)[:100]  # 只显示前100个字符
                print(f"   {i}. <{tag}> class={classes} - '{text}...'")
            
            # 7. 查找特定的HTML结构（基于您提供的HTML）
            print(f"\n7. 查找特定结构...")
            
            # 查找akro-attach
            akro_attach = soup.find(class_="akro-attach")
            if akro_attach:
                print(f"   ✅ 找到akro-attach元素")
                # 查找其中的表格
                attach_tables = akro_attach.find_all('table')
                print(f"   akro-attach中的表格: {len(attach_tables)} 个")
                
                for i, table in enumerate(attach_tables, 1):
                    print(f"   表格 {i}:")
                    rows = table.find_all('tr')
                    for j, row in enumerate(rows[:5]):  # 只显示前5行
                        cells = row.find_all(['th', 'td'])
                        cell_texts = [cell.get_text(strip=True) for cell in cells]
                        print(f"     行 {j+1}: {cell_texts}")
            else:
                print(f"   ❌ 未找到akro-attach元素")
            
            # 8. 保存关键部分的HTML
            if akro_attach:
                attach_html_file = os.path.join(temp_dir, "case_5766_attachments.html")
                with open(attach_html_file, 'w', encoding='utf-8') as f:
                    f.write(str(akro_attach))
                print(f"   附件部分HTML已保存到: {attach_html_file}")
            
            return True
    
    except Exception as e:
        print(f"❌ 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 恢复配置
        config.DATA_DIR = original_data_dir
        config.SAVE_RAW_DATA = original_save_raw_data
        config.PACK_RAW_DATA = original_pack_raw_data
        
        # 不清理临时目录，保留HTML文件供查看
        print(f"\n📁 HTML文件保存在: {temp_dir}")
        print(f"   - case_5766_debug.html: 完整页面HTML")
        print(f"   - case_5766_attachments.html: 附件部分HTML（如果存在）")


def analyze_saved_html():
    """分析保存的HTML文件"""
    print("\n" + "=" * 80)
    print("分析保存的HTML文件")
    print("=" * 80)
    
    try:
        # 查找最近的调试文件
        import glob
        temp_dirs = glob.glob("/tmp/tmp*") + glob.glob("C:/Users/<USER>/AppData/Local/Temp/tmp*")
        
        html_files = []
        for temp_dir in temp_dirs:
            html_file = os.path.join(temp_dir, "case_5766_debug.html")
            if os.path.exists(html_file):
                html_files.append(html_file)
        
        if not html_files:
            print("❌ 未找到保存的HTML文件")
            return False
        
        # 使用最新的文件
        html_file = max(html_files, key=os.path.getmtime)
        print(f"✅ 分析HTML文件: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单统计
        print(f"   文件大小: {len(content)} 字符")
        print(f"   包含'Attachments': {'Attachments' in content}")
        print(f"   包含'DDRPHY': {'DDRPHY' in content}")
        print(f"   包含'download': {'download' in content}")
        print(f"   包含'akro-attach': {'akro-attach' in content}")
        
        # 查找关键词位置
        if 'DDRPHY' in content:
            start = content.find('DDRPHY') - 200
            end = content.find('DDRPHY') + 200
            start = max(0, start)
            end = min(len(content), end)
            
            print(f"\n📋 DDRPHY附近的内容:")
            print(content[start:end])
        
        return True
        
    except Exception as e:
        print(f"❌ 分析HTML文件失败: {e}")
        return False


def main():
    """主函数"""
    print("🔍 工单5766 HTML内容调试")
    
    tests = [
        ("获取工单5766 HTML内容", debug_case_5766_html),
        ("分析保存的HTML文件", analyze_saved_html),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 完成")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 80)
    print("调试结果总结")
    print("=" * 80)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 完成" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总计: {passed}/{total} 测试完成")
    
    if passed > 0:
        print("🎉 HTML内容调试完成！")
        print("\n📋 下一步:")
        print("1. 查看保存的HTML文件")
        print("2. 分析附件表格的实际结构")
        print("3. 修正解析逻辑")
    else:
        print("⚠️  调试失败，需要检查网络连接和登录状态")
    
    return passed > 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
