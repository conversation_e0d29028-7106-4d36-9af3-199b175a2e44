#!/usr/bin/env python3
"""
工单采集程序演示脚本
"""
import os
import json
from datetime import datetime

from models import CaseSummary, CaseDetail, CaseComment, CaseAttachment
from utils import save_json, logger
import config


def create_demo_data():
    """创建演示数据"""
    logger.info("创建演示数据...")
    
    # 创建演示工单概要
    demo_case_summary = CaseSummary(
        id=7514,
        case_number="01747205919",
        subject="MP01 指标说明",
        product_line="MP01",
        product_name="PHY NAME",
        project_name="N001",
        cnum="PADFPA_AS_CoreProcess",
        product_code="S304-0",
        case_level=0,
        issue_type=1,
        issue_description="<p>1.的IP在10Gbps速率 Chip to Chip场景时的RX端接收眼图模板是什么？我们需要一个准确的标准来判断我们链路设计是否OK.</p><p>是否按照红色框框的标准就行。</p><p><img src=\"/uploads/ueditor/image/20250514/1747205872926102.png\" title=\"1747205872926102.png\" alt=\"image.png\"/></p><p><br/></p><p>2、 &nbsp;对于抖动的容忍指标是多少？一般Chip to Chip标准里是有的。</p>",
        attach=None,
        priority=4,
        version="2.10B",
        notify=None,
        status=3,
        uid=260,
        deadline=None,
        is_admin_create=0,
        created_at="2025-05-14 14:58:39",
        updated_at="2025-05-15 15:31:15",
        company_name="alicode",
        username="wf w"
    )
    
    # 创建演示评论
    demo_comments = [
        CaseComment(
            source="技术支持",
            content="感谢您的咨询。关于10Gbps速率下的RX端接收眼图模板，我们建议参考IEEE 802.3ae标准中的相关规范。",
            timestamp="2025-05-14 16:30:00"
        ),
        CaseComment(
            source="wf w",
            content="谢谢回复，能否提供更详细的技术文档？",
            timestamp="2025-05-15 09:15:00"
        ),
        CaseComment(
            source="技术支持",
            content="已为您准备了相关技术文档，请查看附件。如有其他问题，请随时联系我们。",
            timestamp="2025-05-15 15:30:00"
        )
    ]
    
    # 创建演示附件
    demo_attachments = [
        CaseAttachment(
            attachment_id="456",
            file_name="10G_PHY_Specification.pdf",
            file_size="2.5 MB",
            owner="技术支持",
            last_modified="2025-05-15 15:25:00",
            download_url="https://akrocare.akrostar-tech.com/download/456",
            local_path="data/attachments/7514/10G_PHY_Specification.pdf",
            version=1
        ),
        CaseAttachment(
            attachment_id="789",
            file_name="Eye_Diagram_Template.xlsx",
            file_size="1.2 MB",
            owner="技术支持",
            last_modified="2025-05-15 15:28:00",
            download_url="https://akrocare.akrostar-tech.com/download/789",
            local_path="data/attachments/7514/Eye_Diagram_Template.xlsx",
            version=1
        )
    ]
    
    # 创建完整的工单详情
    demo_case_detail = CaseDetail(
        summary=demo_case_summary,
        comments=demo_comments,
        attachments=demo_attachments
    )
    
    return [demo_case_detail]


def save_demo_data(case_details):
    """保存演示数据"""
    logger.info("保存演示数据...")
    
    # 确保目录存在
    os.makedirs(config.DATA_DIR, exist_ok=True)
    os.makedirs(config.ATTACHMENTS_DIR, exist_ok=True)
    
    # 保存工单数据
    data_to_save = [case_detail.to_dict() for case_detail in case_details]
    save_json(data_to_save, config.CASES_DATA_FILE)
    
    # 创建演示附件目录
    case_dir = os.path.join(config.ATTACHMENTS_DIR, "7514")
    os.makedirs(case_dir, exist_ok=True)
    
    # 创建演示附件文件（空文件）
    for case_detail in case_details:
        for attachment in case_detail.attachments:
            if attachment.local_path:
                file_path = attachment.local_path
                os.makedirs(os.path.dirname(file_path), exist_ok=True)
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(f"演示附件: {attachment.file_name}\n")
                    f.write(f"文件大小: {attachment.file_size}\n")
                    f.write(f"所有者: {attachment.owner}\n")
                    f.write(f"最后修改: {attachment.last_modified}\n")
    
    logger.info(f"演示数据已保存到: {config.CASES_DATA_FILE}")


def display_demo_data():
    """显示演示数据"""
    logger.info("显示演示数据...")
    
    if not os.path.exists(config.CASES_DATA_FILE):
        logger.error("演示数据文件不存在，请先运行创建演示数据")
        return
    
    with open(config.CASES_DATA_FILE, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print("\n" + "="*80)
    print("工单采集演示数据")
    print("="*80)
    
    for case_data in data:
        summary = case_data['summary']
        comments = case_data['comments']
        attachments = case_data['attachments']
        
        print(f"\n工单ID: {summary['id']}")
        print(f"工单编号: {summary['case_number']}")
        print(f"标题: {summary['subject']}")
        print(f"产品线: {summary['product_line']}")
        print(f"项目名称: {summary['project_name']}")
        print(f"状态: {summary['status']}")
        print(f"创建时间: {summary['created_at']}")
        print(f"更新时间: {summary['updated_at']}")
        print(f"提单人: {summary['username']}")
        
        print(f"\n评论数量: {len(comments)}")
        for i, comment in enumerate(comments, 1):
            print(f"  评论{i}: {comment['source']} - {comment['timestamp']}")
            print(f"    内容: {comment['content'][:100]}...")
        
        print(f"\n附件数量: {len(attachments)}")
        for i, attachment in enumerate(attachments, 1):
            print(f"  附件{i}: {attachment['file_name']} ({attachment['file_size']})")
            print(f"    本地路径: {attachment['local_path']}")
            print(f"    版本: v{attachment['version']}")
    
    print("\n" + "="*80)


def main():
    """主函数"""
    print("AkroCare工单采集程序演示")
    print("1. 创建演示数据")
    print("2. 显示演示数据")
    print("3. 退出")
    
    while True:
        choice = input("\n请选择操作 (1-3): ").strip()
        
        if choice == '1':
            demo_data = create_demo_data()
            save_demo_data(demo_data)
            print("演示数据创建完成！")
            
        elif choice == '2':
            display_demo_data()
            
        elif choice == '3':
            print("退出演示程序")
            break
            
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
