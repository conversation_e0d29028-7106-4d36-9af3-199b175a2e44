# 新功能总结

本文档总结了AkroCare工单采集程序的所有新增功能和改进。

## 🎯 新增采集模式

### 1. 指定工单ID采集模式
- **功能**: 采集特定ID的工单，从工单列表中搜索指定ID并获取真实概要信息
- **配置**: `SPECIFIC_CASE_IDS = [5766, 7514]`
- **命令**: `python main.py --case-ids 5766,7514`
- **实现**: 遍历工单列表页面，查找匹配的工单ID，获取真实的概要信息
- **优势**: 获取完整的工单概要信息（产品线、项目名称、标题等）
- **用途**: 精确采集指定工单，适用于调试和特定需求

### 2. 最新N条工单模式
- **功能**: 采集最新的N条工单（测试模式）
- **配置**: `LATEST_N_CASES = 10`
- **命令**: `python main.py --latest-n 10`
- **特点**: 不使用增量更新，每次都获取最新的N条
- **用途**: 测试和快速验证功能

### 3. 增量N条工单模式
- **功能**: 采集离上次更新后的N条工单
- **配置**: `INCREMENTAL_N_CASES = 5`
- **命令**: `python main.py --incremental-n 5`
- **特点**: 使用增量更新机制，避免重复采集
- **用途**: 定期增量采集，控制采集量

## 📊 工单状态识别和过滤

### 状态码识别
- **Open状态**: `status = 4`
- **Closed状态**: `status = 5`
- **配置**: `CASE_STATUS_OPEN = 4`, `CASE_STATUS_CLOSED = 5`

### 状态过滤模式
1. **采集两种状态**（推荐）
   - 配置: `COLLECT_BOTH_STATUS = True`
   - 命令: `python main.py --both-status`
   - 说明: 同时采集Open和Closed状态的工单

2. **只采集Open状态**
   - 命令: `python main.py --only-open`
   - 说明: 只采集进行中的工单

3. **只采集Closed状态**（默认）
   - 命令: `python main.py --only-closed`
   - 说明: 只采集已完成的工单

## 🛠️ 技术改进

### 1. 智能目录创建优化
- **问题**: 之前为每个工单都创建目录，即使没有附件或图片
- **改进**: 只有在存在附件或图片时才创建目录
- **检查逻辑**:
  - 检查是否有附件
  - 检查工单描述中是否有`<img>`标签
  - 检查评论内容中是否有`<img>`标签
- **效果**: 避免创建不必要的空目录

### 2. 产品线信息显示增强
- **功能**: 在提取工单详情时显示产品线和标题，并增加分隔线
- **输出示例**:
  ```
  ================================================================================
  INFO - 正在获取工单 5766 的详细信息...
  INFO - 产品线: MP01
  INFO - 工单标题: ddr phy 手册寄存器疑问汇总
  --------------------------------------------------------------------------------
  ... 采集过程 ...
  INFO - 工单 5766 详细信息采集完成
  ================================================================================
  ```
- **用途**: 便于监控采集进度和识别工单，清晰区分不同工单的处理过程

### 3. 代理禁用功能
- **功能**: 可配置的代理禁用，提高连接稳定性
- **配置**: `DISABLE_PROXY = True`
- **命令**: `--disable-proxy` / `--enable-proxy`
- **优势**: 避免代理干扰，提高请求成功率

## 🔍 原始数据保存功能

### 功能概述
- **目的**: 保存从网站获取的原始响应信息，方便调试
- **配置**: `SAVE_RAW_DATA = True`
- **命令**: `python main.py --save-raw-data`

### 保存内容
1. **API请求和响应信息**
   - 请求URL、方法、headers
   - 请求数据和响应状态码
   - 完整的响应内容

2. **工单列表JSON数据**
   - 每页的工单列表响应
   - 包含分页信息和状态过滤

3. **工单详情JSON数据**
   - 每个工单的完整详情
   - 包含概要、评论、附件信息

4. **HTML页面源码**
   - 工单详情页面的完整HTML
   - 便于调试页面解析问题

5. **附件信息**
   - 附件的元数据信息
   - 下载链接和文件信息

### 保存模式

#### 1. 打包模式（推荐）
- **配置**: `PACK_RAW_DATA = True`
- **命令**: `--pack-raw-data`
- **特点**:
  - 所有数据打包成ZIP文件
  - 文件名: `raw_data_YYYYMMDD_HHMMSS.zip`
  - 自动清理临时文件
  - 节省存储空间

#### 2. 分别保存模式
- **配置**: `PACK_RAW_DATA = False`
- **命令**: `--separate-raw-data`
- **特点**:
  - 按类型分别保存到不同目录
  - 便于单独查看和分析
  - 目录结构清晰

### 目录结构
```
raw_data/
├── raw_data_20250524_142348.zip    # 打包模式
├── cases_list/                     # 分别保存模式
│   └── cases_list_page1_status5_*.json
├── case_details/
│   └── case_detail_5766_*.json
├── attachments/
│   └── attachments_5766_*.json
└── html_pages/
    └── case_5766_detail_*.html
```

## 📋 元数据记录

### 自动添加的元数据
每个保存的文件都包含详细的元数据：

```json
{
  "metadata": {
    "timestamp": "2025-05-24T14:23:48.790000",
    "case_id": 5766,
    "data_type": "case_detail",
    "page": 1,
    "status": "5"
  },
  "response": {
    // 实际的响应数据
  }
}
```

### 会话管理
- **会话ID**: 每次运行生成唯一ID (`YYYYMMDD_HHMMSS`)
- **自动打包**: 会话结束时自动打包数据
- **摘要信息**: 提供会话的统计信息

## 🧪 测试和验证

### 新增测试文件
1. **test_new_features.py**: 测试所有新功能
2. **demo_new_features.py**: 新功能演示程序
3. **update_test_dataset.py**: 测试数据集更新
4. **validate_test_dataset.py**: 测试数据集验证

### 测试数据集
- **来源**: 基于真实的demo-content.html
- **内容**: 包含4条评论的完整工单数据
- **格式**: JSON格式，便于测试和验证
- **验证**: 自动化数据完整性检查

## 🚀 使用示例

### 基础使用
```bash
# 采集指定工单并保存原始数据
python main.py --case-ids 5766,7514 --save-raw-data --pack-raw-data

# 采集最新10条工单，包含两种状态
python main.py --latest-n 10 --both-status --disable-proxy

# 增量采集5条工单，使用调试模式
python main.py --incremental-n 5 --save-raw-data --separate-raw-data
```

### 高级使用
```bash
# 完整功能演示
python main.py \
  --both-status \
  --latest-n 20 \
  --save-raw-data \
  --pack-raw-data \
  --api-method requests \
  --disable-proxy
```

## 📊 性能和优化

### 改进点
1. **减少空目录创建**: 只在需要时创建目录
2. **智能代理控制**: 避免网络干扰
3. **原始数据打包**: 节省存储空间
4. **状态过滤**: 减少不必要的请求
5. **限制采集数量**: 控制资源使用

### 兼容性
- **向后兼容**: 所有原有功能保持不变
- **配置兼容**: 新配置项都有合理默认值
- **命令行兼容**: 原有命令行参数继续有效

## 🔧 配置建议

### 生产环境推荐配置
```python
# 基础配置
API_REQUEST_METHOD = 'requests'
DISABLE_PROXY = True
ENABLE_INCREMENTAL_UPDATE = True

# 采集模式（根据需求选择）
COLLECT_BOTH_STATUS = True
LATEST_N_CASES = 0  # 不限制

# 调试配置
SAVE_RAW_DATA = False  # 生产环境通常不需要
```

### 调试环境推荐配置
```python
# 基础配置
API_REQUEST_METHOD = 'requests'
DISABLE_PROXY = True

# 采集模式
LATEST_N_CASES = 10  # 限制数量便于调试
COLLECT_BOTH_STATUS = True

# 调试配置
SAVE_RAW_DATA = True
PACK_RAW_DATA = True
```

## 📈 未来扩展

### 已实现的扩展点
1. **采集模式**: 易于添加新的采集策略
2. **状态过滤**: 支持更多状态类型
3. **原始数据**: 可扩展保存更多类型的数据
4. **测试框架**: 完整的测试和验证体系

### 潜在改进方向
1. **并发采集**: 支持多线程采集
2. **增量策略**: 更智能的增量判断
3. **数据分析**: 内置数据分析功能
4. **监控告警**: 采集状态监控

---

*本文档记录了AkroCare工单采集程序的所有新增功能，确保用户能够充分利用这些改进来提高采集效率和质量。*
