#!/usr/bin/env python3
"""
运行所有测试的主脚本
包含测试数据生成、功能测试、综合测试等
"""
import os
import sys
import subprocess
import time
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils import logger


class TestRunner:
    """测试运行器"""
    
    def __init__(self):
        self.test_results = []
        self.start_time = datetime.now()
    
    def run_test_script(self, script_name: str, description: str) -> bool:
        """运行测试脚本"""
        logger.info(f"开始运行: {description}")
        logger.info(f"脚本: {script_name}")
        
        try:
            start_time = time.time()
            
            # 运行测试脚本
            result = subprocess.run([
                sys.executable, script_name
            ], capture_output=True, text=True, encoding='utf-8')
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = result.returncode == 0
            
            # 记录结果
            test_result = {
                "script": script_name,
                "description": description,
                "success": success,
                "duration": f"{duration:.2f}s",
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            self.test_results.append(test_result)
            
            if success:
                logger.info(f"✅ {description} - 成功 ({duration:.2f}s)")
            else:
                logger.error(f"❌ {description} - 失败 ({duration:.2f}s)")
                logger.error(f"错误输出: {result.stderr}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ {description} - 执行异常: {e}")
            
            test_result = {
                "script": script_name,
                "description": description,
                "success": False,
                "duration": "N/A",
                "return_code": -1,
                "stdout": "",
                "stderr": str(e)
            }
            
            self.test_results.append(test_result)
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 80)
        logger.info("开始运行完整测试套件")
        logger.info("=" * 80)
        
        # 定义测试脚本列表
        test_scripts = [
            {
                "script": "test_data_generator.py",
                "description": "测试数据生成器",
                "required": True
            },
            {
                "script": "test_data_modes.py", 
                "description": "数据模式基础测试",
                "required": True
            },
            {
                "script": "comprehensive_test_suite.py",
                "description": "综合测试套件",
                "required": True
            },
            {
                "script": "test_collector.py",
                "description": "采集器基础测试",
                "required": False
            },
            {
                "script": "test_new_features.py",
                "description": "新功能测试",
                "required": False
            }
        ]
        
        # 运行测试
        total_tests = 0
        passed_tests = 0
        required_failures = 0
        
        for test_config in test_scripts:
            script = test_config["script"]
            description = test_config["description"]
            required = test_config["required"]
            
            # 检查脚本是否存在
            if not os.path.exists(script):
                logger.warning(f"⚠️  测试脚本不存在: {script}")
                continue
            
            total_tests += 1
            success = self.run_test_script(script, description)
            
            if success:
                passed_tests += 1
            elif required:
                required_failures += 1
            
            # 在测试之间添加分隔
            logger.info("-" * 60)
        
        # 生成测试报告
        self.generate_final_report(total_tests, passed_tests, required_failures)
        
        return required_failures == 0
    
    def generate_final_report(self, total_tests: int, passed_tests: int, required_failures: int):
        """生成最终测试报告"""
        end_time = datetime.now()
        total_duration = end_time - self.start_time
        
        logger.info("=" * 80)
        logger.info("测试执行完成")
        logger.info("=" * 80)
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"必需测试失败: {required_failures}")
        logger.info(f"总执行时间: {total_duration}")
        
        # 详细结果
        logger.info("\n详细测试结果:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            logger.info(f"{status} {result['description']} ({result['duration']})")
            
            if not result["success"] and result["stderr"]:
                logger.info(f"   错误: {result['stderr'][:200]}...")
        
        # 保存详细报告
        report_data = {
            "summary": {
                "start_time": self.start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "total_duration": str(total_duration),
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": total_tests - passed_tests,
                "required_failures": required_failures,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%"
            },
            "test_results": self.test_results
        }
        
        try:
            from utils import save_json
            save_json(report_data, "all_tests_report.json")
            logger.info(f"\n📄 详细测试报告已保存: all_tests_report.json")
        except Exception as e:
            logger.warning(f"保存测试报告失败: {e}")
        
        # 最终结论
        if required_failures == 0:
            logger.info("\n🎉 所有必需测试通过！系统功能正常")
            if passed_tests == total_tests:
                logger.info("🌟 所有测试都通过了，系统状态完美！")
        else:
            logger.error(f"\n❌ {required_failures}个必需测试失败，需要修复")
    
    def run_quick_test(self):
        """运行快速测试（只运行核心功能）"""
        logger.info("=" * 80)
        logger.info("运行快速测试套件")
        logger.info("=" * 80)
        
        quick_tests = [
            ("test_data_modes.py", "数据模式基础测试"),
            ("comprehensive_test_suite.py", "综合功能测试")
        ]
        
        total = 0
        passed = 0
        
        for script, description in quick_tests:
            if os.path.exists(script):
                total += 1
                if self.run_test_script(script, description):
                    passed += 1
        
        logger.info(f"\n快速测试结果: {passed}/{total} 通过")
        return passed == total
    
    def run_data_tests_only(self):
        """只运行数据相关测试"""
        logger.info("=" * 80)
        logger.info("运行数据功能测试")
        logger.info("=" * 80)
        
        data_tests = [
            ("test_data_generator.py", "测试数据生成"),
            ("test_data_modes.py", "数据模式测试"),
        ]
        
        total = 0
        passed = 0
        
        for script, description in data_tests:
            if os.path.exists(script):
                total += 1
                if self.run_test_script(script, description):
                    passed += 1
        
        logger.info(f"\n数据测试结果: {passed}/{total} 通过")
        return passed == total


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="测试运行器")
    parser.add_argument('--mode', choices=['all', 'quick', 'data'], default='all',
                       help='测试模式: all(全部), quick(快速), data(数据)')
    parser.add_argument('--verbose', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        import logging
        logging.getLogger().setLevel(logging.DEBUG)
    
    runner = TestRunner()
    
    try:
        if args.mode == 'all':
            success = runner.run_all_tests()
        elif args.mode == 'quick':
            success = runner.run_quick_test()
        elif args.mode == 'data':
            success = runner.run_data_tests_only()
        else:
            logger.error(f"未知的测试模式: {args.mode}")
            return False
        
        return success
        
    except KeyboardInterrupt:
        logger.info("\n用户中断测试")
        return False
    except Exception as e:
        logger.error(f"测试运行器异常: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    
    print("\n" + "=" * 80)
    if success:
        print("🎉 测试执行成功！")
        print("\n可用的测试命令:")
        print("  python run_all_tests.py --mode all     # 运行所有测试")
        print("  python run_all_tests.py --mode quick   # 运行快速测试")
        print("  python run_all_tests.py --mode data    # 运行数据测试")
        print("  python run_all_tests.py --verbose      # 详细输出")
    else:
        print("❌ 测试执行失败，请检查错误信息")
    
    print("=" * 80)
    
    sys.exit(0 if success else 1)
