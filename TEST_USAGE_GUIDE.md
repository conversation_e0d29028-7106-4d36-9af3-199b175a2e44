# 测试使用指南

本指南详细说明如何使用完整的测试套件来验证AkroCare工单采集程序的所有功能。

## 🎯 测试概述

### 测试目标
- **功能验证**: 确保全量模式、增量模式、Patch应用等所有功能正常工作
- **数据完整性**: 验证图片、附件等多媒体内容的正确处理
- **性能测试**: 确保系统在各种数据量下的稳定性
- **兼容性测试**: 验证不同数据格式和模式间的兼容性

### 测试组件
- **测试数据生成器**: 创建包含图片、附件的完整测试数据
- **基础功能测试**: 验证数据模式的核心功能
- **综合测试套件**: 完整的端到端功能测试
- **测试运行器**: 自动化测试执行和报告生成

## 🚀 快速开始

### 1. 运行所有测试
```bash
# 运行完整测试套件
python run_all_tests.py

# 或者使用详细输出
python run_all_tests.py --verbose
```

### 2. 运行快速测试
```bash
# 只运行核心功能测试
python run_all_tests.py --mode quick
```

### 3. 运行数据测试
```bash
# 只运行数据相关测试
python run_all_tests.py --mode data
```

## 📋 详细测试说明

### 1. 测试数据生成

#### 执行命令
```bash
python test_data_generator.py
```

#### 功能说明
- 生成5个基础测试工单（不同场景）
- 生成5个增量测试工单（包含变更）
- 创建多种类型的附件文件
- 生成图片文件（或占位文件）
- 保存测试数据摘要和清单

#### 预期输出
```
test_data_complete/
├── base_dataset.json           # 基础数据集
├── incremental_dataset.json    # 增量数据集  
├── test_data_summary.json      # 测试摘要
├── attachment_manifest.json    # 附件清单
└── attachments/                # 附件文件
    ├── document_*.txt          # 文档附件
    ├── image_*.png             # 图片附件
    ├── data_*.csv              # 数据附件
    └── ...
```

#### 验证点
- ✅ 生成指定数量的测试工单
- ✅ 创建不同类型的附件文件
- ✅ 生成图片文件（PNG格式或占位文件）
- ✅ 保存完整的测试数据集
- ✅ 创建附件清单和摘要信息

### 2. 数据模式基础测试

#### 执行命令
```bash
python test_data_modes.py
```

#### 功能说明
- 测试全量模式的目录和ZIP输出
- 测试增量模式的变更检测
- 测试Patch应用的各种模式
- 验证数据格式兼容性

#### 测试场景
1. **全量模式测试**
   - 目录输出模式
   - ZIP打包模式
   - 元数据验证

2. **增量模式测试**
   - 变更检测逻辑
   - Patch生成
   - 版本管理

3. **Patch应用测试**
   - Patch解析
   - 空目录应用
   - 数据合并

### 3. 综合测试套件

#### 执行命令
```bash
python comprehensive_test_suite.py
```

#### 功能说明
这是最完整的测试套件，包含以下测试：

1. **数据生成测试**
   - 自动生成测试数据
   - 验证数据完整性

2. **附件处理测试**
   - 文件复制和组织
   - ZIP打包验证
   - 目录结构检查

3. **图片内容检测测试**
   - HTML图片标签识别
   - 工单描述图片统计
   - 评论图片统计

4. **全量模式测试**
   - 目录和ZIP两种输出
   - 元数据正确性
   - 附件包含验证

5. **增量模式测试**
   - 变更检测准确性
   - Patch内容验证
   - 版本链管理

6. **Patch应用测试**
   - 解析功能验证
   - 空目录应用测试
   - 数据合并测试
   - 重新打包测试

#### 预期结果
```
总测试数: 6
通过测试: 6
失败测试: 0
成功率: 100.0%
结论: 所有测试通过
```

## 📊 测试数据详情

### 测试工单场景

#### 基础数据集（5个工单）
| 工单ID | 场景类型 | 产品线 | 附件数 | 包含图片 | 说明 |
|--------|----------|--------|--------|----------|------|
| 1001 | basic | MP01 | 1 | ❌ | 基础工单，只有文档附件 |
| 1002 | with_images | MP02 | 2 | ✅ | 包含图片的工单 |
| 1003 | complex | MP03 | 3 | ✅ | 复杂工单，多种附件 |
| 1004 | basic | MP01 | 1 | ❌ | 另一个基础工单 |
| 1005 | with_images | MP02 | 2 | ✅ | 另一个图片工单 |

#### 增量数据集（5个工单）
| 工单ID | 变更类型 | 场景类型 | 说明 |
|--------|----------|----------|------|
| 1001 | 无变化 | basic | 与基础数据完全相同 |
| 1002 | 更新 | updated | 标题和描述已更新 |
| 1006 | 新增 | basic | 全新的基础工单 |
| 1007 | 新增 | with_images | 全新的图片工单 |
| 1008 | 新增 | complex | 全新的复杂工单 |

### 附件文件类型

#### 文档附件
- **document_*.txt**: 包含中文内容的文本文档
- **data_*.csv**: CSV格式的数据文件
- **config_*.json**: JSON格式的配置文件

#### 图片附件
- **image_*.png**: PNG格式的图片文件
- **diagram_*.png**: 图表类图片文件
- **comment_image_*.png**: 评论中的图片文件

### 测试覆盖范围

#### 数据类型覆盖
- **工单状态**: Open(4), Closed(5)
- **产品线**: MP01, MP02, MP03
- **优先级**: 1-5级
- **工单级别**: 1-2级
- **问题类型**: 1-3类

#### 功能覆盖
- **全量模式**: 目录输出、ZIP打包、元数据生成
- **增量模式**: 变更检测、Patch生成、版本管理
- **Patch应用**: 解析、空目录应用、数据合并、重新打包
- **附件处理**: 文件复制、目录组织、ZIP打包
- **图片检测**: HTML解析、内容识别、统计分析

## 🔧 高级测试用法

### 1. 单独运行特定测试

#### 只生成测试数据
```bash
python test_data_generator.py
```

#### 只测试全量模式
```bash
python -c "
from comprehensive_test_suite import ComprehensiveTestSuite
suite = ComprehensiveTestSuite()
suite.test_full_mode_with_attachments([])
"
```

#### 只测试Patch应用
```bash
python patch_applier.py --mode info --patches test_patch.zip
```

### 2. 自定义测试数据

#### 修改测试场景
编辑 `test_data_generator.py` 中的场景定义：

```python
scenarios = {
    "custom": {
        "subject": "自定义测试工单",
        "product_line": "MP04",
        "description": "自定义描述内容",
        "has_images": True,
        "attachment_count": 5
    }
}
```

#### 添加新的附件类型
```python
def create_custom_attachment(self, filename: str, content: str) -> str:
    # 自定义附件创建逻辑
    pass
```

### 3. 性能测试

#### 大数据量测试
```python
# 生成大量测试数据
def generate_large_dataset(self, count: int = 100):
    for i in range(count):
        case_detail = self.generate_test_case_detail(i + 2000, "basic")
        # 处理逻辑
```

#### 内存使用监控
```python
import psutil
import os

def monitor_memory_usage():
    process = psutil.Process(os.getpid())
    memory_info = process.memory_info()
    print(f"内存使用: {memory_info.rss / 1024 / 1024:.2f} MB")
```

## 📈 测试结果分析

### 成功标准
1. **所有测试通过**: 成功率达到100%
2. **数据完整性**: 生成的数据无丢失或损坏
3. **功能正确性**: 所有模式按预期工作
4. **性能要求**: 测试在合理时间内完成

### 失败处理
1. **查看详细日志**: 检查 `case_collector.log` 文件
2. **分析测试报告**: 查看 `comprehensive_test_report.json`
3. **检查中间文件**: 验证生成的临时文件
4. **重新运行**: 使用 `--verbose` 参数获取更多信息

### 常见问题

#### 1. 图片生成失败
**问题**: 没有安装PIL库
**解决**: 
```bash
pip install Pillow
```
或者程序会自动创建文本占位文件

#### 2. 附件文件缺失
**问题**: 文件权限或路径问题
**解决**: 检查目录权限，确保有写入权限

#### 3. 测试数据不一致
**问题**: 随机数据导致的差异
**解决**: 使用固定的随机种子或预定义数据

## 🎯 测试最佳实践

### 1. 定期运行测试
```bash
# 每日自动测试
crontab -e
0 2 * * * cd /path/to/project && python run_all_tests.py --mode quick
```

### 2. 持续集成
```yaml
# GitHub Actions 示例
name: Test Suite
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Install dependencies
      run: pip install -r requirements.txt
    - name: Run tests
      run: python run_all_tests.py
```

### 3. 测试数据管理
- 定期清理临时测试文件
- 备份重要的测试数据集
- 版本控制测试配置文件

### 4. 性能监控
- 记录测试执行时间
- 监控内存使用情况
- 分析性能瓶颈

## 📚 相关文档

- **技术方案**: `DATA_MODE_SOLUTION.md`
- **使用示例**: `data_mode_examples.py`
- **测试文档**: `COMPREHENSIVE_TEST_DOCUMENTATION.md`
- **实现总结**: `FULL_INCREMENTAL_MODE_SUMMARY.md`

---

*本指南提供了完整的测试使用说明，确保您能够充分验证系统的所有功能。*
