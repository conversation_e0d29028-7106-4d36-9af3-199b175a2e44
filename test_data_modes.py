#!/usr/bin/env python3
"""
测试全量模式和增量模式功能
"""
import os
import sys
import json
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from models import CaseDetail, CaseSummary, CaseComment, CaseAttachment
from data_mode_manager import DataModeManager, PatchMetadata, PatchContent
from patch_applier import PatchApplier


def create_test_case_detail(case_id: int, subject: str, product_line: str) -> CaseDetail:
    """创建测试工单详情"""
    summary = CaseSummary(
        id=case_id,
        case_number=f"TEST{case_id:06d}",
        subject=subject,
        product_line=product_line,
        product_name="测试产品",
        project_name="测试项目",
        cnum="TEST",
        product_code="T001",
        case_level=1,
        issue_type=1,
        issue_description=f"测试工单{case_id}的描述",
        attach=None,
        priority=4,
        version="v1.0",
        notify=None,
        status=5,
        uid=1,
        deadline=None,
        is_admin_create=0,
        created_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        updated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        company_name="测试公司",
        username="测试用户"
    )

    # 创建测试评论
    comments = [
        CaseComment(
            source="测试用户",
            content=f"这是工单{case_id}的测试评论",
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        )
    ]

    # 创建测试附件
    attachments = [
        CaseAttachment(
            attachment_id=f"att_{case_id}_001",
            file_name=f"test_file_{case_id}.txt",
            file_size="1024",
            owner="测试用户",
            last_modified=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            download_url=f"http://test.com/file_{case_id}.txt",
            local_path=""
        )
    ]

    return CaseDetail(
        summary=summary,
        comments=comments,
        attachments=attachments
    )


def test_full_mode():
    """测试全量模式"""
    print("=" * 60)
    print("测试全量模式")
    print("=" * 60)

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    config.DATA_DIR = temp_dir

    try:
        # 创建测试数据
        test_cases = [
            create_test_case_detail(1001, "测试工单1", "MP01"),
            create_test_case_detail(1002, "测试工单2", "MP02"),
            create_test_case_detail(1003, "测试工单3", "MP01"),
        ]

        # 创建数据管理器
        data_manager = DataModeManager()

        # 测试目录模式
        print("测试全量模式 - 目录输出")
        dir_path = data_manager.create_full_mode_package(test_cases, enable_zip=False)
        print(f"✅ 目录模式成功: {dir_path}")

        # 验证目录结构
        if os.path.exists(dir_path):
            files = os.listdir(dir_path)
            print(f"   生成文件: {files}")

            # 检查元数据
            metadata_file = os.path.join(dir_path, 'metadata.json')
            if os.path.exists(metadata_file):
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    metadata = json.load(f)
                print(f"   模式: {metadata['mode']}")
                print(f"   工单数量: {metadata['case_count']}")
                print(f"   新增工单: {len(metadata['new_cases'])}")

        # 测试ZIP模式
        print("\n测试全量模式 - ZIP输出")
        zip_path = data_manager.create_full_mode_package(test_cases, enable_zip=True)
        print(f"✅ ZIP模式成功: {zip_path}")

        # 验证ZIP文件
        if os.path.exists(zip_path):
            import zipfile
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                files = zipf.namelist()
                print(f"   ZIP内容: {files}")

        return True

    except Exception as e:
        print(f"❌ 全量模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理
        config.DATA_DIR = original_data_dir
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_incremental_mode():
    """测试增量模式"""
    print("\n" + "=" * 60)
    print("测试增量模式")
    print("=" * 60)

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    config.DATA_DIR = temp_dir

    try:
        # 创建数据管理器
        data_manager = DataModeManager()

        # 第一次：创建基础数据
        print("1. 创建基础数据")
        base_cases = [
            create_test_case_detail(1001, "基础工单1", "MP01"),
            create_test_case_detail(1002, "基础工单2", "MP02"),
        ]

        base_path = data_manager.create_full_mode_package(base_cases, enable_zip=True)
        print(f"✅ 基础数据创建: {base_path}")

        # 第二次：增量数据
        print("\n2. 创建增量数据")
        incremental_cases = [
            create_test_case_detail(1001, "基础工单1", "MP01"),  # 无变化
            create_test_case_detail(1002, "基础工单2-更新", "MP02"),  # 标题变化
            create_test_case_detail(1003, "新增工单3", "MP03"),  # 新增
        ]

        # 获取基础版本号
        base_version = os.path.basename(base_path).replace('full_', '').replace('.zip', '')

        patch_path = data_manager.create_incremental_patch(incremental_cases, base_version)
        print(f"✅ 增量Patch创建: {patch_path}")

        # 验证Patch内容
        if os.path.exists(patch_path):
            import zipfile
            with zipfile.ZipFile(patch_path, 'r') as zipf:
                if 'metadata.json' in zipf.namelist():
                    metadata_content = zipf.read('metadata.json').decode('utf-8')
                    metadata = json.loads(metadata_content)
                    print(f"   模式: {metadata['mode']}")
                    print(f"   基础版本: {metadata['base_version']}")
                    print(f"   新增工单: {metadata['new_cases']}")
                    print(f"   更新工单: {metadata['updated_cases']}")

        return True

    except Exception as e:
        print(f"❌ 增量模式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理
        config.DATA_DIR = original_data_dir
        shutil.rmtree(temp_dir, ignore_errors=True)


def test_patch_applier():
    """测试Patch应用器"""
    print("\n" + "=" * 60)
    print("测试Patch应用器")
    print("=" * 60)

    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    original_data_dir = config.DATA_DIR
    config.DATA_DIR = temp_dir

    try:
        # 创建测试Patch
        data_manager = DataModeManager()
        applier = PatchApplier()

        # 创建基础数据
        base_cases = [
            create_test_case_detail(2001, "基础工单A", "MP01"),
            create_test_case_detail(2002, "基础工单B", "MP02"),
        ]
        base_path = data_manager.create_full_mode_package(base_cases, enable_zip=True)

        # 创建增量Patch
        patch_cases = [
            create_test_case_detail(2003, "新增工单C", "MP03"),
            create_test_case_detail(2004, "新增工单D", "MP01"),
        ]

        # 模拟增量Patch（所有都是新增）
        patch_metadata = PatchMetadata(
            patch_id="test_patch_001",
            timestamp=datetime.now().isoformat(),
            mode="incremental",
            base_version=None,
            case_count=2,
            new_cases=[2003, 2004],
            updated_cases=[],
            description="测试Patch"
        )

        patch_content = PatchContent(
            metadata=patch_metadata,
            cases={
                "2003": patch_cases[0].to_dict(),
                "2004": patch_cases[1].to_dict()
            },
            attachments={}
        )

        # 保存测试Patch
        patch_dir = os.path.join(temp_dir, "test_patch")
        os.makedirs(patch_dir, exist_ok=True)
        data_manager._save_patch_to_directory(patch_content, patch_dir)

        patch_zip = f"{patch_dir}.zip"
        data_manager._create_zip_package(patch_dir, patch_zip)
        shutil.rmtree(patch_dir, ignore_errors=True)

        print("1. 测试Patch解析")
        parsed_patch = applier.parse_patch(patch_zip)
        if parsed_patch:
            print("✅ Patch解析成功")
            print(f"   Patch ID: {parsed_patch.metadata.patch_id}")
            print(f"   新增工单: {len(parsed_patch.metadata.new_cases)}")
        else:
            print("❌ Patch解析失败")
            return False

        print("\n2. 测试空目录应用")
        empty_output = os.path.join(temp_dir, "empty_result.json")
        success = applier.apply_patches_to_empty_directory([patch_zip], empty_output)
        if success and os.path.exists(empty_output):
            print("✅ 空目录应用成功")
            with open(empty_output, 'r', encoding='utf-8') as f:
                result = json.load(f)
                print(f"   结果工单数: {len(result['cases'])}")
        else:
            print("❌ 空目录应用失败")

        print("\n3. 测试现有数据合并")
        merge_output = os.path.join(temp_dir, "merge_result.json")
        success = applier.apply_patches_to_existing_data(base_path, [patch_zip], merge_output)
        if success and os.path.exists(merge_output):
            print("✅ 数据合并成功")
            with open(merge_output, 'r', encoding='utf-8') as f:
                result = json.load(f)
                print(f"   合并后工单数: {len(result['cases'])}")
        else:
            print("❌ 数据合并失败")

        return True

    except Exception as e:
        print(f"❌ Patch应用器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理
        config.DATA_DIR = original_data_dir
        shutil.rmtree(temp_dir, ignore_errors=True)


def main():
    """主函数"""
    print("全量模式和增量模式功能测试")
    print("=" * 60)

    tests = [
        ("全量模式", test_full_mode),
        ("增量模式", test_incremental_mode),
        ("Patch应用器", test_patch_applier)
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))

    # 总结
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    print(f"\n总计: {passed}/{total} 测试通过")

    if passed == total:
        print("🎉 所有测试通过！全量模式和增量模式功能正常")
        print("\n功能说明:")
        print("1. ✅ 全量模式支持目录和ZIP两种输出")
        print("2. ✅ 增量模式支持变更检测和Patch生成")
        print("3. ✅ Patch应用器支持解析、应用和合并")
        print("4. ✅ 数据格式兼容性良好")
        print("5. ✅ 元数据管理完善")
    else:
        print("⚠️  部分测试失败，需要检查相关功能")

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
