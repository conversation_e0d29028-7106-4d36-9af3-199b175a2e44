# 全量模式和增量模式解决方案

本文档详细说明了AkroCare工单采集程序的全量模式和增量模式实现方案。

## 📋 方案概述

### 设计目标
1. **全量模式**: 完整采集所有工单数据，支持ZIP打包，与Patch系统兼容
2. **增量模式**: 基于现有数据进行增量采集，生成Patch文件
3. **Patch管理**: 提供Patch应用、解析和合并功能
4. **兼容性**: 与现有系统完全兼容，支持多种数据格式

### 核心组件
- **DataModeManager**: 数据模式管理器
- **PatchApplier**: Patch应用器
- **PatchContent**: Patch内容结构
- **PatchMetadata**: Patch元数据结构

## 🔧 技术实现

### 1. 数据结构设计

#### PatchMetadata（Patch元数据）
```python
@dataclass
class PatchMetadata:
    patch_id: str           # Patch唯一标识
    timestamp: str          # 创建时间戳
    mode: str              # "full" 或 "incremental"
    base_version: str      # 基础版本（增量模式）
    case_count: int        # 工单数量
    new_cases: List[int]   # 新增工单ID列表
    updated_cases: List[int] # 更新工单ID列表
    description: str       # 描述信息
```

#### PatchContent（Patch内容）
```python
@dataclass
class PatchContent:
    metadata: PatchMetadata              # 元数据
    cases: Dict[str, Any]               # 工单数据
    attachments: Dict[str, List[str]]   # 附件文件路径
```

### 2. 全量模式实现

#### 功能特性
- 完整采集所有工单数据
- 支持目录和ZIP两种输出格式
- 自动标记为全量模式数据
- 与Patch系统完全兼容

#### 使用方法
```bash
# 全量模式 + 目录输出
python main.py --full-mode

# 全量模式 + ZIP打包
python main.py --full-mode --enable-zip-package

# 全量模式 + 指定工单
python main.py --full-mode --case-ids 5766,7514 --enable-zip-package
```

#### 输出结构
```
data/
├── full_20250524_142348/          # 目录模式
│   ├── metadata.json              # Patch元数据
│   ├── patch_content.json         # 完整内容
│   └── attachments/               # 附件文件
│       └── {case_id}/
└── full_20250524_142348.zip       # ZIP模式
```

### 3. 增量模式实现

#### 功能特性
- 基于现有数据进行差异分析
- 自动识别新增和更新的工单
- 生成紧凑的Patch文件
- 支持版本链式管理

#### 使用方法
```bash
# 增量模式（自动检测基础版本）
python main.py --incremental-mode

# 增量模式（指定基础版本）
python main.py --incremental-mode --base-version 20250524_142348

# 增量模式 + 最新10条
python main.py --incremental-mode --latest-n 10
```

#### 变更检测逻辑
1. **新增工单**: 工单ID在基础数据中不存在
2. **更新工单**: 工单的summary、comments或attachments有变化
3. **忽略工单**: 无任何变化的工单

#### 输出结构
```
data/patches/
└── patch_20250524_143256.zip      # Patch ZIP文件
    ├── metadata.json               # Patch元数据
    ├── patch_content.json          # 变更内容
    └── attachments/               # 新增/更新的附件
```

## 🛠️ Patch应用系统

### 1. Patch解析功能

#### 命令示例
```bash
# 显示Patch信息
python patch_applier.py --mode info --patches patch1.zip patch2.zip
```

#### 输出示例
```
================================================================================
Patch信息: patch_20250524_143256
================================================================================
时间戳: 2025-05-24T14:32:56.123456
模式: incremental
基础版本: 20250524_142348
工单总数: 5
描述: 增量采集，新增3个工单，更新2个工单

新增工单 (3个):
  - ID: 5766, 产品线: MP01, 标题: ddr phy 手册寄存器疑问汇总
  - ID: 7514, 产品线: MP02, 标题: 时钟配置问题
  - ID: 8901, 产品线: MP01, 标题: 接口调试问题

更新工单 (2个):
  - ID: 5432, 产品线: MP01, 标题: 原有工单更新
  - ID: 6789, 产品线: MP03, 标题: 状态变更工单

附件信息:
  工单 5766 (ddr phy 手册寄存器疑问汇总): 2 个附件
    - register_manual.pdf
    - timing_diagram.png
  工单 7514 (时钟配置问题): 1 个附件
    - clock_config.xlsx
================================================================================
```

### 2. 空目录应用模式

#### 功能说明
- 从多个Patch中提取新增工单
- 忽略对现有工单的更新
- 输出纯净的新增工单数据

#### 命令示例
```bash
# 应用到空目录
python patch_applier.py --mode empty --patches patch1.zip patch2.zip --output new_cases.json
```

#### 处理逻辑
1. 遍历所有Patch文件
2. 提取`new_cases`列表中的工单
3. 忽略`updated_cases`列表中的工单
4. 合并所有新增工单数据
5. 输出合并结果

#### 输出示例
```
忽略的更新工单:
  - 工单 5432: 原有工单更新
  - 工单 6789: 状态变更工单
  - 工单 7890: 评论更新工单

新增工单数据已保存到: data/applied/new_cases_20250524_143500.json
```

### 3. 现有数据合并模式

#### 功能说明
- 基于现有数据应用Patch
- 新增工单直接添加
- 更新工单覆盖原数据
- 保持数据完整性

#### 命令示例
```bash
# 合并到现有数据
python patch_applier.py --mode merge --base-data full_20250524_142348.zip --patches patch1.zip patch2.zip --output merged.json
```

#### 处理逻辑
1. 加载基础数据（ZIP/目录/JSON）
2. 按顺序应用每个Patch
3. 新增工单：直接添加到结果集
4. 更新工单：覆盖现有数据
5. 生成应用摘要

#### 输出示例
```
加载基础数据: 150 个工单

Patch应用摘要:
  patch_20250524_143256.zip: 新增 3 个，更新 2 个
  patch_20250524_144512.zip: 新增 1 个，更新 4 个

合并数据已保存到: data/applied/merged_20250524_143500.json
```

### 4. 重新打包功能

#### 功能说明
- 将应用后的数据重新打包成Patch
- 支持数据格式转换
- 便于数据分发和备份

#### 命令示例
```bash
# 重新打包
python patch_applier.py --mode repack --patches merged.json --output repacked.zip
```

## 📊 使用场景

### 场景1：初始部署
```bash
# 1. 全量采集
python main.py --full-mode --enable-zip-package

# 2. 查看数据包信息
python patch_applier.py --mode info --patches data/full_20250524_142348.zip
```

### 场景2：日常增量更新
```bash
# 1. 增量采集
python main.py --incremental-mode --latest-n 20

# 2. 查看Patch内容
python patch_applier.py --mode info --patches data/patches/patch_20250524_143256.zip

# 3. 应用到生产数据
python patch_applier.py --mode merge --base-data production_data.zip --patches data/patches/patch_20250524_143256.zip --output updated_production.json
```

### 场景3：多Patch合并
```bash
# 1. 收集多个Patch
ls data/patches/patch_*.zip

# 2. 提取新增工单
python patch_applier.py --mode empty --patches data/patches/patch_*.zip --output new_cases_summary.json

# 3. 完整合并
python patch_applier.py --mode merge --base-data base_data.zip --patches data/patches/patch_*.zip --output complete_merge.json

# 4. 重新打包
python patch_applier.py --mode repack --patches complete_merge.json --output final_package.zip
```

### 场景4：数据分析和调试
```bash
# 1. 分析Patch内容
python patch_applier.py --mode info --patches suspicious_patch.zip

# 2. 提取特定数据
python patch_applier.py --mode empty --patches patch1.zip patch2.zip --output analysis_data.json

# 3. 对比分析
python -c "
import json
with open('analysis_data.json') as f:
    data = json.load(f)
    cases = data['cases']
    print(f'总工单数: {len(cases)}')
    for case_id, case_data in cases.items():
        summary = case_data['summary']
        print(f'ID: {case_id}, 产品线: {summary[\"product_line\"]}, 标题: {summary[\"subject\"]}')
"
```

## 🔍 技术细节

### 1. 版本管理
- 版本号格式：`YYYYMMDD_HHMMSS`
- 自动版本检测和链式管理
- 支持指定基础版本

### 2. 数据兼容性
- 向后兼容现有JSON格式
- 支持ZIP和目录两种存储方式
- 自动格式检测和转换

### 3. 错误处理
- 完善的异常处理机制
- 详细的错误日志记录
- 优雅的降级处理

### 4. 性能优化
- 增量数据只包含变更部分
- ZIP压缩减少存储空间
- 流式处理大文件

## 📈 扩展性

### 1. 自定义过滤器
- 支持自定义变更检测逻辑
- 可配置的忽略字段
- 灵活的数据筛选

### 2. 插件系统
- 支持自定义数据处理器
- 可扩展的输出格式
- 模块化的功能组件

### 3. 集成接口
- RESTful API接口
- 命令行工具
- Python库调用

## 🚀 最佳实践

### 1. 数据管理
- 定期创建全量备份
- 保持Patch文件的有序管理
- 建立版本标记和文档

### 2. 性能优化
- 合理设置采集频率
- 及时清理过期数据
- 监控存储空间使用

### 3. 安全考虑
- 数据传输加密
- 访问权限控制
- 敏感信息脱敏

---

*本方案提供了完整的全量和增量数据管理解决方案，支持灵活的数据采集、存储、应用和分析需求。*
