#!/usr/bin/env python3
"""
从demo-content.html更新本地测试数据集
"""
import os
import sys
import json
from datetime import datetime
from bs4 import BeautifulSoup

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from case_collector import CaseCollector
from models import CaseSummary, CaseDetail, CaseComment, CaseAttachment
from utils import save_json, logger
import config


def extract_case_summary_from_demo():
    """从demo-content.html提取工单概要信息"""
    try:
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
    except FileNotFoundError:
        logger.error("demo-content.html文件不存在")
        return None
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 提取工单基本信息
    case_info = {}
    
    # 从表格中提取信息
    table = soup.find('table')
    if table:
        rows = table.find_all('tr')
        for row in rows:
            cells = row.find_all(['th', 'td'])
            if len(cells) >= 2:
                key = cells[0].get_text(strip=True).replace(':', '')
                value = cells[1].get_text(strip=True)
                case_info[key] = value
    
    # 构建CaseSummary对象
    case_summary = CaseSummary(
        id=5766,  # 从demo文件中的case ID
        case_number=case_info.get('Case Number', '01733361085'),
        subject=case_info.get('Subject', 'ddr phy 手册寄存器疑问汇总'),
        product_line=case_info.get('Product Line', 'DDR4/3'),
        product_name=case_info.get('Product Name', 'AKS DDR4/3 PHY SMIC12SFE'),
        project_name=case_info.get('Project Name', 'N0013'),
        cnum='PADFPA_AS_CoreProcess',  # 默认值
        product_code='S304-0',  # 默认值
        case_level=0,  # 默认值
        issue_type=1,  # Product Usage
        issue_description=case_info.get('Issue Description', ''),
        attach=None,
        priority=4,  # Showstopper+
        version=case_info.get('Version', 'v1.02a'),
        notify=None,
        status=3,  # Closed
        uid=260,  # 默认值
        deadline=None,
        is_admin_create=0,
        created_at=case_info.get('Create Time', '2024-12-05 09:11:25'),
        updated_at='2025-03-05 10:14:35',  # 最后一条评论时间
        company_name='Netforward',  # 从评论中推断
        username='wf w'  # 从评论中推断
    )
    
    return case_summary


def extract_comments_from_demo():
    """从demo-content.html提取评论数据"""
    try:
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
    except FileNotFoundError:
        logger.error("demo-content.html文件不存在")
        return []
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # 临时禁用增量更新以获取所有评论
    original_incremental = config.ENABLE_INCREMENTAL_UPDATE
    config.ENABLE_INCREMENTAL_UPDATE = False
    
    try:
        collector = CaseCollector()
        comments = collector._extract_comments(soup, 5766)
        return comments
    finally:
        config.ENABLE_INCREMENTAL_UPDATE = original_incremental


def extract_attachments_from_demo():
    """从demo-content.html提取附件数据"""
    try:
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
    except FileNotFoundError:
        logger.error("demo-content.html文件不存在")
        return []
    
    soup = BeautifulSoup(html_content, 'html.parser')
    
    collector = CaseCollector()
    attachments = collector._extract_attachments(soup, 5766)
    return attachments


def create_test_dataset():
    """创建完整的测试数据集"""
    print("=" * 60)
    print("从demo-content.html提取测试数据")
    print("=" * 60)
    
    # 提取工单概要
    print("1. 提取工单概要信息...")
    case_summary = extract_case_summary_from_demo()
    if not case_summary:
        print("❌ 工单概要提取失败")
        return None
    print(f"✅ 工单概要: {case_summary.case_number} - {case_summary.subject}")
    
    # 提取评论
    print("\n2. 提取评论信息...")
    comments = extract_comments_from_demo()
    print(f"✅ 提取到 {len(comments)} 条评论")
    for i, comment in enumerate(comments, 1):
        print(f"   评论{i}: {comment.source} - {comment.timestamp}")
    
    # 提取附件
    print("\n3. 提取附件信息...")
    attachments = extract_attachments_from_demo()
    print(f"✅ 提取到 {len(attachments)} 个附件")
    for i, attachment in enumerate(attachments, 1):
        print(f"   附件{i}: {attachment.file_name} ({attachment.file_size})")
    
    # 创建完整的工单详情
    case_detail = CaseDetail(
        summary=case_summary,
        comments=comments,
        attachments=attachments
    )
    
    return case_detail


def update_test_data_file(case_detail):
    """更新测试数据文件"""
    if not case_detail:
        return False
    
    # 创建测试数据目录
    test_data_dir = os.path.join("test_data")
    os.makedirs(test_data_dir, exist_ok=True)
    
    # 保存完整的工单数据
    case_data_file = os.path.join(test_data_dir, "demo_case_detail.json")
    case_dict = case_detail.to_dict()
    save_json(case_dict, case_data_file)
    print(f"✅ 完整工单数据已保存到: {case_data_file}")
    
    # 保存单独的评论数据
    comments_file = os.path.join(test_data_dir, "demo_comments.json")
    comments_data = [comment.__dict__ for comment in case_detail.comments]
    save_json(comments_data, comments_file)
    print(f"✅ 评论数据已保存到: {comments_file}")
    
    # 保存单独的附件数据
    attachments_file = os.path.join(test_data_dir, "demo_attachments.json")
    attachments_data = [attachment.__dict__ for attachment in case_detail.attachments]
    save_json(attachments_data, attachments_file)
    print(f"✅ 附件数据已保存到: {attachments_file}")
    
    # 保存工单概要数据
    summary_file = os.path.join(test_data_dir, "demo_case_summary.json")
    summary_data = case_detail.summary.__dict__
    save_json(summary_data, summary_file)
    print(f"✅ 工单概要已保存到: {summary_file}")
    
    return True


def generate_updated_test_code():
    """生成更新后的测试代码"""
    test_code = '''
    def test_comment_extraction_with_real_data(self):
        """使用真实数据测试评论提取"""
        # 加载真实的评论数据
        with open('test_data/demo_comments.json', 'r', encoding='utf-8') as f:
            expected_comments = json.load(f)
        
        # 加载demo HTML文件
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 临时禁用增量更新
        original_incremental = config.ENABLE_INCREMENTAL_UPDATE
        config.ENABLE_INCREMENTAL_UPDATE = False
        
        try:
            collector = CaseCollector()
            comments = collector._extract_comments(soup, 5766)
            
            # 验证评论数量
            self.assertEqual(len(comments), len(expected_comments))
            
            # 验证每条评论的内容
            for i, comment in enumerate(comments):
                expected = expected_comments[i]
                self.assertEqual(comment.source, expected['source'])
                self.assertEqual(comment.timestamp, expected['timestamp'])
                self.assertIn(expected['content'][:50], comment.content)
                
        finally:
            config.ENABLE_INCREMENTAL_UPDATE = original_incremental
    
    def test_attachment_extraction_with_real_data(self):
        """使用真实数据测试附件提取"""
        # 加载真实的附件数据
        with open('test_data/demo_attachments.json', 'r', encoding='utf-8') as f:
            expected_attachments = json.load(f)
        
        # 加载demo HTML文件
        with open('demo-content.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        soup = BeautifulSoup(html_content, 'html.parser')
        
        collector = CaseCollector()
        attachments = collector._extract_attachments(soup, 5766)
        
        # 验证附件数量
        self.assertEqual(len(attachments), len(expected_attachments))
        
        # 验证每个附件的信息
        for i, attachment in enumerate(attachments):
            expected = expected_attachments[i]
            self.assertEqual(attachment.file_name, expected['file_name'])
            self.assertEqual(attachment.file_size, expected['file_size'])
            self.assertEqual(attachment.owner, expected['owner'])
    '''
    
    # 保存测试代码到文件
    test_code_file = os.path.join("test_data", "updated_test_methods.py")
    with open(test_code_file, 'w', encoding='utf-8') as f:
        f.write(test_code)
    
    print(f"✅ 更新的测试代码已保存到: {test_code_file}")


def main():
    """主函数"""
    print("更新本地测试数据集")
    print("使用demo-content.html作为数据源")
    print()
    
    # 检查demo文件是否存在
    if not os.path.exists('demo-content.html'):
        print("❌ demo-content.html文件不存在")
        print("请确保文件在当前目录下")
        return
    
    # 创建测试数据集
    case_detail = create_test_dataset()
    
    if not case_detail:
        print("\n❌ 测试数据集创建失败")
        return
    
    print("\n" + "=" * 60)
    print("保存测试数据")
    print("=" * 60)
    
    # 更新测试数据文件
    if update_test_data_file(case_detail):
        print("\n" + "=" * 60)
        print("生成测试代码")
        print("=" * 60)
        
        # 生成更新的测试代码
        generate_updated_test_code()
        
        print("\n🎉 测试数据集更新完成！")
        print("\n📁 生成的文件:")
        print("   - test_data/demo_case_detail.json (完整工单数据)")
        print("   - test_data/demo_comments.json (评论数据)")
        print("   - test_data/demo_attachments.json (附件数据)")
        print("   - test_data/demo_case_summary.json (工单概要)")
        print("   - test_data/updated_test_methods.py (更新的测试方法)")
        
        print("\n📋 数据统计:")
        print(f"   - 工单ID: {case_detail.summary.id}")
        print(f"   - 工单号: {case_detail.summary.case_number}")
        print(f"   - 主题: {case_detail.summary.subject}")
        print(f"   - 评论数: {len(case_detail.comments)}")
        print(f"   - 附件数: {len(case_detail.attachments)}")
    else:
        print("\n❌ 测试数据保存失败")


if __name__ == "__main__":
    main()
