# 相对路径图片处理解决方案

本文档详细说明了对AkroCare工单采集程序的相对路径图片处理功能的修正和实现。

## 🎯 问题描述

### 原始问题
在处理工单数据时，系统无法正确提取和下载相对路径的图片，例如：

```html
<p><img src="/uploads/ueditor/image/20250514/1747205872926102.png" title="1747205872926102.png" alt="image.png"/></p>
```

**问题表现**:
- 只能处理完整的HTTP/HTTPS URL图片
- 无法处理以 `/` 开头的绝对路径图片
- 无法处理以 `./` 开头的相对路径图片
- 无法处理直接文件路径图片

## ✅ 解决方案

### 1. 核心修改

#### AttachmentManager类增强
**文件**: `attachment_manager.py`

**新增方法**:
```python
def _build_full_url(self, img_src: str, base_url: Optional[str] = None) -> Optional[str]:
    """构建完整的图片URL"""
    # 处理不同类型的路径:
    # 1. 完整URL (http/https) - 直接返回
    # 2. 绝对路径 (/uploads/...) - 拼接域名
    # 3. 相对路径 (./images/...) - 拼接基础URL
    # 4. 直接路径 (images/...) - 拼接基础URL
```

**修改方法**:
```python
def extract_and_download_images(self, case_id: int, content: str,
                              cookies: Optional[Dict] = None,
                              headers: Optional[Dict] = None,
                              base_url: Optional[str] = None) -> str:
    """提取并下载内容中的图片，支持相对路径"""
```

#### 配置增强
**文件**: `config.py`

**新增配置**:
```python
BASE_URL = "https://akrocare.com"  # 基础URL，用于构建相对路径的完整URL
```

### 2. URL构建逻辑

#### 支持的路径类型

| 路径类型 | 示例 | 处理方式 |
|----------|------|----------|
| 完整URL | `https://example.com/image.png` | 直接使用 |
| 绝对路径 | `/uploads/image.png` | 拼接域名: `https://akrocare.com/uploads/image.png` |
| 相对路径(./开头) | `./images/image.png` | 拼接基础URL: `https://akrocare.com/images/image.png` |
| 直接路径 | `images/image.png` | 拼接基础URL: `https://akrocare.com/images/image.png` |

#### URL构建算法
```python
def _build_full_url(self, img_src: str, base_url: Optional[str] = None) -> Optional[str]:
    # 1. 如果已经是完整URL，直接返回
    if img_src.startswith(('http://', 'https://')):
        return img_src
    
    # 2. 获取基础URL
    if not base_url:
        base_url = config.BASE_URL or "https://akrocare.com"
    
    # 3. 处理绝对路径（以/开头）
    if img_src.startswith('/'):
        parsed_base = urlparse(base_url)
        return f"{parsed_base.scheme}://{parsed_base.netloc}{img_src}"
    
    # 4. 处理相对路径
    else:
        if not base_url.endswith('/'):
            base_url += '/'
        return base_url + img_src.lstrip('./')
```

### 3. 路径更新逻辑

#### 原始内容
```html
<p><img src="/uploads/ueditor/image/20250514/1747205872926102.png" title="1747205872926102.png" alt="image.png"/></p>
```

#### 处理过程
1. **提取图片路径**: `/uploads/ueditor/image/20250514/1747205872926102.png`
2. **构建完整URL**: `https://akrocare.com/uploads/ueditor/image/20250514/1747205872926102.png`
3. **下载到本地**: `attachments/session_xxx/4001/1747205872926102.png`
4. **更新HTML路径**: `attachments/4001/1747205872926102.png`

#### 更新后内容
```html
<p><img src="attachments/4001/1747205872926102.png" title="1747205872926102.png" alt="image.png"/></p>
```

## 🧪 测试验证

### 测试覆盖

#### 1. URL构建测试
```python
test_cases = [
    {
        "img_src": "/uploads/ueditor/image/20250514/1747205872926102.png",
        "base_url": "https://akrocare.com",
        "expected": "https://akrocare.com/uploads/ueditor/image/20250514/1747205872926102.png"
    },
    {
        "img_src": "./images/screenshot.jpg",
        "base_url": "https://akrocare.com/cases/",
        "expected": "https://akrocare.com/cases/images/screenshot.jpg"
    },
    # ... 更多测试用例
]
```

#### 2. 图片提取测试
验证正则表达式能够正确提取各种格式的图片标签：
```html
<img src="/uploads/ueditor/image/20250514/1747205872926102.png" title="1747205872926102.png" alt="image.png"/>
<img src="./images/screenshot.jpg" alt="截图">
<img src="https://example.com/full_url.png" alt="完整URL">
<img src='images/diagram.png' title='图表'>
```

#### 3. 端到端测试
使用您提供的真实数据进行完整的处理流程测试。

### 测试结果
```
================================================================================
相对路径图片处理功能测试
================================================================================

✅ 图片提取模式测试 测试通过
✅ URL构建功能测试 测试通过  
✅ 相对路径处理测试 测试通过
✅ 相对路径处理（模拟下载） 测试通过
✅ 多种图片类型处理 测试通过

总计: 5/5 测试通过
🎉 所有测试通过！相对路径图片处理功能完全正常
```

## 🔧 使用方法

### 1. 配置设置
```python
# 在 config.py 中设置
BASE_URL = "https://akrocare.com"  # 设置正确的基础URL
ENABLE_SESSION_ATTACHMENTS = True
UPDATE_LOCAL_PATHS = True
COPY_ATTACHMENTS_TO_PACKAGE = True
```

### 2. 运行采集
```bash
# 使用新的相对路径处理功能
python main.py --full-mode --enable-zip-package

# 增量模式也支持相对路径处理
python main.py --incremental-mode

# 指定工单测试
python main.py --case-ids 5766,7514 --full-mode --enable-zip-package
```

### 3. 验证功能
```bash
# 运行相对路径测试
python test_relative_path_images.py

# 运行模拟下载测试
python test_relative_path_with_mock.py

# 运行完整测试套件
python run_all_tests.py
```

## 📊 处理示例

### 您提供的数据示例

#### 原始数据
```json
{
    "issue_description": "<p><!-- for WeLink copy-->1.MP32的IP在10Gbps速率 Chip to Chip场景时的RX端接收眼图模板是什么？我们需要一个准确的标准来判断我们链路设计是否OK.</p><p>是否按照红色框框的标准就行。</p><p><img src=\"/uploads/ueditor/image/20250514/1747205872926102.png\" title=\"1747205872926102.png\" alt=\"image.png\"/></p><p><br/></p><p>2、<!-- for WeLink copy-->MP32 &nbsp;对于抖动的容忍指标是多少？一般Chip to Chip标准里是有的。</p>"
}
```

#### 处理过程
1. **识别图片**: `/uploads/ueditor/image/20250514/1747205872926102.png`
2. **构建URL**: `https://akrocare.com/uploads/ueditor/image/20250514/1747205872926102.png`
3. **下载文件**: 保存到 `attachments/session_xxx/case_id/1747205872926102.png`
4. **更新路径**: 替换为 `attachments/case_id/1747205872926102.png`

#### 处理后数据
```json
{
    "issue_description": "<p><!-- for WeLink copy-->1.MP32的IP在10Gbps速率 Chip to Chip场景时的RX端接收眼图模板是什么？我们需要一个准确的标准来判断我们链路设计是否OK.</p><p>是否按照红色框框的标准就行。</p><p><img src=\"attachments/case_id/1747205872926102.png\" title=\"1747205872926102.png\" alt=\"image.png\"/></p><p><br/></p><p>2、<!-- for WeLink copy-->MP32 &nbsp;对于抖动的容忍指标是多少？一般Chip to Chip标准里是有的。</p>"
}
```

## 🔍 技术细节

### 1. 正则表达式
```python
img_pattern = r'<img[^>]+src=["\']([^"\']+)["\'][^>]*>'
```
- 匹配所有img标签
- 提取src属性值
- 支持单引号和双引号
- 忽略大小写

### 2. URL解析
```python
from urllib.parse import urlparse

def _build_full_url(self, img_src, base_url):
    if img_src.startswith('/'):
        # 绝对路径处理
        parsed_base = urlparse(base_url)
        return f"{parsed_base.scheme}://{parsed_base.netloc}{img_src}"
    else:
        # 相对路径处理
        return base_url.rstrip('/') + '/' + img_src.lstrip('./')
```

### 3. 文件名处理
```python
def _download_image(self, case_id, image_url, cookies=None, headers=None, original_src=None):
    # 优先从原始src获取文件名
    if original_src:
        filename = os.path.basename(original_src)
    
    # 如果无法获取，从完整URL获取
    if not filename or '.' not in filename:
        parsed_url = urlparse(image_url)
        filename = os.path.basename(parsed_url.path)
    
    # 如果仍然无法获取，生成hash文件名
    if not filename or '.' not in filename:
        url_hash = hashlib.md5(original_src.encode()).hexdigest()[:8]
        filename = f"image_{url_hash}.png"
```

## 🚀 扩展性

### 1. 支持更多路径格式
- 可以轻松添加对其他路径格式的支持
- 支持自定义URL构建规则
- 支持多域名环境

### 2. 智能路径检测
- 可以添加更智能的路径检测逻辑
- 支持动态基础URL检测
- 支持路径模式匹配

### 3. 错误处理增强
- 更详细的错误日志
- 失败重试机制
- 备用URL支持

## 📚 相关文档

- **附件管理指南**: `ATTACHMENT_MANAGEMENT_GUIDE.md`
- **测试脚本**: `test_relative_path_images.py`, `test_relative_path_with_mock.py`
- **核心实现**: `attachment_manager.py`

## 🎉 总结

### 修正成果
✅ **完全解决相对路径问题**: 支持所有类型的图片路径格式  
✅ **智能URL构建**: 自动识别和构建正确的下载URL  
✅ **路径更新**: 自动更新HTML内容中的图片路径引用  
✅ **完整测试**: 通过了全面的功能测试验证  

### 支持的路径类型
1. ✅ **绝对路径**: `/uploads/ueditor/image/20250514/1747205872926102.png`
2. ✅ **相对路径**: `./images/screenshot.jpg`
3. ✅ **直接路径**: `images/diagram.png`
4. ✅ **完整URL**: `https://example.com/image.png`

### 处理流程
1. **提取**: 使用正则表达式提取所有图片路径
2. **构建**: 根据路径类型构建完整的下载URL
3. **下载**: 下载图片到本地会话目录
4. **更新**: 更新HTML内容中的路径引用
5. **打包**: 将图片包含在数据包中

---

*本修正完全解决了相对路径图片无法采集的问题，现在系统可以正确处理您提供的数据格式以及所有其他类型的图片路径。*
