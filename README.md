# AkroCare工单采集程序

这是一个使用Python和Selenium开发的工单采集程序，用于从AkroCare系统中自动采集工单信息。

## 功能特性

### 🔄 核心采集功能
- **自动登录**: 支持自动登录AkroCare系统
- **工单列表采集**: 获取所有工单的概要信息
- **工单详情采集**: 获取每个工单的详细信息、评论和附件
- **附件下载**: 自动下载工单相关的附件和图片
- **增量更新**: 支持基于时间的增量采集，避免重复采集
- **数据存储**: 将采集的数据保存为JSON格式，附件按工单ID分文件夹存储

### 🎯 采集模式（新增）
- **指定工单ID**: 采集特定ID的工单，支持多个ID
- **最新N条模式**: 采集最新的N条工单（测试模式，不使用增量更新）
- **增量N条模式**: 采集离上次更新后的N条工单（使用增量更新）
- **全量采集**: 采集所有符合条件的工单（默认模式）
- **状态过滤**: 支持Open(4)/Closed(5)状态过滤，可同时采集两种状态

### 🛠️ 技术增强（新增）
- **双重鉴权**: requests + selenium自动降级机制
- **代理控制**: 可配置的代理禁用功能，提高连接稳定性
- **原始数据保存**: 可选的调试数据保存功能，支持打包和分别保存
- **智能目录创建**: 只有在存在附件或图片时才创建目录
- **产品线信息显示**: 在采集时显示工单的产品线和标题，增加分隔线提高可读性

### 📦 数据模式（新增）
- **全量模式**: 完整采集所有数据，支持ZIP打包，与Patch系统兼容
- **增量模式**: 基于现有数据进行增量采集，生成Patch文件
- **Patch管理**: 支持Patch解析、应用、合并和重新打包
- **版本管理**: 自动版本控制和链式管理

## 项目结构

```
selenium-aks/
├── main.py                    # 主程序入口
├── case_collector.py          # 核心采集器类
├── models.py                 # 数据模型定义
├── utils.py                  # 工具函数
├── config.py                 # 配置文件
├── raw_data_manager.py       # 原始数据管理器（新增）
├── data_mode_manager.py      # 数据模式管理器（新增）
├── patch_applier.py          # Patch应用脚本（新增）
├── test_collector.py         # 测试文件
├── test_new_features.py      # 新功能测试（新增）
├── test_data_modes.py        # 数据模式测试（新增）
├── demo_new_features.py      # 新功能演示（新增）
├── data_mode_examples.py     # 数据模式使用示例（新增）
├── update_test_dataset.py    # 测试数据集更新（新增）
├── validate_test_dataset.py  # 测试数据集验证（新增）
├── requirements.txt          # Python依赖包
├── README.md                # 说明文档
├── demo-content.html        # 演示HTML数据（新增）
├── data/                    # 数据存储目录
│   ├── cases_data.json          # 工单数据（标准模式）
│   ├── last_update.json         # 最后更新状态
│   ├── full_YYYYMMDD_HHMMSS/    # 全量模式目录输出（新增）
│   ├── full_YYYYMMDD_HHMMSS.zip # 全量模式ZIP输出（新增）
│   ├── patches/                 # 增量Patch目录（新增）
│   │   └── patch_YYYYMMDD_HHMMSS.zip
│   ├── applied/                 # Patch应用结果（新增）
│   │   ├── new_cases_*.json
│   │   ├── merged_*.json
│   │   └── repacked_*.zip
│   └── attachments/             # 附件存储目录
│       └── {case_id}/           # 按工单ID分组的附件
│           ├── images/          # 图片文件
│           └── ...              # 其他附件
├── test_data/               # 测试数据集（新增）
│   ├── demo_case_detail.json    # 完整工单数据
│   ├── demo_comments.json       # 评论数据
│   ├── demo_attachments.json    # 附件数据
│   ├── demo_case_summary.json   # 工单概要
│   └── README.md               # 测试数据说明
└── raw_data/                # 原始数据存储（可选）
    ├── raw_data_YYYYMMDD_HHMMSS.zip  # 打包的原始数据
    ├── cases_list/              # 工单列表响应
    ├── case_details/            # 工单详情响应
    ├── attachments/             # 附件信息
    └── html_pages/              # HTML页面
```

## 安装和配置

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置登录信息

编辑 `config.py` 文件，设置正确的登录凭据：

```python
# 登录凭据
EMAIL = "<EMAIL>"  # 替换为实际邮箱
PASSWORD = "your_password"        # 替换为实际密码
```

### 3. 其他配置选项

在 `config.py` 中还可以配置：

#### 基础配置
- `CHROME_DRIVER_PATH`: ChromeDriver可执行文件路径（默认None，自动下载）
- `API_REQUEST_METHOD`: API请求方式，'requests'或'selenium'（默认'requests'）
- `DISABLE_PROXY`: 是否禁用代理（默认True）
- `ENABLE_INCREMENTAL_UPDATE`: 是否启用增量更新（默认True）
- `PAGE_SIZE`: 每页获取的工单数量（默认30）
- `WEBDRIVER_TIMEOUT`: WebDriver超时时间（默认30秒）
- `MAX_RETRIES`: 最大重试次数（默认3次）

#### 采集模式配置（新增）
- `SPECIFIC_CASE_IDS`: 指定工单ID列表（默认[]，例如[5766, 7514]）
- `LATEST_N_CASES`: 限制采集最新的N条工单（默认0，不限制）
- `INCREMENTAL_N_CASES`: 限制采集离上次更新后的N条工单（默认0，不限制）

#### 工单状态配置（新增）
- `CASE_STATUS_OPEN`: Open状态码（默认4）
- `CASE_STATUS_CLOSED`: Closed状态码（默认5）
- `COLLECT_BOTH_STATUS`: 是否采集两种状态的工单（默认True）

#### 调试配置（新增）
- `SAVE_RAW_DATA`: 是否保存原始响应数据（默认False）
- `PACK_RAW_DATA`: 是否打包原始数据（默认True）
- `RAW_DATA_DIR`: 原始数据保存目录（默认"raw_data"）

#### ChromeDriver配置

如果您已经安装了ChromeDriver，可以指定其路径：

```python
# 在config.py中设置
CHROME_DRIVER_PATH = "C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe"
```

如果设置为`None`，程序将自动下载适合的ChromeDriver版本。

#### 代理配置

程序默认禁用代理以避免网络干扰：

```python
# 在config.py中设置
DISABLE_PROXY = True   # 禁用代理（推荐）
DISABLE_PROXY = False  # 启用代理
```

**为什么禁用代理？**
- 避免代理服务器干扰HTTP请求
- 提高请求成功率和稳定性
- 减少网络延迟和超时问题
- 确保直连目标服务器

**命令行控制：**
```bash
# 禁用代理
python main.py --disable-proxy

# 启用代理
python main.py --enable-proxy
```

#### API请求方式配置

程序支持两种方式获取工单数据：

**方式一：requests（推荐）**
```python
# 在config.py中设置
API_REQUEST_METHOD = 'requests'
```
- 使用requests库发送HTTP请求
- 性能更好，速度更快
- 自动处理登录后的cookies和headers

**方式二：selenium**
```python
# 在config.py中设置
API_REQUEST_METHOD = 'selenium'
```
- 直接在浏览器中执行JavaScript发送请求
- 完全模拟浏览器行为
- 在requests方式鉴权失败时的备选方案

**自动降级机制**：如果配置为requests方式但请求失败，程序会自动尝试使用selenium方式。

#### 鉴权机制详解

程序实现了完整的登录后鉴权信息传递机制：

**Cookie同步**：
- 自动获取Selenium登录后的所有cookies
- 实时同步cookies到requests session
- 支持domain、path、secure等cookie属性

**Header仿造**：
- 自动获取浏览器的User-Agent
- 设置完整的HTTP headers（Accept、Referer、Origin等）
- 自动提取和传递CSRF token
- 确保请求headers与浏览器一致

**实时更新**：
- 登录成功后立即更新session信息
- 导航到工单页面后再次更新
- 每次API请求前都会同步最新的cookies

## 使用方法

### 快速启动

**Windows用户:**
```bash
run.bat
```

**Linux/Mac用户:**
```bash
./run.sh
```

### 基本使用

```bash
python main.py
```

### 命令行参数

#### 基础参数
```bash
# 指定登录信息
python main.py --email <EMAIL> --password your_password

# 指定ChromeDriver路径
python main.py --driver-path "C:/Program Files (x86)/Google/Chrome/Application/chromedriver.exe"

# 指定API请求方式
python main.py --api-method selenium

# 代理控制
python main.py --disable-proxy  # 禁用代理（推荐）
python main.py --enable-proxy   # 启用代理

# 增量更新控制
python main.py --incremental         # 启用增量更新模式
python main.py --disable-incremental # 禁用增量更新模式
```

#### 采集模式参数（新增）
```bash
# 指定工单ID采集
python main.py --case-ids 5766,7514,8901

# 最新N条工单模式（测试模式）
python main.py --latest-n 10

# 增量N条工单模式
python main.py --incremental-n 5

# 工单状态过滤
python main.py --both-status    # 采集Open和Closed两种状态
python main.py --only-open     # 只采集Open状态
python main.py --only-closed   # 只采集Closed状态（默认）
```

#### 数据模式参数（新增）
```bash
# 全量模式
python main.py --full-mode                    # 启用全量模式
python main.py --full-mode --enable-zip-package  # 全量模式 + ZIP打包

# 增量模式
python main.py --incremental-mode             # 启用增量模式
python main.py --incremental-mode --base-version 20250524_142348  # 指定基础版本
```

#### 调试参数（新增）
```bash
# 原始数据保存
python main.py --save-raw-data           # 启用原始数据保存
python main.py --pack-raw-data           # 打包模式（推荐）
python main.py --separate-raw-data       # 分别保存模式
```

#### 组合使用示例
```bash
# 基础组合
python main.py --email <EMAIL> --password mypass --api-method requests --disable-proxy

# 指定工单采集 + 原始数据保存
python main.py --case-ids 5766,7514 --save-raw-data --pack-raw-data

# 最新10条工单 + 两种状态
python main.py --latest-n 10 --both-status --disable-proxy

# 增量5条工单 + 调试模式
python main.py --incremental-n 5 --save-raw-data --separate-raw-data

# 完整功能演示
python main.py --both-status --latest-n 20 --save-raw-data --pack-raw-data --api-method requests --disable-proxy

# 数据模式组合示例
python main.py --full-mode --both-status --latest-n 50 --enable-zip-package
python main.py --incremental-mode --case-ids 5766,7514 --base-version 20250524_142348
```

### Patch应用工具（新增）

#### 基础用法
```bash
# 查看Patch信息
python patch_applier.py --mode info --patches patch1.zip patch2.zip

# 应用到空目录（只提取新增工单）
python patch_applier.py --mode empty --patches patch1.zip patch2.zip --output new_cases.json

# 合并到现有数据
python patch_applier.py --mode merge --base-data full_20250524_142348.zip --patches patch1.zip patch2.zip --output merged.json

# 重新打包数据
python patch_applier.py --mode repack --patches merged.json --output repacked.zip
```

### 演示程序

#### 基础功能演示
```bash
python demo.py  # 基础数据结构和功能演示
```

#### 新功能演示（新增）
```bash
python demo_new_features.py  # 新增功能的详细演示
python data_mode_examples.py # 数据模式使用示例（新增）
```

#### 测试程序
```bash
python test_collector.py      # 运行完整测试套件
python test_new_features.py   # 测试新增功能
python test_data_modes.py     # 测试数据模式功能（新增）
```

#### 测试数据管理
```bash
python update_test_dataset.py    # 从demo-content.html更新测试数据
python validate_test_dataset.py  # 验证测试数据集完整性
```

### 程序执行流程

1. **登录验证**: 自动登录AkroCare系统
2. **导航到工单页面**: 点击Cases链接进入工单列表
3. **获取工单概要**: 通过API获取所有工单的基本信息
4. **采集工单详情**: 逐个访问工单详情页面
5. **提取评论信息**: 解析页面中的评论内容
6. **下载附件**: 下载工单相关的附件和图片
7. **保存数据**: 将采集的数据保存到本地

## 数据格式

### 工单概要信息

```json
{
  "id": 7514,
  "case_number": "01747205919",
  "subject": "MP01 指标说明",
  "product_line": "MP01",
  "product_name": "PHY NAME",
  "project_name": "N001",
  "status": 3,
  "created_at": "2025-05-14 14:58:39",
  "updated_at": "2025-05-15 15:31:15",
  "username": "wf w"
}
```

### 工单详细信息

```json
{
  "summary": { /* 工单概要信息 */ },
  "comments": [
    {
      "source": "用户名",
      "content": "评论内容",
      "timestamp": "2025-05-14 15:30:00"
    }
  ],
  "attachments": [
    {
      "attachment_id": "456",
      "file_name": "document.pdf",
      "file_size": "1.2 MB",
      "owner": "admin",
      "last_modified": "2025-05-14 16:00:00",
      "download_url": "https://...",
      "local_path": "/path/to/local/file",
      "version": 1
    }
  ]
}
```

## 增量更新机制

程序支持增量更新功能，可以避免重复采集已处理的数据：

- **工单级别**: 根据工单的更新时间判断是否需要重新采集
- **评论级别**: 根据评论时间判断是否为新评论
- **附件级别**: 根据附件的最后修改时间判断是否有更新

增量更新状态保存在 `data/last_update.json` 文件中。

## 测试

运行测试用例：

```bash
python test_collector.py
```

测试覆盖了以下功能：
- 数据模型创建和转换
- HTML内容解析
- 文件名安全处理
- JSON数据操作
- 采集器初始化

## 注意事项

1. **网络环境**: 确保能够正常访问AkroCare系统
2. **登录凭据**: 使用有效的登录邮箱和密码
3. **Chrome浏览器**: 程序会自动下载和配置ChromeDriver
4. **存储空间**: 确保有足够的磁盘空间存储附件
5. **访问频率**: 程序内置了请求间隔，避免对服务器造成过大压力

## 故障排除

### 常见问题

1. **登录失败**: 检查邮箱和密码是否正确
2. **页面加载超时**: 增加 `WEBDRIVER_TIMEOUT` 配置值
3. **附件下载失败**: 检查网络连接和存储权限
4. **Chrome驱动问题**: 确保Chrome浏览器已安装

### 日志文件

程序运行时会生成 `case_collector.log` 日志文件，包含详细的执行信息和错误信息。

## 许可证

本项目仅供学习和研究使用。
