#!/usr/bin/env python3
"""
综合测试套件
测试所有数据模式，包含图片、附件等完整功能
"""
import os
import sys
import json
import tempfile
import shutil
import zipfile
from datetime import datetime
from typing import List, Dict, Any, Tuple

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from test_data_generator import TestDataGenerator
from data_mode_manager import DataModeManager, get_data_mode_manager
from patch_applier import PatchApplier
from utils import logger, save_json, load_json


class ComprehensiveTestSuite:
    """综合测试套件"""
    
    def __init__(self):
        self.test_results = []
        self.temp_dirs = []
        self.original_data_dir = config.DATA_DIR
        
    def setup_test_environment(self) -> str:
        """设置测试环境"""
        temp_dir = tempfile.mkdtemp(prefix="comprehensive_test_")
        self.temp_dirs.append(temp_dir)
        config.DATA_DIR = temp_dir
        
        logger.info(f"测试环境设置完成: {temp_dir}")
        return temp_dir
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        config.DATA_DIR = self.original_data_dir
        
        for temp_dir in self.temp_dirs:
            try:
                shutil.rmtree(temp_dir, ignore_errors=True)
            except Exception as e:
                logger.warning(f"清理临时目录失败 {temp_dir}: {e}")
    
    def record_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        result = {
            "test_name": test_name,
            "success": success,
            "timestamp": datetime.now().isoformat(),
            "details": details
        }
        self.test_results.append(result)
        
        status = "✅ 通过" if success else "❌ 失败"
        logger.info(f"{test_name}: {status}")
        if details:
            logger.info(f"  详情: {details}")
    
    def test_data_generation(self) -> bool:
        """测试数据生成"""
        try:
            logger.info("开始测试数据生成...")
            
            generator = TestDataGenerator()
            base_cases, incremental_cases = generator.save_test_datasets()
            summary = generator.create_test_summary()
            
            # 验证生成的数据
            assert len(base_cases) == 5, f"基础数据集应该有5个工单，实际有{len(base_cases)}个"
            assert len(incremental_cases) == 5, f"增量数据集应该有5个工单，实际有{len(incremental_cases)}个"
            
            # 验证附件文件
            attachment_count = len(os.listdir(generator.attachments_dir))
            assert attachment_count > 0, "应该生成附件文件"
            
            # 验证图片文件
            image_files = [f for f in os.listdir(generator.attachments_dir) 
                          if f.endswith(('.png', '.jpg', '.jpeg', '.txt'))]
            assert len(image_files) > 0, "应该生成图片或图片占位文件"
            
            details = f"生成{len(base_cases)}个基础工单，{len(incremental_cases)}个增量工单，{attachment_count}个附件"
            self.record_test_result("数据生成测试", True, details)
            
            return True, generator
            
        except Exception as e:
            self.record_test_result("数据生成测试", False, str(e))
            return False, None
    
    def test_full_mode_with_attachments(self, test_cases: List) -> bool:
        """测试全量模式（包含附件）"""
        try:
            logger.info("开始测试全量模式...")
            
            test_env = self.setup_test_environment()
            data_manager = get_data_mode_manager()
            
            # 测试目录模式
            dir_path = data_manager.create_full_mode_package(test_cases, enable_zip=False)
            
            # 验证目录结构
            assert os.path.exists(dir_path), "全量模式目录应该存在"
            assert os.path.exists(os.path.join(dir_path, "metadata.json")), "应该有metadata.json"
            assert os.path.exists(os.path.join(dir_path, "patch_content.json")), "应该有patch_content.json"
            
            # 验证附件目录
            attachments_dir = os.path.join(dir_path, "attachments")
            if os.path.exists(attachments_dir):
                attachment_count = sum(len(files) for _, _, files in os.walk(attachments_dir))
                logger.info(f"全量模式包含{attachment_count}个附件文件")
            
            # 测试ZIP模式
            zip_path = data_manager.create_full_mode_package(test_cases, enable_zip=True)
            assert os.path.exists(zip_path), "全量模式ZIP文件应该存在"
            
            # 验证ZIP内容
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                zip_files = zipf.namelist()
                assert "metadata.json" in zip_files, "ZIP中应该有metadata.json"
                assert "patch_content.json" in zip_files, "ZIP中应该有patch_content.json"
                
                # 检查元数据
                metadata_content = zipf.read("metadata.json").decode('utf-8')
                metadata = json.loads(metadata_content)
                assert metadata["mode"] == "full", "模式应该是full"
                assert metadata["case_count"] == len(test_cases), f"工单数量应该是{len(test_cases)}"
            
            details = f"目录模式: {dir_path}, ZIP模式: {zip_path}"
            self.record_test_result("全量模式测试", True, details)
            return True
            
        except Exception as e:
            self.record_test_result("全量模式测试", False, str(e))
            return False
    
    def test_incremental_mode_with_changes(self, base_cases: List, incremental_cases: List) -> bool:
        """测试增量模式（包含变更检测）"""
        try:
            logger.info("开始测试增量模式...")
            
            test_env = self.setup_test_environment()
            data_manager = get_data_mode_manager()
            
            # 1. 先创建基础数据
            base_zip = data_manager.create_full_mode_package(base_cases, enable_zip=True)
            base_version = os.path.basename(base_zip).replace('full_', '').replace('.zip', '')
            
            # 2. 创建增量Patch
            patch_path = data_manager.create_incremental_patch(incremental_cases, base_version)
            
            assert os.path.exists(patch_path), "增量Patch文件应该存在"
            
            # 验证Patch内容
            with zipfile.ZipFile(patch_path, 'r') as zipf:
                metadata_content = zipf.read("metadata.json").decode('utf-8')
                metadata = json.loads(metadata_content)
                
                assert metadata["mode"] == "incremental", "模式应该是incremental"
                assert metadata["base_version"] == base_version, f"基础版本应该是{base_version}"
                assert len(metadata["new_cases"]) > 0, "应该有新增工单"
                
                # 验证变更检测
                new_cases = metadata["new_cases"]
                updated_cases = metadata["updated_cases"]
                
                logger.info(f"检测到{len(new_cases)}个新增工单: {new_cases}")
                logger.info(f"检测到{len(updated_cases)}个更新工单: {updated_cases}")
            
            details = f"基础版本: {base_version}, 新增: {len(metadata['new_cases'])}, 更新: {len(metadata['updated_cases'])}"
            self.record_test_result("增量模式测试", True, details)
            return True, patch_path
            
        except Exception as e:
            self.record_test_result("增量模式测试", False, str(e))
            return False, None
    
    def test_patch_application_modes(self, base_zip: str, patch_files: List[str]) -> bool:
        """测试Patch应用模式"""
        try:
            logger.info("开始测试Patch应用...")
            
            applier = PatchApplier()
            
            # 1. 测试Patch解析
            for patch_file in patch_files:
                patch_content = applier.parse_patch(patch_file)
                assert patch_content is not None, f"应该能解析Patch文件: {patch_file}"
                
                # 验证Patch信息显示
                logger.info(f"解析Patch: {patch_content.metadata.patch_id}")
                logger.info(f"  模式: {patch_content.metadata.mode}")
                logger.info(f"  新增工单: {len(patch_content.metadata.new_cases)}")
                logger.info(f"  更新工单: {len(patch_content.metadata.updated_cases)}")
            
            # 2. 测试空目录应用
            empty_output = os.path.join(config.DATA_DIR, "empty_result.json")
            success = applier.apply_patches_to_empty_directory(patch_files, empty_output)
            assert success, "空目录应用应该成功"
            assert os.path.exists(empty_output), "应该生成空目录应用结果文件"
            
            # 验证空目录应用结果
            with open(empty_output, 'r', encoding='utf-8') as f:
                empty_result = json.load(f)
                new_cases_count = len(empty_result['cases'])
                logger.info(f"空目录应用提取了{new_cases_count}个新增工单")
            
            # 3. 测试数据合并
            merge_output = os.path.join(config.DATA_DIR, "merge_result.json")
            success = applier.apply_patches_to_existing_data(base_zip, patch_files, merge_output)
            assert success, "数据合并应该成功"
            assert os.path.exists(merge_output), "应该生成合并结果文件"
            
            # 验证合并结果
            with open(merge_output, 'r', encoding='utf-8') as f:
                merge_result = json.load(f)
                total_cases = len(merge_result['cases'])
                logger.info(f"数据合并后共有{total_cases}个工单")
            
            # 4. 测试重新打包
            repack_output = os.path.join(config.DATA_DIR, "repacked.zip")
            success = applier.create_merged_patch(merge_output, repack_output)
            assert success, "重新打包应该成功"
            assert os.path.exists(repack_output), "应该生成重新打包文件"
            
            details = f"解析{len(patch_files)}个Patch, 空目录提取{new_cases_count}个工单, 合并后{total_cases}个工单"
            self.record_test_result("Patch应用测试", True, details)
            return True
            
        except Exception as e:
            self.record_test_result("Patch应用测试", False, str(e))
            return False
    
    def test_attachment_handling(self, test_cases: List) -> bool:
        """测试附件处理"""
        try:
            logger.info("开始测试附件处理...")
            
            test_env = self.setup_test_environment()
            data_manager = get_data_mode_manager()
            
            # 统计附件信息
            total_attachments = 0
            image_attachments = 0
            
            for case in test_cases:
                for attachment in case.attachments:
                    total_attachments += 1
                    if attachment.file_name.endswith(('.png', '.jpg', '.jpeg')):
                        image_attachments += 1
            
            logger.info(f"测试数据包含{total_attachments}个附件，其中{image_attachments}个图片")
            
            # 创建包含附件的数据包
            zip_path = data_manager.create_full_mode_package(test_cases, enable_zip=True)
            
            # 验证附件是否正确包含在ZIP中
            with zipfile.ZipFile(zip_path, 'r') as zipf:
                zip_files = zipf.namelist()
                attachment_files = [f for f in zip_files if f.startswith('attachments/')]
                
                logger.info(f"ZIP包中包含{len(attachment_files)}个附件文件")
                
                # 验证附件目录结构
                case_dirs = set()
                for att_file in attachment_files:
                    parts = att_file.split('/')
                    if len(parts) >= 3:  # attachments/case_id/filename
                        case_dirs.add(parts[1])
                
                logger.info(f"附件分布在{len(case_dirs)}个工单目录中")
            
            details = f"处理{total_attachments}个附件（{image_attachments}个图片），ZIP包含{len(attachment_files)}个文件"
            self.record_test_result("附件处理测试", True, details)
            return True
            
        except Exception as e:
            self.record_test_result("附件处理测试", False, str(e))
            return False
    
    def test_image_content_detection(self, test_cases: List) -> bool:
        """测试图片内容检测"""
        try:
            logger.info("开始测试图片内容检测...")
            
            # 统计包含图片的内容
            cases_with_images = 0
            comments_with_images = 0
            
            for case in test_cases:
                # 检查描述中的图片
                if '<img' in case.summary.issue_description:
                    cases_with_images += 1
                
                # 检查评论中的图片
                for comment in case.comments:
                    if '<img' in comment.content:
                        comments_with_images += 1
            
            logger.info(f"发现{cases_with_images}个工单描述包含图片")
            logger.info(f"发现{comments_with_images}条评论包含图片")
            
            # 验证图片检测逻辑
            assert cases_with_images > 0, "应该有工单包含图片内容"
            
            details = f"工单描述图片: {cases_with_images}, 评论图片: {comments_with_images}"
            self.record_test_result("图片内容检测测试", True, details)
            return True
            
        except Exception as e:
            self.record_test_result("图片内容检测测试", False, str(e))
            return False
    
    def run_comprehensive_tests(self) -> Dict[str, Any]:
        """运行综合测试"""
        logger.info("=" * 80)
        logger.info("开始运行综合测试套件")
        logger.info("=" * 80)
        
        try:
            # 1. 测试数据生成
            success, generator = self.test_data_generation()
            if not success:
                return self.generate_test_report()
            
            # 加载测试数据
            base_data_file = os.path.join(generator.test_data_dir, "base_dataset.json")
            incremental_data_file = os.path.join(generator.test_data_dir, "incremental_dataset.json")
            
            with open(base_data_file, 'r', encoding='utf-8') as f:
                base_data = json.load(f)
                base_cases = [self.dict_to_case_detail(case) for case in base_data['cases']]
            
            with open(incremental_data_file, 'r', encoding='utf-8') as f:
                incremental_data = json.load(f)
                incremental_cases = [self.dict_to_case_detail(case) for case in incremental_data['cases']]
            
            # 2. 测试附件处理
            self.test_attachment_handling(base_cases)
            
            # 3. 测试图片内容检测
            self.test_image_content_detection(base_cases)
            
            # 4. 测试全量模式
            self.test_full_mode_with_attachments(base_cases)
            
            # 5. 测试增量模式
            success, patch_path = self.test_incremental_mode_with_changes(base_cases, incremental_cases)
            if not success:
                return self.generate_test_report()
            
            # 6. 测试Patch应用
            # 首先创建基础ZIP用于测试
            test_env = self.setup_test_environment()
            data_manager = get_data_mode_manager()
            base_zip = data_manager.create_full_mode_package(base_cases, enable_zip=True)
            
            self.test_patch_application_modes(base_zip, [patch_path])
            
            return self.generate_test_report()
            
        except Exception as e:
            logger.error(f"综合测试执行失败: {e}")
            import traceback
            traceback.print_exc()
            self.record_test_result("综合测试执行", False, str(e))
            return self.generate_test_report()
        
        finally:
            self.cleanup_test_environment()
    
    def dict_to_case_detail(self, case_dict: Dict[str, Any]):
        """将字典转换为CaseDetail对象"""
        from models import CaseDetail, CaseSummary, CaseComment, CaseAttachment
        
        # 转换summary
        summary_dict = case_dict['summary']
        summary = CaseSummary(**summary_dict)
        
        # 转换comments
        comments = []
        for comment_dict in case_dict.get('comments', []):
            comment = CaseComment(**comment_dict)
            comments.append(comment)
        
        # 转换attachments
        attachments = []
        for attachment_dict in case_dict.get('attachments', []):
            attachment = CaseAttachment(**attachment_dict)
            attachments.append(attachment)
        
        return CaseDetail(summary=summary, comments=comments, attachments=attachments)
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed_tests": passed_tests,
                "failed_tests": failed_tests,
                "success_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                "execution_time": datetime.now().isoformat()
            },
            "test_results": self.test_results,
            "conclusion": "所有测试通过" if failed_tests == 0 else f"{failed_tests}个测试失败"
        }
        
        return report


def main():
    """主函数"""
    print("=" * 80)
    print("综合测试套件 - 全量模式和增量模式完整测试")
    print("=" * 80)
    
    suite = ComprehensiveTestSuite()
    
    try:
        # 运行综合测试
        report = suite.run_comprehensive_tests()
        
        # 显示测试结果
        print("\n" + "=" * 80)
        print("测试结果总结")
        print("=" * 80)
        
        summary = report["test_summary"]
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过测试: {summary['passed_tests']}")
        print(f"失败测试: {summary['failed_tests']}")
        print(f"成功率: {summary['success_rate']}")
        print(f"结论: {report['conclusion']}")
        
        print(f"\n详细结果:")
        for result in report["test_results"]:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test_name']}")
            if result["details"]:
                print(f"   {result['details']}")
        
        # 保存测试报告
        report_file = "comprehensive_test_report.json"
        save_json(report, report_file)
        print(f"\n📄 详细测试报告已保存: {report_file}")
        
        return summary['failed_tests'] == 0
        
    except Exception as e:
        print(f"❌ 测试套件执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
