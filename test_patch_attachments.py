#!/usr/bin/env python3
"""
测试Patch模式的附件打包和解包功能
验证修正后的逻辑是否正确工作
"""
import os
import sys
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
from utils import logger
from data_mode_manager import DataModeManager
from patch_applier import PatchApplier
from models import CaseDetail, CaseSummary, CaseComment, CaseAttachment
from attachment_manager import AttachmentManager


def create_test_case_with_attachments(case_id: int, title: str, product_line: str = "TEST") -> CaseDetail:
    """创建包含附件的测试工单"""

    current_time = datetime.now().isoformat()

    # 创建工单摘要
    summary = CaseSummary(
        id=case_id,
        case_number=f"CASE{case_id:06d}",
        subject=title,
        product_line=product_line,
        product_name="测试产品",
        project_name="测试项目",
        cnum="TEST001",
        product_code="TP01",
        case_level=1,
        issue_type=1,
        issue_description=f'<p>测试工单描述</p><p><img src="/uploads/ueditor/image/test_image_{case_id}.png" alt="测试图片"/></p>',
        attach=None,
        priority=2,
        version="1.0",
        notify=None,
        status=5,  # Closed
        uid=1001,
        deadline=None,
        is_admin_create=0,
        created_at=current_time,
        updated_at=current_time,
        company_name="测试公司",
        username="测试用户"
    )

    # 创建评论
    comments = [
        CaseComment(
            source="测试用户",
            content=f'<p>测试评论内容</p><p><img src="/uploads/ueditor/image/comment_image_{case_id}.png" alt="评论图片"/></p>',
            timestamp=current_time
        )
    ]

    # 创建附件
    attachments = [
        CaseAttachment(
            attachment_id=f"att_{case_id}_1",
            file_name=f"test_document_{case_id}.pdf",
            file_size="1.2 MB",
            owner="测试用户",
            last_modified=current_time,
            download_url=f"https://example.com/download/test_document_{case_id}.pdf",
            local_path=""  # 将在处理时设置
        )
    ]

    return CaseDetail(
        summary=summary,
        comments=comments,
        attachments=attachments
    )


def create_mock_attachment_files(attachment_manager: AttachmentManager, case_details: list):
    """创建模拟的附件文件"""
    created_files = []

    for case_detail in case_details:
        case_id = case_detail.summary.id
        case_dir = attachment_manager.get_case_attachments_dir(case_id)
        os.makedirs(case_dir, exist_ok=True)

        # 创建附件文件
        for attachment in case_detail.attachments:
            file_path = os.path.join(case_dir, attachment.file_name)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"模拟附件内容 - {attachment.file_name}")

            # 更新本地路径
            attachment.local_path = file_path
            created_files.append(file_path)
            logger.info(f"创建模拟附件: {file_path}")

        # 创建图片文件
        for img_name in [f"test_image_{case_id}.png", f"comment_image_{case_id}.png"]:
            img_path = os.path.join(case_dir, img_name)
            with open(img_path, 'wb') as f:
                f.write(b"MOCK_IMAGE_DATA")
            created_files.append(img_path)
            logger.info(f"创建模拟图片: {img_path}")

    return created_files


def test_incremental_patch_with_attachments():
    """测试增量模式的附件打包"""
    print("\n" + "="*80)
    print("测试增量模式附件打包")
    print("="*80)

    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置临时配置
            original_data_dir = config.DATA_DIR
            config.DATA_DIR = temp_dir
            config.COPY_ATTACHMENTS_TO_PACKAGE = True
            config.UPDATE_LOCAL_PATHS = True

            # 创建数据管理器
            data_manager = DataModeManager()

            # 创建测试工单
            test_cases = [
                create_test_case_with_attachments(6001, "增量测试工单A", "MP01"),
                create_test_case_with_attachments(6002, "增量测试工单B", "MP02"),
            ]

            # 创建模拟附件文件
            created_files = create_mock_attachment_files(data_manager.attachment_manager, test_cases)
            print(f"✅ 创建了 {len(created_files)} 个模拟文件")

            # 创建基础数据（空的）
            base_cases = []
            base_path = data_manager.create_full_mode_package(base_cases, enable_zip=True)
            print(f"✅ 基础数据包已创建: {base_path}")

            # 创建增量Patch
            patch_path = data_manager.create_incremental_patch(test_cases)
            print(f"✅ 增量Patch已创建: {patch_path}")

            # 验证Patch内容
            if os.path.exists(patch_path):
                import zipfile
                with zipfile.ZipFile(patch_path, 'r') as zipf:
                    file_list = zipf.namelist()
                    print(f"\n📦 Patch内容 ({len(file_list)} 个文件):")

                    # 检查必要文件
                    has_metadata = 'metadata.json' in file_list
                    has_content = 'patch_content.json' in file_list
                    attachment_files = [f for f in file_list if f.startswith('attachments/')]

                    print(f"   ✅ metadata.json: {'存在' if has_metadata else '❌ 缺失'}")
                    print(f"   ✅ patch_content.json: {'存在' if has_content else '❌ 缺失'}")
                    print(f"   📎 附件文件: {len(attachment_files)} 个")

                    for att_file in attachment_files[:5]:  # 只显示前5个
                        print(f"      - {att_file}")
                    if len(attachment_files) > 5:
                        print(f"      ... 还有 {len(attachment_files) - 5} 个文件")

                    if len(attachment_files) > 0:
                        print("✅ 增量Patch包含附件文件")
                    else:
                        print("❌ 增量Patch缺少附件文件")

            # 恢复原始配置
            config.DATA_DIR = original_data_dir

            return len(attachment_files) > 0

    except Exception as e:
        logger.error(f"增量模式测试失败: {e}")
        return False


def test_patch_extraction():
    """测试Patch解包功能"""
    print("\n" + "="*80)
    print("测试Patch解包功能")
    print("="*80)

    try:
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 设置临时配置
            original_data_dir = config.DATA_DIR
            config.DATA_DIR = temp_dir
            config.COPY_ATTACHMENTS_TO_PACKAGE = True

            # 创建数据管理器和应用器
            data_manager = DataModeManager()
            applier = PatchApplier()

            # 创建测试工单
            test_cases = [
                create_test_case_with_attachments(7001, "解包测试工单A", "MP01"),
                create_test_case_with_attachments(7002, "解包测试工单B", "MP02"),
            ]

            # 创建模拟附件文件
            created_files = create_mock_attachment_files(data_manager.attachment_manager, test_cases)
            print(f"✅ 创建了 {len(created_files)} 个模拟文件")

            # 创建全量数据包
            full_package_path = data_manager.create_full_mode_package(test_cases, enable_zip=True)
            print(f"✅ 全量数据包已创建: {full_package_path}")

            # 创建输出目录
            output_dir = os.path.join(temp_dir, "extracted")
            output_file = os.path.join(output_dir, "extracted_data.json")

            # 应用Patch到空目录
            success = applier.apply_patches_to_empty_directory([full_package_path], output_file)

            if success:
                print("✅ Patch应用成功")

                # 检查提取的附件
                attachments_dir = os.path.join(output_dir, 'attachments')
                if os.path.exists(attachments_dir):
                    extracted_files = []
                    for root, dirs, files in os.walk(attachments_dir):
                        for file in files:
                            extracted_files.append(os.path.join(root, file))

                    print(f"📎 提取的附件文件: {len(extracted_files)} 个")
                    for file_path in extracted_files[:5]:  # 只显示前5个
                        rel_path = os.path.relpath(file_path, output_dir)
                        print(f"   - {rel_path}")
                    if len(extracted_files) > 5:
                        print(f"   ... 还有 {len(extracted_files) - 5} 个文件")

                    # 检查数据文件中的路径更新
                    if os.path.exists(output_file):
                        from utils import load_json
                        data = load_json(output_file)
                        if data and 'cases' in data:
                            path_updated_count = 0
                            for case_id, case_data in data['cases'].items():
                                # 检查附件路径
                                attachments = case_data.get('attachments', [])
                                for attachment in attachments:
                                    if attachment.get('local_path', '').startswith('attachments/'):
                                        path_updated_count += 1

                                # 检查描述中的图片路径
                                desc = case_data.get('summary', {}).get('issue_description', '')
                                if 'attachments/' in desc:
                                    path_updated_count += 1

                            print(f"🔗 路径更新: {path_updated_count} 处")

                            if path_updated_count > 0:
                                print("✅ 路径更新成功")
                                return True
                            else:
                                print("⚠️ 路径未更新")
                                return False
                    else:
                        print("❌ 数据文件不存在")
                        return False
                else:
                    print("❌ 附件目录不存在")
                    return False
            else:
                print("❌ Patch应用失败")
                return False

            # 恢复原始配置
            config.DATA_DIR = original_data_dir

    except Exception as e:
        logger.error(f"解包测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Patch附件功能测试")
    print("测试修正后的打包和解包逻辑")

    # 设置测试环境
    config.ENABLE_INCREMENTAL_UPDATE = False

    test_results = []

    # 测试1: 增量模式附件打包
    print("\n🔧 测试1: 增量模式附件打包")
    result1 = test_incremental_patch_with_attachments()
    test_results.append(("增量模式附件打包", result1))

    # 测试2: Patch解包功能
    print("\n🔧 测试2: Patch解包功能")
    result2 = test_patch_extraction()
    test_results.append(("Patch解包功能", result2))

    # 输出测试结果
    print("\n" + "="*80)
    print("测试结果总结")
    print("="*80)

    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_count += 1

    print(f"\n总计: {passed_count}/{len(test_results)} 测试通过")

    if passed_count == len(test_results):
        print("🎉 所有测试通过！Patch附件功能修正成功！")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")


if __name__ == "__main__":
    main()
